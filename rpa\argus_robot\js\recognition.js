/* global bootstrap Dropzone $ ThumbmarkJS CKEDITOR Message JSONEditor jsonpath Message */

const {
	BalloonEditor,
    ClassicEditor,
    AccessibilityHelp,
    Autoformat,
    AutoLink,
    Autosave,
    Bold,
    BalloonToolbar,
    Code,
    CodeBlock,
    Essentials,
    Heading,
    Italic,
    Link,
    Markdown,
    Paragraph,
    PasteFromMarkdownExperimental,
    SelectAll,
    SourceEditing,
    Table,
    TableCaption,
    TableCellProperties,
    TableColumnResize,
    TableProperties,
    TableToolbar,
    TextTransformation,
    Undo
} = CKEDITOR;

    // 全局状态管理对象，包含所有需要在不同函数间共享的变量
    const AppState = {
        basePath: '',
        settings: {
            currentLanguage: localStorage.getItem('appState') ? JSON.parse(localStorage.getItem('appState')).currentLanguage : 'en',
            environment: localStorage.getItem('appState') ? JSON.parse(localStorage.getItem('appState')).environment : 'uat',
            operationMode: localStorage.getItem('appState') ? JSON.parse(localStorage.getItem('appState')).operationMode : 'auto',
            devEnv: 'http://localhost:2189',
            testEnv: 'https://copilot-test.pharmaronclinical.com',
            uatEnv: 'https://copilot-uat.pharmaronclinical.com',
            renderFormat: localStorage.getItem('appState') ? JSON.parse(localStorage.getItem('appState')).renderFormat : 'markdown',
            reportType: localStorage.getItem('appState') ? JSON.parse(localStorage.getItem('appState')).reportType : 'initial'
        },
        lastSwitchTime: 0,  // 上次切换语言的时间
        switchInterval: 5000,  // 语言切换的时间间隔限制
        eventDescriptionModal: null,
        eventDescription: '', // 新增，用于存储事件描述
        recognitionResults: '',
        isRecognitionModified: false, // 新增属性
        isStructuredDataModified: false, // 新增，用于标记结构化数据是否被修改
        isTransformedDataModified: false, // 新增，用于标记二次转换数据是否被修改
        insightData: {},
        finalStructuredData: {},
        finalTransformedData: {},
        languageToast: null,
        currentLanguageSpan: null,
        languageToggleIcon: null,
        configModal: null,
        pdfMd5: '',
        pdfTotalPage: 0,
        startTime: null,
        endTime: null,
        isLoading: false,  // 标记当前是否在加载
        hasFinishedLoading: false,  // 标记加载是否已经完成
        promptsData: {},
        modifiedPrompts: new Set(), // 新增，用于记录被修改的提示词
        lastActiveEditor: null,
        lastActiveTime: 0,
        isDataReviewModified: false, // 标记数据复核的数据是否被修改
        fieldMapping: {},            // 字段映射关系
        reverseFieldMapping: {},     // 逆向字段映射关系
        dataReviewData: {},           // 数据复核的数据,
        // 添加租户列表数据
        tenantList: [],
    };

    // 在 AppState 中添加模块和占位符的数据
    AppState.modulesPlaceholders = [
        {
            "module": "studyInfo",
            "placeholder": "studyData"
        },
        {
            "module": "dictInfo",
            "placeholder": [
                "{desc_reptd}",
                "{ind_reptd}",
                "{labtest}",
                "{pat_exposure}",
                "{pat_hist_rptd}",
                "{product_name}",
                "{TXT_cdr_admin_route_id}",
                "{TXT_cdr_dose_unit_id}",
                "{TXT_formulation_id}",
                "{TXT_freq_id}",
                "{TXT_labunit}"
            ]
        },
        {
            "module": "encodeCache",
            "placeholder": [
                "desc_reptd",
                "Ind_Table_ind_reptd",
                "labtest",
                "pat_exposure",
                "product_name",
                "rel_hist_table_pat_hist_rptd",
                "TXT_cdr_admin_route_id",
                "TXT_cdr_dose_unit_id",
                "TXT_formulation_id",
                "TXT_freq_id",
                "TXT_labunit_0"
            ]
        }
    ];

    // 添加模块和占位符的国际化映射
    AppState.i18n = {
        'module': {
            'studyInfo': '临床试验',
            'dictInfo': '字典配置',
            'encodeCache': '字典缓存'
        },
        'placeholder': {
            'studyData': '方案信息',
            'desc_reptd': 'Meddra编码-不良事件',
            'Ind_Table_ind_reptd': 'Meddra编码-药品适应症',
            'labtest': 'Meddra编码-实验室检查',
            'pat_exposure': '药品-试验用药品编码',
            'product_name': 'whoDrug编码',
            'rel_hist_table_pat_hist_rptd': 'Meddra编码-相关病史',
            'TXT_cdr_admin_route_id': '药品-给药途径',
            'TXT_cdr_dose_unit_id': '药品-剂量单位',
            'TXT_formulation_id': '药品-给药剂型',
            'TXT_freq_id': '药品-给药频率',
            'TXT_labunit_0': '实验室检查-检查结果单位',
            '{desc_reptd}': 'Meddra编码-不良事件',
            '{ind_reptd}': 'Meddra编码-药品适应症',
            '{labtest}': 'Meddra编码-实验室检查',
            '{pat_exposure}': '药品-试验用药品编码',
            '{pat_hist_rptd}': 'Meddra编码-相关病史',
            '{product_name}': 'whoDrug编码-合并用药品编码',
            '{TXT_cdr_admin_route_id}': '药品-给药途径',
            '{TXT_cdr_dose_unit_id}': '药品-剂量单位',
            '{TXT_formulation_id}': '药品-给药剂型',
            '{TXT_freq_id}': '药品-给药频率',
            '{TXT_labunit}': '实验室检查-检查结果单位'
        }
    };

    // 在 AppState 或其他合适的位置定义字段配置
    AppState.fieldData = [
        { key: "方案编号", value: "", type: "readonly", path: "datetime.protocal_number" },
        { key: "SAE报告术语", value: [], type: "readonly", path: "datetime.desc_reptd" },
        { key: "SAE发生日期(最早)", value: "", type: "required", path: "datetime.onset" },
        { key: "试验用药给药日期(最早)", value: "", type: "required", path: "datetime.start_datetime" },
        { key: "收到报告日期", value: "", type: "optional", path: "datetime.report_receive_date" },
        { key: "受试者入院日期", value: "", type: "optional", path: "datetime.admission_date" }
    ];

    /**
     * 判断当前是否为自动模式
     * @returns {boolean} - 如果是自动模式返回 true，否则返回 false
     */
    function isAutoMode() {
        return AppState.settings.operationMode === 'auto';
    }

    async function  loadTenant() {
        const res = await fetch('/kb-server/tenant/tenantList?search=&page=1&pageSize=10000');
        AppState.tenantList = (await res.json()).data.rows || [];
    }

    /**
     * 页面加载完成后执行的初始化操作
     */
    document.addEventListener('DOMContentLoaded', async () => {
        await loadTenant();
        // 请求浏览器通知权限
        requestNotificationPermission();
        // 处理租户信息
        const tenantInfo = getTenantInfo();

        // 初始化模态框
        initializeModals();
        // 绑定事件监听器
        bindEvents();
        // 在页面加载时填充租户选择列表
        populateTenantSelect();
        // 加载环境配置
        loadEnvironmentSettings();
        // 初始化并切换环境
        updateEnvironment(true);
        // 设置按钮可见性
        updateButtonVisibility();
        // 更新用户角色显示
        updateUserRoleDisplay();

        // 更新租户信息显示
        document.getElementById('tenantName').innerText = tenantInfo.tenantName;
        document.getElementById('tenantId').innerText = tenantInfo.tenantId;
        document.getElementById('userName').innerText = tenantInfo.userName;
        document.getElementById('studyNum').innerText = tenantInfo.studyNum === 'undefined' ? '' : tenantInfo.studyNum;

        // 更新环境信息显示
        document.getElementById('tenantEnv').innerText = AppState.settings.environment;
        document.getElementById('languageEnv').innerText = AppState.settings.currentLanguage;

        ThumbmarkJS.setOption('exclude', ['canvas', 'permissions', 'screen.mediaMatches', 'plugins', 'system.useragent', 'system.browser.name', 'system.browser.version', 'hardware.videocard.vendorUnmasked', 'hardware.videocard.rendererUnmasked', 'fonts'])

        // 获取设备指纹作为 userId
        ThumbmarkJS.getFingerprint({ includeData: true }).then(function(fp){

            console.info(JSON.stringify(fp))

            AppState.userId = fp.hash;
            document.getElementById('userId').innerText = AppState.userId; // 显示新的 userId
        });

        // 监听 localStorage 的变化
        window.addEventListener('localStorageModified', checkStorageConsistency);

        const labelPanel = document.getElementById('global-label-panel');
        const labelPanelToggle = document.getElementById('label-panel-toggle');

        // 折叠和展开功能
        labelPanelToggle.addEventListener('click', () => {
            console.info('折叠')
            labelPanel.classList.toggle('collapsed');
            labelPanelToggle.classList.toggle('collapsed');
        });

        // 选项卡切换
        const tabItems = labelPanel.querySelectorAll('.tab-item');
        const tabPanes = labelPanel.querySelectorAll('.tab-pane');

        tabItems.forEach(tabItem => {
            tabItem.addEventListener('click', () => {
                // 移除所有选项卡和内容的激活状态
                tabItems.forEach(item => item.classList.remove('active'));
                tabPanes.forEach(pane => pane.classList.remove('active'));

                // 激活选中的选项卡和内容
                tabItem.classList.add('active');
                const paneId = tabItem.getAttribute('data-tab');
                const activePane = labelPanel.querySelector(`#${paneId}`);
                activePane.classList.add('active');
            });
        });

        // 标签按钮功能
        const labelButtons = labelPanel.querySelectorAll('.label-button');
        labelButtons.forEach(button => {
            button.addEventListener('click', () => {
                const labelText = button.textContent;

                // 获取最后一个活动的编辑器实例
                const editorInstance = getLastActiveEditorInstance();
                if (editorInstance) {
                    // 在光标位置插入标签
                    editorInstance.model.change(writer => {
                        const insertPosition = editorInstance.model.document.selection.getFirstPosition();
                        writer.insertText(labelText, insertPosition);
                    });
                    // 重新聚焦编辑器
                    editorInstance.editing.view.focus();
                } else {
                    Message.warning('未找到活动的编辑器，请先点击需要插入标签的编辑器区域。');
                }
            });
        });

        renderKeyValueData(AppState.fieldData);

        // 获取最后一个活动的编辑器实例
        function getLastActiveEditorInstance() {
            const now = Date.now();
            const MAX_IDLE_TIME = 5 * 60 * 1000; // 5 分钟

            if (AppState.lastActiveEditor && (now - AppState.lastActiveTime) < MAX_IDLE_TIME) {
                return AppState.lastActiveEditor;
            } else {
                return null;
            }
        }

        // 在初始化时调用一次
        populateModuleSelect();

        // 添加事件监听器，当模块改变时更新占位符选择框（只绑定一次）
        const moduleSelect = document.getElementById('moduleSelect');
        moduleSelect.addEventListener('change', () => {
            populatePlaceholderSelect();
        });

        // 添加事件监听器，当占位符改变时获取新的数据（只绑定一次）
        const placeholderSelect = document.getElementById('placeholderSelect');
        placeholderSelect.addEventListener('change', () => {
            fetchDrugDictionaryData();
        });

        document.querySelector('#gotoInsightView').addEventListener('click', function (e) {
            window.open(`/pv-manus-front/pv-extraction?key=${AppState.pdfMd5}_insightView`)
        },false);

        document.querySelector('#imageResults-tab i.fas').addEventListener('click', function (e) {
            e.stopPropagation();
            e.preventDefault();
            if(confirm('是否清空缓存的结果？')){
                localStorage.removeItem(`${AppState.pdfMd5}_imageRecognition`);
                AppState.recognitionResults = '';
                const event = new MouseEvent('click');
                document.getElementById('exportButton').dispatchEvent(event);
            }
        },false);

        document.querySelector('#insightResults-tab i.fas').addEventListener('click', function (e) {
            e.stopPropagation();
            e.preventDefault();
            if(confirm('是否清空缓存的结果？')){
                localStorage.removeItem(`${AppState.pdfMd5}_insightView`);
                AppState.insightData = {}; 
                const event = new MouseEvent('click');
                document.getElementById('exportButton').dispatchEvent(event);                
                document.querySelector('#gotoInsightView').style.display = 'none';
            }
        },false);

        document.querySelector('#narrativeResults-tab i.fas').addEventListener('click', function (e) {
            e.stopPropagation();
            e.preventDefault();
            if(confirm('是否清空缓存的结果？')){
                localStorage.removeItem(`${AppState.pdfMd5}_eventDescription`);
                AppState.eventDescription = '';
                const event = new MouseEvent('click');
                document.getElementById('exportButton').dispatchEvent(event);
            }
        },false);

        document.querySelector('#structuredDataResults-tab i.fas').addEventListener('click', function (e) {
            e.stopPropagation();
            e.preventDefault();
            if(confirm('是否清空缓存的结果？')){
                localStorage.removeItem(`${AppState.pdfMd5}_structuredData`);
                AppState.finalStructuredData = {};
                const event = new MouseEvent('click');
                document.getElementById('exportButton').dispatchEvent(event);
            }
        },false);

        document.querySelector('#transformedDataResults-tab i.fas').addEventListener('click', function (e) {
            e.stopPropagation();
            e.preventDefault();
            if(confirm('是否清空缓存的结果？')){
                localStorage.removeItem(`${AppState.pdfMd5}_transformedData`);
                AppState.finalTransformedData = {};
                const event = new MouseEvent('click');
                document.getElementById('exportButton').dispatchEvent(event);
            }
        },false);
    });

    /**
     * 请求浏览器通知权限
     */
    function requestNotificationPermission() {

        if (Notification.permission == 'denied') {
            Message.warning('请开启浏览器的通知功能')
        }

        if (Notification.permission !== 'granted' && Notification.permission !== 'denied') {
            Notification.requestPermission();
        }
    }

    /**
     * 显示浏览器通知
     * @param {string} title - 通知标题
     * @param {string} content - 通知正文
     */
    function showNotification(title, content) {
        if (Notification.permission === 'granted') {
            // 获取当前时间并格式化
            const now = new Date();
            const timeString = now.toLocaleString(); // 显示完整的日期和时间

            // 构建包含时间的内容，将时间放在第一行
            const bodyContent = content || document.getElementById('fileName').textContent;
            const notificationContent = `${timeString}\n${bodyContent}`;

            new Notification(title, {
                body: notificationContent,
                icon: 'https://www.pharmaron.cn/images/logo.png' // 可替换为网站图标
            });
        }
    }

    /**
     * 填充租户选择列表
     */
    function populateTenantSelect() {
        const tenantSelect = document.getElementById('tenantSelect'); 
        tenantSelect.innerHTML = ''; // 清空现有选项

        AppState.tenantList.forEach(tenant => {
            const option = document.createElement('option');
            option.value = tenant.tenantId;
            option.textContent = tenant.tenantName;
            tenantSelect.appendChild(option);
        });

        // 设置默认选中的租户
        tenantSelect.value = AppState.tenantId || localStorage.getItem('tenantId') || AppState.tenantList[0].tenantId;
    }

    /**
     * 初始化模态框
     */
    function initializeModals() {
        AppState.eventDescriptionModal = new bootstrap.Modal(document.getElementById('eventDescriptionModal'));
        AppState.languageToast = new bootstrap.Toast(document.getElementById('languageToast'));
        AppState.configModal = new bootstrap.Modal(document.getElementById('configModal'));
        AppState.currentLanguageSpan = document.getElementById('currentLanguage');
        AppState.languageToggleIcon = document.getElementById('languageToggle');

        // 初始化语言切换图标的显示内容
        updateLanguageToggleIcon();
    }

    /**
     * 绑定所有按钮和控件的事件监听器
     */
    function bindEvents() {
        // 绑定语言切换按钮点击事件
        AppState.languageToggleIcon.addEventListener('click', async () => {
            await switchLanguage();
            AppState.languageToast.show();  // 显示语言切换提示
        });

        // 初始化文件上传处理
        initializeFileUpload();

        // 事件描述更新按钮点击处理
        document.getElementById('updateDescriptionBtn').addEventListener('click', function () {
            const updateDescriptionBtn = document.getElementById('updateDescriptionBtn');
            // const eventDescriptionContent = document.getElementById('eventDescriptionContent');
            const eventDescriptionTextarea = document.getElementById('eventDescriptionTextarea');
            let originalMarkdown = eventDescriptionTextarea.value;

            if (updateDescriptionBtn.textContent === '更新') {
                eventDescriptionTextarea.value = originalMarkdown;
                // eventDescriptionContent.classList.add('hidden');
                eventDescriptionTextarea.classList.remove('hidden');
                updateDescriptionBtn.textContent = '保存';
            } else {
                const updatedContent = eventDescriptionTextarea.value;
                // 转义星号防止Markdown解析
                // eventDescriptionContent.innerHTML = marked.parse(updatedContent.replace(/\*/g, '\\*'));
                // eventDescriptionContent.classList.remove('hidden');
                eventDescriptionTextarea.classList.add('hidden');
                updateDescriptionBtn.textContent = '更新';

                const cacheKey = `${AppState.pdfMd5}_eventDescription`;
                localStorage.setItem(cacheKey, JSON.stringify({description: updatedContent}));
            }
        });

        // 获取按钮元素
        const generateInsightButton = document.getElementById('generateInsightView');
        const generateEventDescriptionButton = document.getElementById('generateEventDescription');
        const regenerateDescriptionBtn = document.getElementById('regenerateDescriptionBtn');
        const generateStructuredDataButton = document.getElementById('generateStructuredData');
        const secondaryTransformButton = document.getElementById('secondaryTransform');

        // 绑定点击事件，使用通用的防重复点击函数
        bindButtonWithSteps(generateInsightButton, async function() {
            await generateInsightView();
        }, ['imageRecognition']);

        // 绑定生成事件描述按钮
        bindButtonWithSteps(generateEventDescriptionButton, async function() {
            await generateEventDescription();
        }, ['insightGeneration']);

        // 绑定重新生成事件描述按钮
        bindButtonWithSteps(regenerateDescriptionBtn, async function() {
            await generateEventDescription(true);
        }, ['insightGeneration']);

        // 绑定生成结构化数据按钮
        bindButtonWithSteps(generateStructuredDataButton, async function() {
            await generateStructuredData();
        }, ['eventDescription']);

        // 绑定二次转换按钮
        bindButtonWithSteps(secondaryTransformButton, async function() {
            await secondaryTransformData();
        }, ['structuredData']);

        // 绑定配置按钮点击事件
        document.getElementById('configButton').addEventListener('click', function () {
            // 获取提示词数据
            fetchPromptsData();
            // 初始化模块和占位符选择框
            populateModuleSelect();
            // 显示配置模态框
            AppState.configModal.show();
        });

        // 定义获取提示词数据的函数
        async function fetchPromptsData() {
            try {
                const response = await fetch(`${AppState.basePath}/api/pv/prompt/${AppState.tenantId}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                const responseData = await response.json();
                if (responseData.status_code !== 200) {
                    throw new Error(`Failed to fetch prompts: ${responseData.message}`);
                }
                 // data 应该包含 { "cn": {...}, "en": {...} }
                // 将提示词数据存储在 AppState 中
                AppState.promptsData = responseData.data;
                // 在弹框中显示提示词数据
                displayPromptsData();
            } catch (error) {
                console.error('Error fetching prompts data:', error);
                showError('无法获取提示词数据。');
            }
        }

        // 修改后的显示提示词数据的函数
        function displayPromptsData() {
            const promptsNavigation = document.querySelector('#promptsConfig .prompts-navigation');
            const promptsContent = document.querySelector('#promptsConfig .prompts-content');
            promptsNavigation.innerHTML = ''; // 清空导航
            promptsContent.innerHTML = ''; // 清空内容

            const promptsData = AppState.promptsData; // 从AppState中获取提示词数据

            // 收集所有提示词的键
            let allPromptKeys = new Set();
            for (const lang in promptsData) {
                const languagePrompts = promptsData[lang];
                for (const promptKey in languagePrompts) {
                    allPromptKeys.add(promptKey);
                }
            }

            // 转换为数组并排序
            allPromptKeys = Array.from(allPromptKeys).sort();

            // 维护一个全局变量来保存所有的提示词，用于点击导航时显示
            AppState.allPrompts = {};

            // 按照模块对提示词进行分组
            const moduleGroups = {}; // { moduleName: [promptKey1, promptKey2, ...] }
            for (const promptKey of allPromptKeys) {
                const moduleName = promptKey.split('_')[0]; // 获取模块名称
                if (!moduleGroups[moduleName]) {
                    moduleGroups[moduleName] = [];
                }
                moduleGroups[moduleName].push(promptKey);

                // 保存提示词数据
                AppState.allPrompts[promptKey] = {
                    cn: promptsData['cn'] && promptsData['cn'][promptKey] || '',
                    en: promptsData['en'] && promptsData['en'][promptKey] || ''
                };
            }

            // 创建导航列表
            const promptListContainer = document.createElement('div');

            for (const moduleName in moduleGroups) {
                const moduleTitle = document.createElement('div');
                moduleTitle.classList.add('nav-module-title');
                moduleTitle.textContent = moduleName;
                promptListContainer.appendChild(moduleTitle);

                const promptList = document.createElement('ul');
                promptList.classList.add('nav-prompt-list');

                for (const promptKey of moduleGroups[moduleName]) {
                    const promptItem = document.createElement('li');
                    promptItem.classList.add('nav-prompt-item');
                    promptItem.textContent = promptKey;
                    promptItem.dataset.promptKey = promptKey;
                    promptItem.dataset.fullTitle = promptKey; // 用于悬浮提示显示完整标题

                    promptItem.addEventListener('click', function () {
                        // 点击导航项时，显示对应的提示词内容
                        displayPromptContent(promptKey);
                        // 更新导航项的选中状态
                        document.querySelectorAll('.nav-prompt-item').forEach(item => item.classList.remove('active'));
                        promptItem.classList.add('active');
                    });

                    promptList.appendChild(promptItem);
                }

                promptListContainer.appendChild(promptList);
            }

            promptsNavigation.appendChild(promptListContainer);

            // 默认显示第一个提示词内容
            const firstPromptKey = allPromptKeys[0];
            if (firstPromptKey) {
                displayPromptContent(firstPromptKey);
                // 设置第一个导航项为选中状态
                const firstNavItem = promptsNavigation.querySelector(`[data-prompt-key="${firstPromptKey}"]`);
                if (firstNavItem) {
                    firstNavItem.classList.add('active');
                }
            }
        }

        // 修改后的显示提示词内容的函数
        function displayPromptContent(promptKey) {
            const promptsContent = document.querySelector('#promptsConfig .prompts-content');
            promptsContent.innerHTML = ''; // 清空内容

            const promptData = AppState.allPrompts[promptKey];
            if (promptData) {
                const { cn, en } = promptData;

                const promptDiv = document.createElement('div');
                promptDiv.classList.add('prompt-item');

                promptDiv.innerHTML = `
                <h4>${promptKey}</h4>
                <div class="row">
                    <div class="col-6">
                        <label>Chinese</label>
                        <textarea id="${promptKey}_cn" class="form-control prompt-textarea" rows="15">${cn}</textarea>
                    </div>
                    <div class="col-6">
                        <label>English</label>
                        <textarea id="${promptKey}_en" class="form-control prompt-textarea" rows="15">${en}</textarea>
                    </div>
                </div>
            `;
                promptsContent.appendChild(promptDiv);

                // 保存初始值到 promptData 中
                promptData.initialCnValue = cn;
                promptData.initialEnValue = en;

                // 当文本区域内容变化时，更新全局数据并记录修改
                const cnTextarea = promptDiv.querySelector(`textarea[id="${promptKey}_cn"]`);
                cnTextarea.addEventListener('input', function() {
                    promptData.cn = cnTextarea.value;

                    // 比较当前值和初始值
                    if (cnTextarea.value !== promptData.initialCnValue) {
                        AppState.modifiedPrompts.add(`${promptKey}_cn`);
                    } else {
                        AppState.modifiedPrompts.delete(`${promptKey}_cn`);
                    }
                });

                const enTextarea = promptDiv.querySelector(`textarea[id="${promptKey}_en"]`);
                enTextarea.addEventListener('input', function() {
                    promptData.en = enTextarea.value;

                    // 比较当前值和初始值
                    if (enTextarea.value !== promptData.initialEnValue) {
                        AppState.modifiedPrompts.add(`${promptKey}_en`);
                    } else {
                        AppState.modifiedPrompts.delete(`${promptKey}_en`);
                    }
                });
            }
        }

        // 修改后的收集并保存提示词数据的函数
        async function collectAndSavePromptsData() {
            // 从 AppState.allPrompts 中收集被修改的提示词数据
            const modifiedPromptsData = { 'cn': {}, 'en': {} };
            for (const promptKey in AppState.allPrompts) {
                const promptData = AppState.allPrompts[promptKey];

                if (AppState.modifiedPrompts.has(`${promptKey}_cn`)) {
                    modifiedPromptsData['cn'][promptKey] = promptData.cn;
                }
                if (AppState.modifiedPrompts.has(`${promptKey}_en`)) {
                    modifiedPromptsData['en'][promptKey] = promptData.en;
                }
            }

            // 判断是否有被修改的提示词
            const hasCnUpdates = Object.keys(modifiedPromptsData['cn']).length > 0;
            const hasEnUpdates = Object.keys(modifiedPromptsData['en']).length > 0;

            if (!hasCnUpdates && !hasEnUpdates) {
                Message.warning('数据未修改，无需更新。');
                return;
            }

            // 将被修改的提示词数据发送到后台保存
            await savePromptsData(modifiedPromptsData);
            Message.success('提示词数据已更新成功！');
        }

        // 保存提示词数据的函数
        async function savePromptsData(promptsData) {
            try {
                // 遍历每种语言
                for (const language in promptsData) {
                    const languagePrompts = promptsData[language];
                    // 遍历每个被修改的提示词模板
                    for (const template_key in languagePrompts) {
                        const content = languagePrompts[template_key];
                        // 发送 PUT 请求更新提示词
                        const response = await fetch(`${AppState.basePath}/api/pv/prompt/${AppState.tenantId}/${template_key}`, {
                            method: 'PUT',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                content: content,
                                language: language,
                                tenantId: AppState.tenantId,
                                tenantEnv: AppState.tenantEnv,
                                studyNum: AppState.studyNum,
                                userId: AppState.userId,
                                userName: AppState.userName,
                            })
                        });
                        if (!response.ok) {
                            const errorData = await response.json();
                            throw new Error(`Failed to save prompt ${template_key}: ${errorData.detail || response.statusText}`);
                        }
                    }
                }
                console.log('Prompts saved successfully');

                // 在保存成功后，更新已修改的提示词的初始值
                for (const promptKey in AppState.allPrompts) {
                    const promptData = AppState.allPrompts[promptKey];
                    if (AppState.modifiedPrompts.has(`${promptKey}_cn`)) {
                        promptData.initialCnValue = promptData.cn;
                    }
                    if (AppState.modifiedPrompts.has(`${promptKey}_en`)) {
                        promptData.initialEnValue = promptData.en;
                    }
                }

                // 清空修改过的提示词集合
                AppState.modifiedPrompts.clear();
            } catch (error) {
                console.error('Error saving prompts data:', error);
                showError('无法保存提示词数据。');
                throw error; // 继续抛出错误，供上层捕获
            }
        }

        // 获取提示词更新按钮
        const updatePromptsBtn = document.getElementById('updatePromptsBtn');

        // 绑定提示词更新按钮的点击事件
        updatePromptsBtn.addEventListener('click', async function () {
            const originalText = updatePromptsBtn.textContent;
            updatePromptsBtn.disabled = true;
            updatePromptsBtn.textContent = '保存中...';

            try {
                // 保存提示词数据
                await collectAndSavePromptsData();
            } catch (error) {
                console.error('Error saving prompts data:', error);
                showError('提示词更新失败，请重试。');
            } finally {
                updatePromptsBtn.disabled = false;
                updatePromptsBtn.textContent = originalText;
            }
        });

        // 在操作模式更新时调用
        document.getElementById('updateOperationModeBtn').addEventListener('click', function () {
            AppState.settings.operationMode = document.getElementById('operationModeSwitch').value;
            AppState.settings.renderFormat = document.getElementById('resultRenderFormat').value;
            AppState.settings.reportType = document.getElementById('reportTypeSelect').value;
            localStorage.setItem('appState', JSON.stringify(AppState.settings));

            // 更新按钮可见性
            updateButtonVisibility();

            // 更新用户角色显示
            updateUserRoleDisplay();

            Message.success('操作模式已更新');
        });

        // 绑定重置按钮点击事件
        document.getElementById('resetButton').addEventListener('click', function () {
            if(!confirm('是否要清除缓存信息？')){
                return;
            }
            localStorage.clear();
            sessionStorage.clear();
            Message.success('LocalStorage data has been cleared!');
            // 重新加载页面以重置状态
            location.reload();
        });

        // 绑定环境更新按钮的点击事件
        document.getElementById('updateEnvironmentBtn').addEventListener('click', function () {
            updateEnvironment(false);
            AppState.configModal.hide();
            const params = new URLSearchParams(location.search);
            if(params.has('tenantId') && AppState.tenantId != params.get('tenantId')){
                const url = new URL(location.href);
                url.searchParams.set('tenantId',AppState.tenantId);
                url.searchParams.set('tenantName',AppState.tenantName);
                location.href = url
            }else{
                location.reload();
            }
            
        });
        let exportModal = null;
        // 绑定导出按钮点击事件
        document.getElementById('exportButton').addEventListener('click', function () {
            // 获取缓存中的图像识别结果
            const cacheKey = `${AppState.pdfMd5}_imageRecognition`;
            const recognitionResults = JSON.parse(localStorage.getItem(cacheKey) || '[]');

            let recognitionResultsText = 'No data available';
            if (recognitionResults.length > 0) {
                recognitionResultsText = recognitionResults.map(
                    (result, index) => `Page ${index + 1}:\n${result.content}\n`).join('\n');
            }

            // 获取缓存中的事件描述结果
            const narrativeCacheKey = `${AppState.pdfMd5}_eventDescription`;
            const narrativeResult = JSON.parse(localStorage.getItem(narrativeCacheKey) ?? '{}').description ?? 'No narrative data available';

            // 获取缓存中的结构化数据结果
            const structuredDataResultsText = AppState.finalStructuredData ? JSON.stringify(AppState.finalStructuredData, null, 2) : 'No data available';

            // 获取缓存中的二次转换数据结果
            const transformedDataResultsText = localStorage.getItem(`${AppState.pdfMd5}_transformedData`) || AppState.finalTransformedData || 'No data available';

            // 获取缓存中的insight数据结果
            const insightResult = AppState.insightData ? JSON.stringify(AppState.insightData, null, 2) : 'No data available';

            // 将获取的结果填充到各自的文本框中
            document.getElementById('imageResultsText').value = recognitionResultsText;
            // document.getElementById('structuredDataResultsText').value = structuredDataResultsText;
            // document.getElementById('transformedDataResultsText').value = transformedDataResultsText;
            document.getElementById('narrativeResultsText').value = narrativeResult;
            // document.getElementById('insightResultsText').value = insightResult;  // 新增insight数据导出
            // JSON 数据使用 JSONEditor 渲染展示
            renderJsonTree(structuredDataResultsText, document.getElementById('structuredDataResultsText'), (json) => {
                AppState.finalStructuredData = json;
                localStorage.setItem(`${AppState.pdfMd5}_structuredData`, JSON.stringify(json));
            });
            renderJsonTree(transformedDataResultsText, document.getElementById('transformedDataResultsText'), (json) => {
                AppState.finalTransformedData = json;
                localStorage.setItem(`${AppState.pdfMd5}_transformedData`, JSON.stringify(json));
            });
            renderJsonTree(insightResult, document.getElementById('insightResultsText'), (json) => {
                AppState.insightData = json;
                localStorage.setItem(`${AppState.pdfMd5}_insightView`, JSON.stringify(json));
            });

            // 显示导出结果的模态框
            if(exportModal){
                exportModal.dispose();
            }
            exportModal = new bootstrap.Modal(document.getElementById('exportModal'));
            exportModal.show();
        });

        document.getElementById('reportBugButton').addEventListener('click', function () {
            const params = {
                tenantId: AppState.tenantId,
                tenantName: AppState.tenantName,
                studyNum: AppState.studyNum,
                userId: AppState.userId,
                userName: AppState.userName,
                environment: AppState.settings.environment,
                currentLanguage: AppState.settings.currentLanguage,
                pdfMd5: AppState.pdfMd5,
                pdfName: document.querySelector('#fileName').innerText.trim(),
            }
            if(!params.pdfMd5){
                Message.warning('请先上传文件！')
                return
            }
            if(!AppState.eventDescription){
                Message.warning('请先进行事件描述生成！')
                return;
            }
            const searchStr = new URLSearchParams(params).toString();
            window.open('/pv-manus-front/report-bug?' + searchStr);  

        });

        document.getElementById('fileMD5').addEventListener('click', function () {
            if(!AppState.pdfMd5){
                Message.warning('请先上传文件！')
                return
            }            
            window.open(`/pv-manus-front/fs?type=document&path=${encodeURIComponent('/'+AppState.pdfMd5)}`);
        });
    }

    /**
     * 加载环境配置项的设置
     */
    function loadEnvironmentSettings() {
        const savedState = localStorage.getItem('appState');
        if (savedState) {
            AppState.settings = JSON.parse(savedState);
        }

        document.getElementById('devEnvInput').value = AppState.settings.devEnv;
        document.getElementById('testEnvInput').value = AppState.settings.testEnv;
        document.getElementById('uatEnvInput').value = AppState.settings.uatEnv;
        document.getElementById('environmentSelect').value = AppState.settings.environment;
        document.getElementById('operationModeSwitch').value = AppState.settings.operationMode;
        document.getElementById('resultRenderFormat').value = AppState.settings.renderFormat;
        document.getElementById('reportTypeSelect').value = AppState.settings.reportType;
    }

    /**
     * 初始化文件上传功能，使用Dropzone库
     */
    function initializeFileUpload() {
        // 获取目标元素
        const fileUploadElement = document.getElementById("fileUpload");

        // 检查并销毁已经存在的 Dropzone 实例
        if (fileUploadElement.dropzone) {
            fileUploadElement.dropzone.destroy();
        }

        // 禁用 Dropzone 的自动发现功能
        Dropzone.autoDiscover = false;

        // 初始化 Dropzone 并绑定事件
        new Dropzone("#fileUpload", {
            autoProcessQueue: false,
            addRemoveLinks: false,
            acceptedFiles: "image/*,application/pdf",
            init: function() {
                // 处理添加的文件
                this.on("addedfile", handleFileAdded);
                // 处理移除的文件
                this.on("removedfile", handleFileRemoved);
            }
        });
    }

    /**
     * 处理添加的文件，根据文件类型调用不同的处理函数
     * @param {File} file - 上传的文件
     */
    function handleFileAdded(file) {
        if (file.type === "application/pdf") {
            // 处理PDF文件上传
            handlePdfUpload(file).then(() => {                
                document.getElementById("fileUpload").dropzone.disable();
                $('.dz-error-message,.dz-error-mark').hide()
            });
        } else {
            // 处理图片文件上传
            handleImageUpload(file).then(() => {                
                document.getElementById("fileUpload").dropzone.disable();
                $('.dz-error-message,.dz-error-mark').hide()
            });
        }
    }

    /**
     * 处理文件移除，清除对应的结果面板和缓存
     * @param {File} file - 被移除的文件
     */
    function handleFileRemoved(file) {
        const panels = document.querySelectorAll('.image-result-container');
        panels.forEach(panel => {
            if (panel.dataset.filename === file.name) {
                panel.remove();  // 移除显示的结果面板
                localStorage.removeItem(file.name);  // 删除缓存
            }
        });
    }

    /**
     * 切换语言设置，并更新UI
     */
    async function switchLanguage() {
        const now = Date.now();
        // 检查是否超过了语言切换的时间间隔
        if (now - AppState.lastSwitchTime < AppState.switchInterval) {
            return;
        }
        AppState.lastSwitchTime = now;

        // 切换语言并保存到AppState
        AppState.settings.currentLanguage = AppState.settings.currentLanguage === 'en' ? 'cn' : 'en';

        // 保存更新后的设置到localStorage
        localStorage.setItem('appState', JSON.stringify(AppState.settings));

        // 更新语言切换图标和文本
        updateLanguageToggleIcon();

        // 实时更新语言环境显示
        document.getElementById('languageEnv').innerText = AppState.settings.currentLanguage;
    }

    /**
     * 更新语言切换图标和文本
     */
    function updateLanguageToggleIcon() {
        const icon = AppState.languageToggleIcon;
        const currentLanguageSpan = AppState.currentLanguageSpan;
        if (AppState.settings.currentLanguage === 'en') {
            icon.classList.remove('fa-language');
            icon.classList.add('fa-globe');
            currentLanguageSpan.textContent = 'English';
        } else {
            icon.classList.remove('fa-globe');
            icon.classList.add('fa-language');
            currentLanguageSpan.textContent = 'Chinese';
        }
    }

    /**
     * 更新环境配置并保存到localStorage
     * @param {boolean} flag - 是否在刷新时避免提示
     */
    function updateEnvironment(flag) {
        const selectedEnv = document.getElementById('environmentSelect').value;
        switch (selectedEnv) {
            case 'dev':
                AppState.basePath = document.getElementById('devEnvInput').value;
                break;
            case 'test':
                AppState.basePath = document.getElementById('testEnvInput').value;
                break;
            case 'uat':
                AppState.basePath = document.getElementById('uatEnvInput').value;
                break;
        }

        // 更新 AppState.settings 对象
        AppState.settings.environment = selectedEnv;
        AppState.settings.devEnv = document.getElementById('devEnvInput').value;
        AppState.settings.testEnv = document.getElementById('testEnvInput').value;
        AppState.settings.uatEnv = document.getElementById('uatEnvInput').value;
        AppState.settings.operationMode = document.getElementById('operationModeSwitch').value;
        AppState.settings.renderFormat = document.getElementById('resultRenderFormat').value;
        AppState.settings.reportType = document.getElementById('reportTypeSelect').value;

        // 保存到localStorage
        localStorage.setItem('appState', JSON.stringify(AppState.settings));

        // 获取选定的租户
        const tenantSelect = document.getElementById('tenantSelect');
        const selectedTenantId = tenantSelect.value;
        const selectedTenant = AppState.tenantList.find(tenant => tenant.tenantId === selectedTenantId);
        if (!selectedTenant || !selectedTenantId) {
            Message.warning(`未找到租户的信息`);
            return;
        }        

        // 更新 sessionStorage 和 localStorage
        updateLocalStorageAndTriggerEvent('tenantId', selectedTenant.tenantId);
        updateLocalStorageAndTriggerEvent('tenantName', selectedTenant.tenantName);

        // 切换租户后清空 studyNum
        updateLocalStorageAndTriggerEvent('studyNum', '');

        // 保存到 AppState
        AppState.tenantId = selectedTenant.tenantId;
        AppState.tenantName = selectedTenant.tenantName;
        AppState.studyNum = '';

        if (!flag) {
            Message.success(`Environment switched to: ${AppState.basePath}`);
        }
    }

    /**
     * 处理PDF文件上传，发送到服务器进行处理，并显示识别结果
     * @param {File} file - 上传的PDF文件
     */
    async function handlePdfUpload(file) {
        // 更新当前节点显示
        updateCurrentNode('上传 PDF 中');

        // 更新文件名显示
        document.getElementById('fileName').textContent = file.name;
        document.getElementById('fileMD5').textContent = '处理中...';

        const formData = new FormData();
        formData.append('file', file);
        formData.append('language', AppState.settings.currentLanguage);
        formData.append('tenantId', AppState.tenantId);
        formData.append('tenantEnv', AppState.tenantEnv);
        formData.append('studyNum', AppState.studyNum);
        formData.append('userId', AppState.userId);
        formData.append('userName', AppState.userName);
        // 显示加载动画
        showLoadingSpinner();
        try {
            // 发送文件到服务器进行处理
            const response = await fetch(`${AppState.basePath}/api/pv/file/upload`, { method: 'POST', body: formData });
            if (!response.ok) {
                throw new Error(`服务器错误：${response.status} ${response.statusText}`);
            }
            const data = await response.json();
            // 保存返回的文件MD5值和总页数
            AppState.pdfMd5 = data.data.md5;
            AppState.pdfTotalPage = data.data.base64_images.length;

            // 更新MD5显示
            document.getElementById('fileMD5').textContent = AppState.pdfMd5;

            // 显示并处理识别结果
            await displayImagesAndProcessRecognition(data.data.base64_images, file.name);

            // 显示浏览器通知
            showNotification('图片识别完成，请完成数据标注');
        } catch (error) {
            console.error('Error uploading PDF:', error);

            // 更新MD5显示为处理失败
            document.getElementById('fileMD5').textContent = '处理失败';

            showErrorInLoadingSpinner(`上传 PDF 时发生错误：${error.message}`);
            AppState.hasError = true; // 标记发生了错误
        } finally {
            updateProgress(1, 1); // 确保进度条更新为完成状态
            if (!AppState.hasError) {
                hideLoadingSpinner();
            }
        }
    }

    /**
     * 处理图片文件上传，并显示识别结果
     * @param {File} file - 上传的图片文件
     */
    async function handleImageUpload(file) {
        // 更新文件名和MD5显示
        document.getElementById('fileName').textContent = file.name;
        document.getElementById('fileMD5').textContent = '（图片无MD5）';

        const reader = new FileReader();
        reader.onload = async (e) => {
            // 显示并处理识别结果
            await displayImagesAndProcessRecognition([e.target.result], file.name);
        };
        reader.readAsDataURL(file);
    }

    /**
     * 显示图片并处理识别结果
     * @param {Array} imageDataArray - 图片数据数组
     * @param {string} fileName - 文件名称
     */
    async function displayImagesAndProcessRecognition(imageDataArray, fileName) {
        const container = document.querySelector('.container');
        // 遍历所有图片数据并创建对应的结果面板
        imageDataArray.forEach((imageData, index) => {
            const imageResultContainer = createImageResultContainer(imageData, fileName, index);
            container.appendChild(imageResultContainer);
        });
        // 进行图片识别
        await processImageRecognition(fileName);
    }

    /**
     * 创建用于显示识别结果的图片容器
     * @param {string} imageData - 图片数据
     * @param {string} fileName - 文件名称
     * @param {number} index - 图片索引
     * @returns {HTMLElement} - 返回创建的图片容器元素
     */
    function createImageResultContainer(imageData, fileName, index) {
        const imageResultContainer = document.createElement('div');
        imageResultContainer.classList.add('image-result-container');
        imageResultContainer.dataset.filename = `${fileName}_page_${index}`;

        const imagePanel = document.createElement('div');
        imagePanel.classList.add('image-panel');
        const img = document.createElement('img');
        img.src = imageData;
        imagePanel.appendChild(img);
        imageResultContainer.appendChild(imagePanel);

        const resultPanel = createResultPanel(index, fileName);
        imageResultContainer.appendChild(resultPanel);

        return imageResultContainer;
    }

    /**
     * 创建用于显示识别结果的结果面板
     * @param {number} index - 图片索引
     * @param {string} fileName - 文件名称
     * @returns {HTMLElement} - 返回创建的结果面板元素
     */
    function createResultPanel(index, fileName) {
        const resultPanel = document.createElement('div');
            resultPanel.classList.add('result-panel');

            const editorId = `editor-${Date.now()}-${index}`;
            resultPanel.innerHTML = `
            <p style="margin-bottom: 3rem !important">&nbsp;</p>
            <!-- 移除标签面板 -->
            <!-- <div class="label-panel"> ... </div> -->
            <div class="results-content" id="${editorId}">处理中...</div>
            <button class="retry-button hidden">重试</button>
        `;
        resultPanel.querySelector('.retry-button').addEventListener('click', () => processImageRecognition(fileName));
        return resultPanel;
    }

    /**
     * 处理图片识别
     * @param {string} fileName - 文件名称
     */
    async function processImageRecognition(fileName) {
        // 更新当前节点显示
        updateCurrentNode('正在处理图像识别');
        // 显示加载框
        showLoadingSpinner();
        const url = `${AppState.basePath}/api/pv/recognize/stream/chat`;
        const body = getRequestBody();
        const cacheKey = `${AppState.pdfMd5}_imageRecognition`;

        // 执行数据获取操作
        AppState.recognitionResults = await fetchData(
            url,
            'POST',
            body,
            cacheKey,
            updateResultPanelCallback(fileName),
            'array',
            AppState.pdfTotalPage,
            (resultDataArray) => {
                // 将结果存储到 AppState 和 localStorage
                resultDataArray.forEach((result, index) => {
                    updateResultPanel(index, result, fileName);
                });
            }
        );

        if (!AppState.hasError) {
            // 隐藏加载框
            hideLoadingSpinner();
        }
    }

    /**
     * 返回用于更新结果面板的回调函数
     * @param {string} fileName - 文件名称
     * @returns {Function} - 返回的回调函数
     */
    function updateResultPanelCallback(fileName) {
        return (data, resultData) => {
            if (data.type === 'end') return;

            // 更新 resultData 对象
            resultData[data.page] = {
                content: data.message.content,
                labels: data.message.labels
            };

            // 更新结果面板
            updateResultPanel(data.page, resultData[data.page], fileName);
        };
    }

    function processCache() {
        const eventDescriptionCacheKey = `${AppState.pdfMd5}_eventDescription`;
        const structuredDataCacheKey = `${AppState.pdfMd5}_structuredData`;
        const transformedDataCacheKey = `${AppState.pdfMd5}_transformedData`;

        const eventDescriptionExists = !!localStorage.getItem(eventDescriptionCacheKey);
        const structuredDataExists = !!localStorage.getItem(structuredDataCacheKey);
        const transformedDataExists = !!localStorage.getItem(transformedDataCacheKey);

        if (eventDescriptionExists || structuredDataExists || transformedDataExists) {
            // 如果后续步骤已经执行过，需要设置 isDataReviewModified 为 true
            AppState.isDataReviewModified = true;
            // 标记需要重新生成结构化数据和二次转换数据
            AppState.isStructuredDataModified = true;
            AppState.isTransformedDataModified = true;
        }
    }

    /**
     * 更新结果面板的内容
     * @param {number} index - 图片索引
     * @param {object} result - 识别结果对象，包含 content 和 labels
     * @param {string} fileName - 文件名称
     */
    function updateResultPanel(index, result, fileName) {
        const currentPanel = document.querySelector(`.image-result-container[data-filename="${fileName}_page_${index}"]`);
        if (currentPanel) {
            const resultsContent = currentPanel.querySelector('.results-content');
            const renderFormat = AppState.settings.renderFormat;

            // 处理内容的显示
            if (resultsContent) {
                if (renderFormat === 'markdown') {
                    const editorConfig = {
						licenseKey: 'GPL', 
                        toolbar: {
                        },
                        plugins: [
                            AccessibilityHelp,
                            Autoformat,
                            AutoLink,
                            Autosave,
                            Bold,
                            BalloonToolbar,
                            Code,
                            CodeBlock,
                            Essentials,
                            Heading,
                            Italic,
                            Link,
                            Paragraph,
                            SelectAll,
                            SourceEditing,
                            Table,
                            TableCaption,
                            TableCellProperties,
                            TableColumnResize,
                            TableProperties,
                            TableToolbar,
                            TextTransformation,
                            Undo
                        ],
                        balloonToolbar: ['bold', 'italic', '|', 'link', '|'],
                        initialData: result.content,
                        placeholder: 'Type or paste your content here!',
                        table: {
                            contentToolbar: ['tableColumn', 'tableRow', 'mergeTableCells', 'tableProperties', 'tableCellProperties']
                        },
                        autosave: {
                            waitingTime: 2000,
                            save(editor) {
                                const data = editor.getData();
                                // 更新缓存中的识别结果
                                const cacheKey = `${AppState.pdfMd5}_imageRecognition`;
                                let recognitionResults = JSON.parse(localStorage.getItem(cacheKey)) || [];
                                if (recognitionResults[index] && recognitionResults[index].content !== data) {
                                    recognitionResults[index].content = data;
                                    localStorage.setItem(cacheKey, JSON.stringify(recognitionResults));
                                    // 设置标记
                                    AppState.isRecognitionModified = true;

                                    processCache();
                                }

                                return true;
                            }
                        }
                    };

                    BalloonEditor.create(resultsContent, editorConfig).then(editor => {
                        // 存储编辑器实例到当前面板
                        currentPanel.editorInstance = editor;

                        // 添加 focus 事件监听器
                        editor.editing.view.document.on('focus', () => {
                            AppState.lastActiveEditor = editor;
                            AppState.lastActiveTime = Date.now();
                        });

                        // 不再需要 blur 事件监听器

                    }).catch(error => {
                        console.error('Error initializing the editor:', error);
                    });
                } else if (renderFormat === 'mindmap') {
                    renderMindMap(result.content, resultsContent);
                } else if (renderFormat === 'tree') {
                    renderJsonTree(result.content, resultsContent);
                }
            }
        }
    }

    function markdownToMarkmapJson(markdown) {
        const lines = markdown.split('\n');
        const root = { content: "", children: [] };
        let stack = [{ node: root, indent: -1 }];

        function getIndentation(line) {
            return line.search(/\S|$/);
        }

        function createNode(content) {
            return { content: content, children: [] };
        }

        function parseContent(content) {
            return content.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
        }

        function parseLine(line) {
            if (/^#{1,6} /.test(line)) {
                const level = line.match(/^#{1,6}/)[0].length;
                return { type: 'heading', level: level, content: parseContent(line.substring(level + 1).trim()) };
            } else if (line.startsWith('- ')) {
                return { type: 'list', content: parseContent(line.substring(2).trim()) };
            } else if (/^\d+\. /.test(line)) {
                return { type: 'orderedList', content: parseContent(line.replace(/^\d+\. /, '').trim()) };
            } else if (/^```/.test(line)) {
                const codeLanguage = line.substring(3).trim();
                return { type: 'codeBlock', language: codeLanguage };
            } else if (/^\|/.test(line)) {
                return { type: 'table', content: line };
            } else if (/^!\[/.test(line)) {
                const imgMatch = line.match(/!\[(.*?)]\((.*?)\)/);
                if (imgMatch) {
                    return { type: 'image', alt: imgMatch[1], src: imgMatch[2] };
                }
            } else {
                return { type: 'text', content: parseContent(line.trim()) };
            }
        }

        let i = 0;
        while (i < lines.length) {
            const line = lines[i];
            const indent = getIndentation(line);
            const trimmedLine = line.trim();
            if (!trimmedLine) {
                i++;
                continue;
            }

            const parsedLine = parseLine(trimmedLine);
            let newNode = null;

            if (parsedLine.type === 'heading') {
                newNode = createNode(parsedLine.content);
                while (stack.length > parsedLine.level) {
                    stack.pop();
                }
                stack[stack.length - 1].node.children.push(newNode);
                stack.push({ node: newNode, indent });
            } else {
                const parent = stack[stack.length - 1].node;
                if (parsedLine.type === 'list' || parsedLine.type === 'orderedList' || parsedLine.type === 'text') {
                    newNode = createNode(parsedLine.content);
                } else if (parsedLine.type === 'codeBlock') {
                    const codeBlock = [];
                    i++;
                    while (i < lines.length && !lines[i].startsWith('```')) {
                        codeBlock.push(lines[i]);
                        i++;
                    }
                    newNode = createNode(`<pre><code class="language-${parsedLine.language}">${codeBlock.join('\n').trim()}</code></pre>`);
                } else if (parsedLine.type === 'table') {
                    newNode = createNode(`<table>${parsedLine.content}</table>`);
                } else if (parsedLine.type === 'image') {
                    newNode = createNode(`<img src="${parsedLine.src}" alt="${parsedLine.alt}">`);
                }

                if (newNode) {
                    parent.children.push(newNode);
                }
            }

            i++;
        }

        return root;
    }

    function renderMindMap(markdown, container) {
        container.innerHTML = '';

        const mindmapContainer = document.createElement('svg');
        container.appendChild(mindmapContainer);

        const markmapJson = markdownToMarkmapJson(markdown);

        console.info(markmapJson)

        window.markmap.Markmap.create(mindmapContainer, null, markmapJson);

        // 模拟剪切和粘贴操作
        setTimeout(() => {
            const temp = mindmapContainer.outerHTML;
            mindmapContainer.remove();
            container.innerHTML = temp; // 重新插入
        }, 0);
    }

    function renderJsonTree(markdown, container, onChange) {
            // 参数验证
        if (!container) {
            console.error('renderJsonTree: Container element is missing');
            return null;
        }
        container.innerHTML = ''; // 清空容器内容
        if (!markdown) {
            console.error('renderJsonTree: JSON data is missing');
            container.innerHTML = 'No data to display';
            return null;
        }
        try {
            // 确保数据是有效的JSON对象
            const jsonData = typeof markdown === 'string' ? JSON.parse(markdown) : markdown;
            // 验证数据类型
            if (typeof jsonData !== 'object') {
                throw new Error('Data must be an object or array');
            }
            // 使用jsoneditor展示JSON数据
            const editor = new JSONEditor(container, {
                mode: 'tree', 
                modes: ['view', 'form', 'text', 'tree', 'preview'],
                mainMenuBar: true,
                navigationBar: true,
                statusBar: true,
                search: true,
                onEditable : () => {
                    return {field: true, value: true}
                },
                onChange: function () {
                    try {
                      const json = editor.get(); 
                      onChange && onChange(json);
                    } catch (err) {
                      console.error('Error:', err);
                    }
                }
            }, jsonData);
            editor.expandAll();
        } catch (error) {
            console.error('Invalid JSON data:', error);
        }
    }

    function checkPairedMarkers(html, markerPairs) {
        // 参数验证
        if (!html || !markerPairs || !Array.isArray(markerPairs) || !markerPairs.length) {
            return {
                isValid: false,
                message: '参数无效'
            };
        }
      
        // 用于存储标记的栈
        const stack = [];
        // 用于记录每组标记是否出现过
        const pairFound = new Array(markerPairs.length).fill(false);
        
        // 构建所有标记的位置信息
        const markers = [];
        markerPairs.forEach((pair, pairIndex) => {
            const [start, end] = pair;
            
            // 查找所有起始标记的位置
            let pos = 0;
            while ((pos = html.indexOf(start, pos)) !== -1) {
                markers.push({
                    position: pos,
                    isStart: true,
                    text: start,
                    pairIndex
                });
                pos += start.length;
            }
            
            // 查找所有结束标记的位置
            pos = 0;
            while ((pos = html.indexOf(end, pos)) !== -1) {
                markers.push({
                    position: pos,
                    isStart: false,
                    text: end,
                    pairIndex
                });
                pos += end.length;
            }
        });
      
        // 按位置排序所有标记
        markers.sort((a, b) => a.position - b.position);
      
        // 检查标记配对
        for (const marker of markers) {
            if (marker.isStart) {
                // 起始标记直接入栈
                stack.push(marker);
            } else {
                // 结束标记需要检查栈顶
                if (stack.length === 0) {
                    return {
                        isValid: false,
                        message: `发现未配对的结束标记: ${marker.text}`
                    };
                }
      
                const lastStart = stack[stack.length - 1];
                if (lastStart.pairIndex !== marker.pairIndex) {
                    return {
                        isValid: false,
                        message: `标记配对错误: ${lastStart.text} 与 ${marker.text}`
                    };
                }
      
                // 标记该组已找到
                pairFound[marker.pairIndex] = true;
                // 移除栈顶元素
                stack.pop();
            }
        }
      
        // 检查栈是否为空（是否还有未配对的起始标记）
        if (stack.length > 0) {
            return {
                isValid: false,
                message: `发现未配对的起始标记: ${stack[stack.length - 1].text}`
            };
        }
      
        // 检查是否至少有一组完整的配对
        if (!pairFound.some(found => found)) {
            return {
                isValid: false,
                message: '未找到任何完整的标记对'
            };
        }
      
        return {
            isValid: true,
            message: '所有标记都正确配对'
        };
      }

    /**
     * 生成报告全息视图
     */
    async function generateInsightView() {
        // 更新当前节点显示
        updateCurrentNode('汇总图片识别结果');
        // 显示加载框
        showLoadingSpinner();

        // 主动保存所有编辑器中的内容
        const imagePanels = document.querySelectorAll('.image-result-container');
        if (imagePanels.length > 0) {
            updateCurrentNode('保存编辑器内容中...');

            // 获取所有编辑器实例
            const editorPromises = [];
            imagePanels.forEach(panel => {
                if (panel.editorInstance) {
                    // 主动触发编辑器的保存功能
                    try {
                        // 使用Autosave插件的save方法
                        const autosavePlugin = panel.editorInstance.plugins.get('Autosave');
                        if (autosavePlugin) {
                            editorPromises.push(autosavePlugin.save());
                        }
                    } catch (e) {
                        console.error('Error saving editor content:', e);
                    }
                }
            });

            // 等待所有编辑器保存完成
            if (editorPromises.length > 0) {
                await Promise.all(editorPromises);
            } else {
                // 如果没有找到编辑器实例，还是保留一个较短的延迟
                await new Promise(resolve => setTimeout(resolve, 500));
            }
        }

        // 检查是否需要同步识别结果
        const recognitionCacheKey = `${AppState.pdfMd5}_imageRecognition`;
        const recognitionResults = JSON.parse(localStorage.getItem(recognitionCacheKey) || '[]');

        if (recognitionResults.length > 0 && AppState.isRecognitionModified) {
            await recognizeWithCachedResults(recognitionResults);
        }
 
        const rev = checkPairedMarkers(
            recognitionResults.map(v => v.content).join(' '),
            Array.from(document.querySelectorAll('#labeling-tab .label-group')).map(el => [el.querySelector('.start-label').innerText.trim(),el.querySelector('.end-label').innerText.trim()]))
         
        if(!rev.isValid){
            showErrorInLoadingSpinner(rev.message)
            return 
        }

        // 更新当前节点显示
        updateCurrentNode('生成全息视图中');
        const cacheKey = `${AppState.pdfMd5}_insightView`;
        const url = `${AppState.basePath}/api/pv/extract/stream/chat`;
        const body = getRequestBody();

        // 根据标记决定是否使用缓存
        const useCache = !AppState.isRecognitionModified;

        // 执行数据获取操作
        AppState.insightData = await fetchData(
            url,
            'POST',
            body,
            cacheKey,
            (data, resultData) => {
                if (data.type === 'message') {
                    resultData[data.module] = data.message;
                    processCache();
                }
            },
            'object',
            7,
            (cachedResult) => {
                AppState.insightData = cachedResult;
                displayInsightView(AppState.insightData || '');
            },
            useCache // 根据标记决定是否使用缓存
        );

        if (!AppState.insightData) {
            AppState.insightData = {};
        }
        displayInsightView(AppState.insightData || '');

        document.querySelector('#gotoInsightView').style.display = 'inline';

        // 重置标记，避免后续不必要的请求
        AppState.isRecognitionModified = false;

        if (!AppState.hasError) {
            // 隐藏加载框
            hideLoadingSpinner();
        }

        // 自动模式跳过点击Narrative步骤
        if (isAutoMode()) {
            await generateEventDescription();
        }
    }

    /**
     * 使用缓存的识别结果调用 recognize 方法
     * @param {Array} recognitionResults - 从缓存中获取的识别结果
     */
    async function recognizeWithCachedResults(recognitionResults) {
        // 更新当前节点显示
        updateCurrentNode('同步识别结果中');
        // 更新进度条，初始化为0
        updateProgress(0, 1);

        // 定义用于传递给 recognize 的参数
        const requestBody = {
            md5: AppState.pdfMd5,
            tenantId: AppState.tenantId,
            tenantEnv: AppState.tenantEnv,
            studyNum: AppState.studyNum,
            userId: AppState.userId,
            userName: AppState.userName,
            data: recognitionResults.map(result => ({
                content: result.content,
                labels: result.labels
            }))
        };

        try {
            const response = await fetch(`${AppState.basePath}/api/pv/recognize/stream/chat`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'text/event-stream' // 注意：Accept 头设置为 SSE
                },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                throw new Error(`Error calling recognize API: ${response.status} ${response.statusText}`);
            }

            const reader = response.body.getReader();
            const decoder = new TextDecoder("utf-8");
            let processedMessages = 0;
            const totalMessages = AppState.pdfTotalPage || recognitionResults.length || 1; // 根据情况调整
            updateProgress(processedMessages, totalMessages);

            let resultData = []; // 用于存储识别结果

            let bufferedData = '';
            while (true) {
                const { done, value } = await reader.read();
                if (done) {
                    break;
                }

                bufferedData += decoder.decode(value, { stream: true });

                let boundary = bufferedData.indexOf('\n');
                while (boundary !== -1) {
                    const line = bufferedData.slice(0, boundary);
                    bufferedData = bufferedData.slice(boundary + 1);

                    if (line.startsWith('data: ')) {
                        const jsonString = line.slice(6).trim();
                        if (jsonString === '[DONE]') {
                            continue;
                        }
                        try {
                            const data = JSON.parse(jsonString);
                            // 处理接收到的数据
                            // 例如，将其推入 resultData 数组
                            resultData.push(data);
                            // 更新进度
                            processedMessages++;
                            updateProgress(processedMessages, totalMessages);
                        } catch (err) {
                            console.error('Error parsing SSE data:', err);
                        }
                    }
                    boundary = bufferedData.indexOf('\n');
                }
            }

            // 处理完所有数据后，可以使用 resultData
            console.log('Recognition results updated successfully', resultData);

            // 更新进度条为完成
            updateProgress(totalMessages, totalMessages);

        } catch (error) {
            // 显示错误信息
            console.error('Error updating recognition results:', error);
            showErrorInLoadingSpinner(`同步识别结果时发生错误：${error.message}`);
            AppState.hasError = true; // 标记发生了错误
        }
    }

    /**
     * 显示全息视图内容
     * @param {string} insight - 报告全息视图内容
     */
    function displayInsightView(insight) {
        // 如果存在方案编号，更新显示和AppState
        if (insight && insight['报告基础信息'] && insight['报告基础信息']['方案编号']) {
            const studyNum = insight['报告基础信息']['方案编号'];
            document.getElementById('studyNum').innerText = studyNum;
            AppState.studyNum = studyNum;
        }

        // 清空逆向映射关系
        AppState.reverseFieldMapping = {};

        // 遍历全局字段定义，提取数据并更新value
        AppState.fieldData.forEach(field => {
            const { key, path} = field;
            const value = getNestedValue(insight, path);
            field.value = value !== undefined ? value : '';

            // 构建逆向映射关系
            AppState.reverseFieldMapping[key] = path;
        });

        // 将数据保存到 AppState
        AppState.dataReviewData = AppState.fieldData;

        // 渲染数据复核的内容
        renderKeyValueData(AppState.dataReviewData);
    }

    /**
     * 根据路径获取对象中的嵌套属性值
     * @param {Object} obj - 目标对象
     * @param {string} path - 属性路径，例如 'datetime.start_datetime'
     * @returns {*} - 对应的属性值
     */
    function getNestedValue(obj, path) {
        const keys = path.split('.');
        let current = obj;
        for (const key of keys) {
            if (current && typeof current === 'object' && key in current) {
                current = current[key];
            } else {
                return undefined;
            }
        }
        return current;
    }

    /**
     * 根据路径设置对象中的嵌套属性值
     * @param {Object} obj - 目标对象
     * @param {string} path - 属性路径，例如 'datetime.start_datetime'
     * @param {*} value - 要设置的值
     */
    function setNestedValue(obj, path, value) {
        const keys = path.split('.');
        let current = obj;
        for (let i = 0; i < keys.length - 1; i++) {
            const key = keys[i];
            if (!(key in current) || typeof current[key] !== 'object') {
                current[key] = {};
            }
            current = current[key];
        }
        const finalKey = keys[keys.length - 1];
        current[finalKey] = value;
    }

    function isEmptyValue(value) {
        return value === undefined || value === null || value === '' || value === '双击编辑' || (Array.isArray(value) && value.length === 0);
    }

    function renderKeyValueData(data) {
        const keyValueContainer = document.getElementById('key-value-data');
        keyValueContainer.innerHTML = ''; // 清空内容

        data.forEach(field => {
            let value = field.value;
            const key = field.key;
            const type = field.type || 'optional'; // 默认类型为可选

            const itemDiv = document.createElement('div');
            itemDiv.classList.add('data-item', `type-${type}`); // 添加类型类

            const keyDiv = document.createElement('div');
            keyDiv.classList.add('key');
            keyDiv.textContent = key;

            // 统一使用图标表示字段类型
            const iconSpan = document.createElement('span');
            const icon = document.createElement('i');
            if (type === 'required') {
                icon.classList.add('fas', 'fa-star'); // 星号图标
                icon.style.color = 'red'; // 红色
            } else if (type === 'optional') {
                icon.classList.add('fas', 'fa-circle'); // 圆圈图标
                icon.style.color = 'greenyellow'; // 灰色
            } else if (type === 'readonly') {
                icon.classList.add('fas', 'fa-lock'); // 锁定图标
                icon.style.color = 'orange'; // 灰色
            }
            icon.style.marginLeft = '5px';
            iconSpan.appendChild(icon);
            keyDiv.appendChild(iconSpan);

            const valueDiv = document.createElement('div');
            valueDiv.classList.add('value');

            // 判断值是否为空，包括空数组
            if (isEmptyValue(value)) {
                valueDiv.textContent = type !== 'readonly' ? '双击编辑' : '（只读）'; // 只读字段显示“（只读）”
                if (type !== 'readonly') {
                    valueDiv.classList.add('placeholder'); // 添加占位符样式
                } else {
                    valueDiv.classList.add('readonly'); // 添加只读样式
                }
            } else if (Array.isArray(value)) {
                valueDiv.textContent = value.join(', ');
            } else {
                valueDiv.textContent = value;
            }

            // 设置最小高度，使得空的 div 也可以被点击
            valueDiv.style.minHeight = '20px';

            if (type !== 'readonly') {
                // 鼠标样式为可点击
                valueDiv.style.cursor = 'pointer';

                // 添加双击事件监听器，支持编辑
                valueDiv.addEventListener('dblclick', function() {
                    const input = document.createElement('input');
                    input.type = 'text';
                    input.value = isEmptyValue(value) ? '' : valueDiv.textContent;
                    input.style.width = '100%';

                    valueDiv.innerHTML = '';
                    valueDiv.appendChild(input);

                    input.focus();

                    // 在数据复核的修改事件中，设置标记和更新数据
                    input.addEventListener('blur', function() {
                        const newValue = input.value.trim();

                        // 更新数据，根据原始值的类型进行处理
                        if (Array.isArray(value)) {
                            field.value = newValue ? newValue.split(',').map(item => item.trim()) : [];
                            valueDiv.textContent = field.value.join(', ');
                        } else {
                            field.value = newValue;
                            valueDiv.textContent = newValue || '双击编辑'; // 如果新值为空，显示占位符
                        }

                        // 移除或添加占位符样式
                        if (newValue && newValue !== '双击编辑') {
                            valueDiv.classList.remove('placeholder');
                        } else {
                            valueDiv.classList.add('placeholder');
                        }

                        // 必填字段且未填写值，给予提示
                        if (type === 'required' && !newValue) {
                            Message.warning(`${key}为必填项，请填写。`);
                        }

                        // 设置标记，表示数据已修改
                        AppState.isDataReviewModified = true;
                        // 标记需要重新生成结构化数据和二次转换数据
                        AppState.isStructuredDataModified = true;
                        AppState.isTransformedDataModified = true;

                        // 更新 AppState.insightData 中的对应值
                        const path = field.path;
                        if (path) {
                            setNestedValue(AppState.insightData, path, field.value);
                        }

                        // 更新本地的 value，以便后续编辑时正确处理
                        value = field.value;
                    });

                    input.addEventListener('keydown', function(event) {
                        if (event.key === 'Enter') {
                            input.blur();
                        }
                    });
                });
            } else {
                // 只读字段，设置灰色样式
                valueDiv.classList.add('readonly');
            }

            itemDiv.appendChild(keyDiv);
            itemDiv.appendChild(valueDiv);
            keyValueContainer.appendChild(itemDiv);
        });
    }

    /**
     * 生成事件描述
     * @param {boolean} [forceRegenerate=false] - 是否强制重新生成
     */
    async function generateEventDescription(forceRegenerate = false) {
        // 显示加载框
        showLoadingSpinner();
        // 如果数据复核的数据已被修改，更新全息视图的数据
        if (AppState.isDataReviewModified) {
            await updateInsightDataWithModifiedDataReview();
            // 重置标记，避免后续不必要的请求
            AppState.isDataReviewModified = false;
            // 新增代码：标记需要重新生成结构化数据和二次转换数据
            AppState.isStructuredDataModified = true;
            AppState.isTransformedDataModified = true;
            forceRegenerate = true;
        }

        // 更新当前节点显示
        updateCurrentNode('生成事件描述中');
        const cacheKey = `${AppState.pdfMd5}_eventDescription`;
        const url = `${AppState.basePath}/api/pv/narrative/stream/chat`;
        const body = getRequestBody();

        // 根据标记和参数决定是否使用缓存
        const useCache = !AppState.isDataReviewModified && !forceRegenerate;

        // 执行数据获取操作
        const eventDescriptionData = await fetchData(
            url,
            'POST',
            body,
            cacheKey,
            (data, resultData) => {
                if (data.type === 'message') {
                    resultData.description = (resultData.description || '') + data.message;
                }
            },
            'object',
            1,
            null,
            useCache // 根据标记决定是否使用缓存
        );

        if (!AppState.hasError) {
            // 隐藏加载框
            hideLoadingSpinner();
            // 展示事件描述
            displayEventDescription(eventDescriptionData.description || '');
        }

        // 显示浏览器通知
        showNotification('Narrative生成完成，请继续后续的流程');
    }

    async function updateInsightDataWithModifiedDataReview() {
        // 更新当前节点显示
        updateCurrentNode('同步数据复核结果中');

        // 准备请求体，包含修改后的全息视图数据
        const requestBody = {
            md5: AppState.pdfMd5,
            tenantId: AppState.tenantId,
            tenantEnv: AppState.tenantEnv,
            studyNum: AppState.studyNum,
            userId: AppState.userId,
            userName: AppState.userName,
            // 包含修改后的全息视图数据
            data: AppState.insightData
        };

        // 发送请求更新全息视图数据
        const url = `${AppState.basePath}/api/pv/extract/stream/chat`;

        // 使用与 generateInsightView 相同的缓存键
        const cacheKey = `${AppState.pdfMd5}_insightView`;

        // 清空之前的 insightData
        AppState.insightData = {};

        try {
            // 执行数据获取操作，与 generateInsightView 方法一致
            AppState.insightData = await fetchData(
                url,
                'POST',
                requestBody,
                cacheKey, // 使用相同的 cacheKey 更新缓存
                (data, resultData) => {
                    if (data.type === 'message') {
                        resultData[data.module] = data.message;
                    }
                },
                'object',
                7, // 根据实际情况调整 totalMessages
                null, // 不需要 cacheCallback
                false // 不使用缓存，强制从后端获取最新数据
            );

            // fetchData 函数在完成后会自动更新缓存

            // 重新渲染数据复核的内容
            displayInsightView(AppState.insightData || '');

            // 重置标记，避免后续不必要的请求
            AppState.isDataReviewModified = false;

        } catch (error) {
            console.error('Error updating insight data:', error);
            showErrorInLoadingSpinner(`同步数据复核结果时发生错误：${error.message}`);
            AppState.hasError = true;
        } finally {
            updateProgress(1, 1);
        }
    }

    /**
     * 显示事件描述内容
     * @param {string} description - 事件描述
     */
    function displayEventDescription(description) {
        // const eventDescriptionContent = document.getElementById('eventDescriptionContent');
        const eventDescriptionTextarea = document.getElementById('eventDescriptionTextarea');

        // 转义星号防止Markdown解析
        // eventDescriptionContent.innerHTML = marked.parse(description.replace(/\*/g, '\\*'));
        eventDescriptionTextarea.value = description;

        // eventDescriptionContent.classList.remove('hidden');
        eventDescriptionTextarea.classList.add('hidden');

        eventDescriptionTextarea.style.height = 'calc(100vh - 200px)';

        document.getElementById('updateDescriptionBtn').textContent = '更新';
        AppState.eventDescriptionModal.show();

        // 新增，保存事件描述到 AppState
        AppState.eventDescription = description;
    }

    /**
     * 生成结构化数据
     */
    async function generateStructuredData() {
        // 更新当前节点显示
        updateCurrentNode('生成结构化数据中');
        // 显示加载框
        showLoadingSpinner();
        const cacheKey = `${AppState.pdfMd5}_structuredData`;
        const url = `${AppState.basePath}/api/pv/structured/stream/chat`;
        const body = getRequestBody();

        // 根据标记决定是否使用缓存
        const useCache = !AppState.isStructuredDataModified;

        // 执行数据获取操作
        AppState.finalStructuredData = await fetchData(
            url,
            'POST',
            body,
            cacheKey,
            (data, resultData) => {
                if (data.type === 'message') {
                    resultData[data.module] = data.message;
                }
            },
            'object',
            undefined,
            null,
            useCache // 根据标记决定是否使用缓存
        );

        if (!AppState.hasError) {
            // 隐藏加载框
            hideLoadingSpinner();
        }

        // 重置标记，表示结构化数据已更新
        AppState.isStructuredDataModified = false;

        // 自动模式跳过点击Narrative步骤
        if (isAutoMode()) {
            await secondaryTransformData();
        }
    }

    /**
     * 执行二次数据转换
     */
    async function secondaryTransformData() {
        // 更新当前节点显示
        updateCurrentNode('二次转换数据中');
        // 显示加载框
        showLoadingSpinner();
        const cacheKey = `${AppState.pdfMd5}_transformedData`;
        const url = `${AppState.basePath}/api/pv/transformer/stream/chat`;
        const body = getRequestBody();

        // 根据标记决定是否使用缓存
        const useCache = !AppState.isTransformedDataModified;

        // 执行数据获取操作
        AppState.finalTransformedData = await fetchData(
            url,
            'POST',
            body,
            cacheKey,
            (data, resultData) => {
                if (data.type === 'message') {
                    resultData[data.module] = data.message;
                }
            },
            'object',
            undefined,
            null,
            useCache // 根据标记决定是否使用缓存
        );

        if (!AppState.hasError) {
            // 隐藏加载框
            hideLoadingSpinner();
        }

        // 重置标记，表示二次转换数据已更新
        AppState.isTransformedDataModified = false;

        // 如果选择了“随访报告”，在最后打印描述
        if (AppState.settings.reportType === 'follow-up') {
            console.log("This is a follow-up report. Additional follow-up information should be included here.");
        }

        // 显示浏览器通知
        showNotification('二次转换完成，请继续后续的流程');

        const errors = validate(AppState.finalTransformedData,schema_rules);
        if(errors && errors.length > 0 ){
            showErrorInLoadingSpinner(errors[0].message + '，请检查数据');
            const spinner = document.getElementById('loading-spinner');
            const fn = () => {
                window.open(`/pv-manus-front/pv-extraction?key=${cacheKey}`)
                spinner.removeEventListener('click', fn, false); 
            }
            spinner.addEventListener('click', fn, false); 
            return;
        }



        // 发送结构化数据到指定接口
        // 判断报告类型，调用 sendStructuredData 函数
        // 根据报告类型设置 action，并发送结构化数据
        const action = AppState.settings.reportType === 'follow-up' ? 'followUpstrucutrueData' : 'strucutrueData';
        // 发送文件的MD5
        const MD5 = document.getElementById('fileMD5').textContent || AppState.pdfMd5;
        await sendStructuredData({ action, data: AppState.finalTransformedData, md5: MD5 });
        console.log("Response from sending structured data1:", AppState.finalTransformedData);
    }

    async function sendStructuredData({ action, data, md5 }) {
		if (typeof chrome !== 'undefined' && chrome.runtime) {
        // 识别完成后发送消息给 background script
			chrome.runtime.sendMessage({ action, data, md5 });
		}else{
			window.postMessage({ action, data, md5 }, '*');
		}
    }

    /**
     * 通用的用于从服务器获取数据的方法
     * @param {string} url - API请求的URL
     * @param {string} method - HTTP方法
     * @param {Object} body - 请求体
     * @param {string} cacheKey - 缓存键
     * @param {Function} processStreamCallback - 处理流数据的回调函数
     * @param {string} [resultDataType='object'] - 结果数据类型，默认是'object'
     * @param {number} [totalMessages=7] - 总消息数，用于进度更新
     * @param {Function} [cacheCallback] - 处理缓存数据的回调函数
     * @param useCache
     * @returns {Promise<Object>} - 返回请求的数据
     */
    async function fetchData(url, method, body, cacheKey, processStreamCallback, resultDataType = 'object', totalMessages = 7, cacheCallback, useCache = true) {
        const cachedResult = (useCache && cacheKey) ? localStorage.getItem(cacheKey) : null;
        if (cachedResult) {
            const cachedResultObject = JSON.parse(cachedResult);
            // 直接将进度条更新为完成状态，不再模拟加载过程
            updateProgress(totalMessages, totalMessages);
            // 立即调用回调函数
            if (cacheCallback) {
                cacheCallback(cachedResultObject);
            }
            return cachedResultObject;
        }

        let processedMessages = 0; // 当前已处理的消息数
        updateProgress(processedMessages, totalMessages);

        try {
            const response = await fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'text/event-stream'
                },
                body: JSON.stringify(body)
            });

            if (!response.ok) {
                throw new Error(`服务器错误：${response.status} ${response.statusText}`);
            }

            const reader = response.body.getReader();
            const decoder = new TextDecoder("utf-8");
            let resultData = resultDataType === 'array' ? [] : {};

            const processStream = async () => {
                let bufferedData = '';
                while (true) {
                    const { done, value } = await reader.read();
                    if (done) {
                        // 如果返回结果为空，则直接抛出异常，阻断流程
                        if (resultData && Object.keys(resultData).length > 0) {
                            localStorage.setItem(cacheKey, JSON.stringify(resultData));
                        } else {
                            throw new Error('服务器处理异常，请重试！');
                        }
                        break;
                    }

                    bufferedData += decoder.decode(value, { stream: true });

                    let boundary = bufferedData.indexOf('\n');
                    while (boundary !== -1) {
                        const chunk = bufferedData.slice(0, boundary);
                        bufferedData = bufferedData.slice(boundary + 1);

                        if (chunk.startsWith('data: ')) {
                            const jsonString = chunk.slice(6).trim();
                            const data = JSON.parse(jsonString);

                            // 新增的错误处理
                            if (data.type === 'error') {
                                throw new Error(data.message);
                            }

                            // 新增心跳的处理
                            if (data.type === 'heartbeat') {
                                continue
                            }

                            processStreamCallback(data, resultData);

                            // 更新进度
                            processedMessages++;
                            updateProgress(processedMessages, totalMessages);
                        }
                        boundary = bufferedData.indexOf('\n');
                    }
                }
            };

            await processStream();
            return resultData;
        } catch (error) {
            console.error(error);
            showNotification('发生错误', error.message);
            showErrorInLoadingSpinner(`发生错误：${error.message}`);
            AppState.hasError = true; // 标记发生了错误
            throw error; // 重新抛出错误以便上层处理
        } finally {
            updateProgress(totalMessages, totalMessages);
        }
    }

    function showErrorInLoadingSpinner(errorMessage) {
        const spinner = document.getElementById('loading-spinner');
        const spinnerIcon = document.querySelector('#loading-spinner .spinner-icon');

        // 设置加载结束时间
        AppState.endTime = new Date();

        // 更新时间显示
        const startTime = AppState.startTime ? formatTime(AppState.startTime) : '--:--:--';
        const endTime = formatTime(AppState.endTime);
        const totalTime = ((AppState.endTime - AppState.startTime) / 1000).toFixed(2);

        document.getElementById('startTime').textContent = `开始时间: ${startTime}`;
        document.getElementById('endTime').textContent = `结束时间: ${endTime}`;
        document.getElementById('totalTime').textContent = `总耗时: ${totalTime} 秒`;

        // 设置加载框样式，与“完成”状态一致，但背景颜色为红色
        spinner.style.backgroundColor = '#D48806'; // 设置背景为红色
        spinner.style.display = 'block';
        spinner.style.animation = 'flash 1s linear infinite'; // 与完成状态相同的动画

        // 停止旋转动画
        spinnerIcon.classList.remove('rotate');

        // 网络异常的描述转义
        errorMessage = errorMessage.replace(/(Failed to fetch|network error)$/, '网络异常，无法连接到服务器');

        // 显示错误信息
        document.getElementById('currentNode').textContent = errorMessage;

        // 设置提示信息
        document.getElementById('loadingMessage').textContent = '运行完成，请确认结果后，点击 loading 框来结束当前流程';

        // 隐藏进度条和百分比
        document.getElementById('progress-container').style.display = 'none';

        AppState.isLoading = false;
        AppState.hasFinishedLoading = true;
        AppState.hasError = true; // 标记发生了错误

        // 允许通过点击隐藏加载框
        spinner.style.pointerEvents = 'auto'; // 允许点击
        spinner.addEventListener('click', hideSpinnerOnClick);
    }

    /**
     * 显示加载动画
     */
    function showLoadingSpinner() {
        // 重置 AppState 中的相关状态信息
        resetAppState();

        AppState.hasError = false; // 重置错误标记

        // 设置加载开始时间
        AppState.startTime = new Date();

        // 重置并显示加载框的 UI
        resetLoadingUI();

        updateLoadingUI(true);

        // 设置提示信息
        document.getElementById('loadingMessage').textContent = '运行中，请勿点击 loading 框';

        // 确保在加载过程中不能点击隐藏
        const spinner = document.getElementById('loading-spinner');
        spinner.removeEventListener('click', hideSpinnerOnClick);  // 移除之前可能绑定的事件
        spinner.style.pointerEvents = 'none'; // 禁用点击事件
    }

    /**
     * 隐藏加载动画
     */
    function hideLoadingSpinner() {
        // 设置加载结束时间
        AppState.endTime = new Date();

        // 添加更新当前节点显示
        updateCurrentNode('运行完成');

        updateLoadingUI(false);

        // 确保旋转动画已停止
        const spinnerIcon = document.querySelector('#loading-spinner .spinner-icon');
        spinnerIcon.classList.remove('rotate');

        // 重置提示信息（移除或注释掉这行代码）
        // document.getElementById('loadingMessage').textContent = '';

        // 加载完成后，允许通过点击隐藏加载框
        const spinner = document.getElementById('loading-spinner');
        spinner.style.pointerEvents = 'auto'; // 允许点击
        spinner.addEventListener('click', hideSpinnerOnClick);
    }

    /**
     * 点击隐藏加载框的处理函数
     */
    function hideSpinnerOnClick() {
        const spinner = document.getElementById('loading-spinner');
        if (AppState.hasFinishedLoading) {
            spinner.style.display = 'none';
        }
    }

    /**
     * 重置加载框的UI状态，清除上次的显示内容
     */
    function resetLoadingUI() {
        const spinner = document.getElementById('loading-spinner');

        // 重置时间和进度条显示
        document.getElementById('startTime').textContent = `开始时间: --:--:--`;
        document.getElementById('endTime').textContent = `结束时间: --:--:--`;
        document.getElementById('totalTime').textContent = `总耗时: --`;
        document.getElementById('loadingProgress').value = 0;
        document.getElementById('progressText').textContent = `0%`;

        // 重置加载框的背景和动画状态
        spinner.style.backgroundColor = '#716DCC';
        spinner.style.display = 'block';
        spinner.style.animation = 'none';

        // 重置进度容器显示
        document.getElementById('progress-container').style.display = 'block';
    }

    /**
     * 重置 AppState 中的加载相关状态
     */
    function resetAppState() {
        AppState.startTime = null;
        AppState.endTime = null;
        AppState.isLoading = false;
        AppState.hasFinishedLoading = false;
    }

    /**
     * 更新加载动画的UI状态
     * @param {boolean} isLoading - 当前是否正在加载
     */
    function updateLoadingUI(isLoading) {
        const spinner = document.getElementById('loading-spinner');
        const spinnerIcon = document.querySelector('#loading-spinner .spinner-icon');
        const startTime = AppState.startTime ? formatTime(AppState.startTime) : '--:--:--';
        const endTime = AppState.endTime ? formatTime(AppState.endTime) : '--:--:--';
        const totalTime = AppState.endTime ? ((AppState.endTime - AppState.startTime) / 1000).toFixed(2) : '--';

        document.getElementById('startTime').textContent = `开始时间: ${startTime}`;
        document.getElementById('endTime').textContent = `结束时间: ${endTime}`;
        document.getElementById('totalTime').textContent = `总耗时: ${totalTime} 秒`;

        if (isLoading) {
            spinner.style.backgroundColor = '#716DCC'; // 进行中状态的颜色
            spinner.style.animation = 'none';
            spinnerIcon.classList.add('rotate'); // 添加旋转动画
            document.getElementById('loadingMessage').textContent = '运行中，请勿点击 loading 框';
            spinner.style.pointerEvents = 'none'; // 禁用点击事件
        } else {
            spinner.style.backgroundColor = AppState.hasError ? '#D48806' : '#3A8176'; // 根据是否有错误设置颜色
            spinner.style.animation = 'flash 1s linear infinite';
            spinnerIcon.classList.remove('rotate'); // 停止旋转动画
            document.getElementById('loadingMessage').textContent = '运行完成，请确认结果后，点击 loading 框来结束当前流程';
            spinner.style.pointerEvents = 'auto'; // 允许点击
        }

        spinner.style.display = 'block';

        AppState.isLoading = isLoading;
        AppState.hasFinishedLoading = !isLoading;
    }

    /**
     * 更新当前操作节点的显示
     * @param {string} nodeName - 当前节点名称
     */
    function updateCurrentNode(nodeName) {
        document.getElementById('currentNode').textContent = nodeName;
    }

    /**
     * 更新加载进度
     * @param {number} processedMessages - 已处理的消息数
     * @param {number} totalMessages - 总消息数
     */
    function updateProgress(processedMessages, totalMessages) {
        const progress = Math.min((processedMessages / totalMessages) * 100, 100);
        document.getElementById('loadingProgress').value = progress;
        document.getElementById('progressText').textContent = `${Math.round(progress)}%`;
    }

    /**
     * 格式化时间为HH:MM:SS
     * @param {Date} date - 时间对象
     * @returns {string} - 格式化后的时间字符串
     */
    function formatTime(date) {
        return date.toTimeString().split(' ')[0];
    }

    /**
     * 显示错误信息
     * @param {string} message - 错误信息
     */
    function showError(message) {
        const errorModal = document.getElementById('errorModal');
        const errorMessage = document.getElementById('errorMessage');

        if (errorModal && errorMessage) {
            errorMessage.textContent = message;
            new bootstrap.Toast(errorModal).show();
        } else {
            Message.error(message);  // 作为后备方案，使用alert
        }
    }

    function getRequestBody() {
        return {
            md5: AppState.pdfMd5,
            tenantId: AppState.tenantId,
            tenantEnv: AppState.tenantEnv,
            studyNum: AppState.studyNum,
            userId: AppState.userId,
            userName: AppState.userName // 添加 userName
        };
    }

    /**
     * 获取租户信息，优先从 URL 参数中获取，然后是 sessionStorage，最后是 localStorage
     * @returns {Object} - 包含租户信息的对象
     */
    function getTenantInfo() {
        const urlParams = new URLSearchParams(window.location.search);
        let tenantName = urlParams.get('tenantName');
        let tenantId = urlParams.get('tenantId');
        let tenantEnv = urlParams.get('tenantEnv');
        let studyNum = urlParams.get('studyNum');
        let userName = urlParams.get('argusUsername'); // 之前的 userName
        let userId = urlParams.get('userId'); // 之前的 userId

        if (tenantName && tenantId && tenantEnv && studyNum && userName && userId) {
            // 保存到 sessionStorage 和 localStorage
            sessionStorage.setItem('tenantName', tenantName);
            sessionStorage.setItem('tenantId', tenantId);
            sessionStorage.setItem('tenantEnv', tenantEnv);
            sessionStorage.setItem('studyNum', studyNum);
            sessionStorage.setItem('userName', userName); // 保存 userName
            sessionStorage.setItem('userId', userId); // 保存 userId

            updateLocalStorageAndTriggerEvent('tenantName', tenantName);
            updateLocalStorageAndTriggerEvent('tenantId', tenantId);
            updateLocalStorageAndTriggerEvent('tenantEnv', tenantEnv);
            updateLocalStorageAndTriggerEvent('studyNum', studyNum);
            updateLocalStorageAndTriggerEvent('userName', userName); // 更新 userName
            updateLocalStorageAndTriggerEvent('userId', userId); // 更新 userId
        } else if (sessionStorage.getItem('tenantName')) {
            tenantName = sessionStorage.getItem('tenantName');
            tenantId = sessionStorage.getItem('tenantId');
            tenantEnv = sessionStorage.getItem('tenantEnv');
            studyNum = sessionStorage.getItem('studyNum');
            userName = sessionStorage.getItem('userName'); // 从 sessionStorage 获取 userName
            userId = sessionStorage.getItem('userId'); // 从 sessionStorage 获取 userId

            // 同步到 localStorage
            updateLocalStorageAndTriggerEvent('tenantName', tenantName);
            updateLocalStorageAndTriggerEvent('tenantId', tenantId);
            updateLocalStorageAndTriggerEvent('tenantEnv', tenantEnv);
            updateLocalStorageAndTriggerEvent('studyNum', studyNum);
            updateLocalStorageAndTriggerEvent('userName', userName); // 同步 userName
            updateLocalStorageAndTriggerEvent('userId', userId); // 同步 userId
        } else if (localStorage.getItem('tenantName')) {
            tenantName = localStorage.getItem('tenantName');
            tenantId = localStorage.getItem('tenantId');
            tenantEnv = localStorage.getItem('tenantEnv');
            studyNum = localStorage.getItem('studyNum');
            userName = localStorage.getItem('userName'); // 从 localStorage 获取 userName
            userId = localStorage.getItem('userId'); // 从 localStorage 获取 userId

            sessionStorage.setItem('tenantName', tenantName);
            sessionStorage.setItem('tenantId', tenantId);
            sessionStorage.setItem('tenantEnv', tenantEnv);
            sessionStorage.setItem('studyNum', studyNum);
            sessionStorage.setItem('userName', userName); // 保存 userName 到 sessionStorage
            sessionStorage.setItem('userId', userId); // 保存 userId 到 sessionStorage
        } else {
            // 无租户信息，使用默认值
            tenantName = '-';
            tenantId = '-';
            tenantEnv = '-';
            studyNum = '-';
            userName = '-'; // 默认的 userName

            sessionStorage.setItem('tenantName', tenantName);
            sessionStorage.setItem('tenantId', tenantId);
            sessionStorage.setItem('tenantEnv', tenantEnv);
            sessionStorage.setItem('studyNum', studyNum);
            sessionStorage.setItem('userName', userName);
            sessionStorage.setItem('userId', userId);

            updateLocalStorageAndTriggerEvent('tenantName', tenantName);
            updateLocalStorageAndTriggerEvent('tenantId', tenantId);
            updateLocalStorageAndTriggerEvent('tenantEnv', tenantEnv);
            updateLocalStorageAndTriggerEvent('studyNum', studyNum);
            updateLocalStorageAndTriggerEvent('userName', userName);
            updateLocalStorageAndTriggerEvent('userId', userId);
        }

        // 保存到 AppState 中
        AppState.tenantId = tenantId;
        AppState.tenantEnv = tenantEnv;
        AppState.studyNum = studyNum;
        AppState.userName = userName; // 保存 userName 到 AppState
        AppState.userId = userId; // 保存 userId 到 AppState

        return { tenantName, tenantId, tenantEnv, studyNum, userName, userId };
    }

    /**
     * 更新 localStorage 并手动触发一个自定义事件
     * @param {string} key - localStorage 的键
     * @param {string} value - 要设置的值
     */
    function updateLocalStorageAndTriggerEvent(key, value) {
        localStorage.setItem(key, value);
        // 手动触发一个自定义事件
        const event = new Event('localStorageModified');
        window.dispatchEvent(event);
    }

    /**
     * 检查 sessionStorage 和 localStorage 中的租户信息是否一致，如果不一致则刷新页面
     */
    function checkStorageConsistency() {
        const tenantNameSession = sessionStorage.getItem('tenantName');
        const tenantNameLocal = localStorage.getItem('tenantName');

        const tenantIDSession = sessionStorage.getItem('tenantId');
        const tenantIDLocal = localStorage.getItem('tenantId');

        const environmentSession = sessionStorage.getItem('tenantEnv');
        const environmentLocal = localStorage.getItem('tenantEnv');

        const studyNumSession = sessionStorage.getItem('studyNum');
        const studyNumLocal = localStorage.getItem('studyNum');

        const userNameSession = sessionStorage.getItem('userName');
        const userNameLocal = localStorage.getItem('userName');

        const userIdSession = sessionStorage.getItem('userId');
        const userIdLocal = localStorage.getItem('userId');

        if (tenantNameSession !== tenantNameLocal ||
            tenantIDSession !== tenantIDLocal ||
            environmentSession !== environmentLocal ||
            userIdSession !== userIdLocal ||
            userNameSession !== userNameLocal ||
            studyNumSession !== studyNumLocal) {

            // 更新 sessionStorage
            sessionStorage.setItem('tenantName', tenantNameLocal);
            sessionStorage.setItem('tenantId', tenantIDLocal);
            sessionStorage.setItem('tenantEnv', environmentLocal);
            sessionStorage.setItem('studyNum', studyNumLocal);
            sessionStorage.setItem('userId', userIdLocal);
            sessionStorage.setItem('userName', userNameLocal);

            // 更新 URL 参数
            const url = new URL(window.location);
            url.searchParams.set('tenantName', tenantNameLocal);
            url.searchParams.set('tenantId', tenantIDLocal);
            url.searchParams.set('tenantEnv', environmentLocal);
            url.searchParams.set('studyNum', studyNumLocal);
            url.searchParams.set('userId', userIdLocal);
            url.searchParams.set('userName', userNameLocal);

            // 刷新页面，使用更新后的 URL
            window.location.href = url.toString();
        }
    }

    /**
     * 通用的点击事件处理函数，包含前置步骤校验、防重复点击、loading管理
     * @param {HTMLElement} button - 按钮元素
     * @param {Function} handler - 主处理逻辑函数
     * @param {Array} [requiredSteps=[]] - 前置步骤列表
     * @param {number} [delay=2000] - 点击延迟时间，防止多次点击
     */
    function bindButtonWithSteps(button, handler, requiredSteps = [], delay = 2000) {
        button.addEventListener('click', async function() {
            if (button.disabled) return;

            // 禁用按钮并保存原样式
            button.disabled = true;
            const originalClasses = [...button.classList];
            button.className = '';
            button.classList.add('fas', 'fa-spinner', 'fa-spin'); // 添加加载中的样式

            try {
                // 检查前置步骤
                checkPreviousSteps(requiredSteps);

                // 执行主逻辑
                await handler();

            } catch (error) {
                console.error(error.message);
            } finally {
                // 恢复按钮样式和状态
                setTimeout(() => {
                    button.disabled = false;
                    button.className = ''; // 清空所有类
                    button.classList.add(...originalClasses); // 恢复原始样式
                }, delay);
            }
        });
    }

    const stepNames = {
        fileUpload: '文件上传',
        imageRecognition: '图片识别',
        insightGeneration: '报告全息视图生成',
        eventDescription: '事件描述生成',
        structuredData: '结构化数据生成',
        secondaryTransform: '二次数据转换'
    };

    function checkPreviousSteps(requiredSteps) {
        const steps = {
            fileUpload: AppState.pdfMd5 && AppState.pdfTotalPage > 0,
            imageRecognition: AppState.recognitionResults && AppState.recognitionResults.length > 0,
            insightGeneration: AppState.insightData && Object.keys(AppState.insightData).length > 0,
            eventDescription: AppState.eventDescription && AppState.eventDescription.length > 0,
            structuredData: AppState.finalStructuredData && Object.keys(AppState.finalStructuredData).length > 0,
            secondaryTransform: AppState.finalTransformedData && Object.keys(AppState.finalTransformedData).length > 0
        };

        for (const step of requiredSteps) {
            if (!steps[step]) {
                const stepName = stepNames[step] || step; // 若无映射则回退到英文名称
                showErrorInLoadingSpinner(`前置步骤 "${stepName}" 未完成，请先执行此步骤。`);
                throw new Error(`前置步骤 "${stepName}" 未完成`);
            }
        }
    }

    // 在AppState中增加一个属性，用于存储药品字典的markdown数据
    AppState.drugDictionaryData = {
        initialContent: '',
        currentContent: ''
    };

    // 绑定更新按钮点击事件
    document.getElementById('updateDrugDictionaryBtn').addEventListener('click', async function() {
        await saveDrugDictionaryData();
    });

    // 初始化模块选择框，并填充选项
    function populateModuleSelect() {
        const moduleSelect = document.getElementById('moduleSelect');
        moduleSelect.innerHTML = ''; // 清空现有选项

        AppState.modulesPlaceholders.forEach((item) => {
            const option = document.createElement('option');
            option.value = item.module;
            // 使用国际化名称
            option.textContent = AppState.i18n.module[item.module] || item.module;
            moduleSelect.appendChild(option);
        });

        // 触发初始的占位符填充
        populatePlaceholderSelect();
    }

    // 初始化占位符选择框，并填充选项
    function populatePlaceholderSelect() {
        const moduleSelect = document.getElementById('moduleSelect');
        const placeholderSelect = document.getElementById('placeholderSelect');
        placeholderSelect.innerHTML = ''; // 清空现有选项

        const selectedModule = moduleSelect.value;
        const moduleItem = AppState.modulesPlaceholders.find((item) => item.module === selectedModule);

        if (moduleItem) {
            const placeholders = moduleItem.placeholder;
            let placeholderArray = Array.isArray(placeholders) ? placeholders : [placeholders];

            placeholderArray.forEach((placeholder) => {
                const option = document.createElement('option');
                option.value = placeholder;
                // 使用国际化名称
                option.textContent = AppState.i18n.placeholder[placeholder] || placeholder;
                placeholderSelect.appendChild(option);
            });
        }

        // 初始获取数据
        fetchDrugDictionaryData();
    }

    // 定义获取药品字典数据的函数
    async function fetchDrugDictionaryData() {

        const moduleSelect = document.getElementById('moduleSelect');
        const placeholderSelect = document.getElementById('placeholderSelect');
        const selectedModule = moduleSelect.value;
        const selectedPlaceholder = placeholderSelect.value;

        // 根据环境确定使用的 basePath
        const basePath = AppState.settings.environment === 'dev' ? AppState.settings.testEnv : AppState.basePath;
        try {
            const response = await fetch(`${basePath}/pv-model-service-java/config/selectDictMap`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'tenantId': `${AppState.tenantId}`
                },
                body: JSON.stringify({
                    id: `pv_copilot_report_process${AppState.settings.environment === 'uat' ? '' : '_test'}`,
                    module: selectedModule,
                    placeholder: selectedPlaceholder,
                    projectCode: AppState.studyNum,
                    companyCode: AppState.tenantId
                })
            });
            const responseData = await response.json();
            if (!response.ok) {
                throw new Error(`Failed to fetch drug dictionary: ${responseData.message}`);
            }
            const content = responseData.data || '';
            AppState.drugDictionaryData.initialContent = content;
            AppState.drugDictionaryData.currentContent = content;

            // 使用CKEditor展示获取到的markdown数据
            displayDrugDictionaryEditor(content);

        } catch (error) {
            console.error('Error fetching drug dictionary data:', error);
            showError('无法获取药品字典数据。');
        }
    }

    // 显示药品字典编辑器
    function displayDrugDictionaryEditor(content) {
        const container = document.getElementById('drugDictionaryEditorContainer');
        container.innerHTML = ''; // 清空内容
        const editorDiv = document.createElement('div');
        editorDiv.id = 'drugDictionaryEditor';
        container.appendChild(editorDiv);

        const editorConfig = {
            licenseKey: "GPL",
            toolbar: {},
            plugins: [
                AccessibilityHelp,
                Autoformat,
                AutoLink,
                Autosave,
                Bold,
                BalloonToolbar,
                Code,
                CodeBlock,
                Essentials,
                Heading,
                Italic,
                Link,
                Markdown,
                Paragraph,
                PasteFromMarkdownExperimental,
                SelectAll,
                SourceEditing,
                Table,
                TableCaption,
                TableCellProperties,
                TableColumnResize,
                TableProperties,
                TableToolbar,
                TextTransformation,
                Undo
            ],
            balloonToolbar: ['bold', 'italic', '|', 'link', '|'],
            initialData: content,
            placeholder: '在此编辑药品字典内容的Markdown数据...',
            table: {
                contentToolbar: ['tableColumn', 'tableRow', 'mergeTableCells', 'tableProperties', 'tableCellProperties']
            },
            autosave: {
                waitingTime: 2000,
                save(editor) {
                    AppState.drugDictionaryData.currentContent = editor.getData();
                    return true;
                }
            }
        };

        BalloonEditor.create(editorDiv, editorConfig).then(editor => {
            // 记录编辑器实例，以便其他功能使用
            container.editorInstance = editor;
            // 将编辑器实例保存到 AppState
            AppState.drugDictionaryEditorInstance = editor;
            editor.editing.view.document.on('focus', () => {
                AppState.lastActiveEditor = editor;
                AppState.lastActiveTime = Date.now();
            });
        }).catch(error => {
            console.error('Error initializing drug dictionary editor:', error);
        });
    }

    // 保存药品字典数据
    // 修改 saveDrugDictionaryData 函数，确保获取最新的编辑器内容，并在保存前等待自动保存完成
    async function saveDrugDictionaryData() {
        const button = document.getElementById('updateDrugDictionaryBtn');
        const originalText = button.textContent;
        button.disabled = true;
        button.textContent = '保存中...';

        try {
            const editor = AppState.drugDictionaryEditorInstance;
            // 等待任何未完成的自动保存操作
            await editor.plugins.get('Autosave').save();
            // 获取编辑器中的最新内容
            AppState.drugDictionaryData.currentContent = editor.getData();

            // 如果数据没有修改，可直接返回
            if (AppState.drugDictionaryData.currentContent === AppState.drugDictionaryData.initialContent) {
                Message.warning('数据未修改，无需更新。');
                return;
            }

            const moduleSelect = document.getElementById('moduleSelect');
            const placeholderSelect = document.getElementById('placeholderSelect');
            const selectedModule = moduleSelect.value;
            const selectedPlaceholder = placeholderSelect.value;

            const basePath = AppState.settings.environment === 'dev' ? AppState.settings.testEnv : AppState.basePath;
            const response = await fetch(`${basePath}/pv-model-service-java/config/updateDictMap`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'tenantId': `${AppState.tenantId}`
                },
                body: JSON.stringify({
                    id: `pv_copilot_report_process${AppState.settings.environment === 'uat' ? '' : '_test'}`,
                    module: selectedModule,
                    placeholder: selectedPlaceholder,
                    companyCode: AppState.tenantId,
                    projectCode: AppState.studyNum,
                    content: AppState.drugDictionaryData.currentContent.replace(/\\_/g, '_')
                })
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(`Failed to save drug dictionary: ${errorData.detail || response.statusText}`);
            }

            // 更新 initialContent
            AppState.drugDictionaryData.initialContent = AppState.drugDictionaryData.currentContent;
            Message.success('药品字典数据已更新成功！');
        } catch (error) {
            console.error('Error saving drug dictionary data:', error);
            showError('无法保存药品字典数据。');
        } finally {
            button.disabled = false;
            button.textContent = originalText;
        }
    }

    /**
     * 根据当前操作模式更新按钮可见性
     */
    function updateButtonVisibility() {
        const isAuto = isAutoMode();
        // 获取需要在auto模式下隐藏的按钮
        const narrativeButton = document.getElementById('generateEventDescription');
        const structureButton = document.getElementById('secondaryTransform');
        const tenantSelect = document.getElementById('tenantSelect');

        if(isAuto){
            tenantSelect.parentNode.style.display = 'none'; // 隐藏租户选择框
        }else{
            tenantSelect.parentNode.style.display = 'block'; // 显示租户选择框
        }
          

        // 设置按钮显示/隐藏
        if (narrativeButton && structureButton) {
            if (isAuto) {
                narrativeButton.style.display = 'none';
                structureButton.style.display = 'none';
            } else {
                narrativeButton.style.display = '';
                structureButton.style.display = '';
            }
        }

        // 更新配置选项卡可见性
        updateConfigTabsVisibility();
    }

    /**
     * 更新用户角色显示
     */
    function updateUserRoleDisplay() {
        const userRoleElement = document.getElementById('userRole');
        if (userRoleElement) {
            userRoleElement.textContent = AppState.settings.operationMode === 'auto' ? '普通用户' : '管理员';
        }
    }

    /**
     * 更新配置模态框中选项卡的可见性
     */
    function updateConfigTabsVisibility() {
        const isAuto = isAutoMode();

        // 获取配置选项卡元素
        const promptsConfigTab = document.getElementById('promptsConfig-tab');
        const drugDictionaryConfigTab = document.getElementById('drugDictionaryConfig-tab');

        // 设置配置选项卡显示/隐藏
        if (promptsConfigTab && drugDictionaryConfigTab) {
            if (isAuto) {
                // 普通用户模式下隐藏提示词配置和字典配置选项卡
                promptsConfigTab.style.display = 'none';
                drugDictionaryConfigTab.style.display = 'none';

                // 检查当前是否有被隐藏的标签处于激活状态
                if (promptsConfigTab.classList.contains('active') || drugDictionaryConfigTab.classList.contains('active')) {
                    // 将激活状态切换到操作模式标签
                    const operationModeConfigTab = document.getElementById('operationModeConfig-tab');
                    if (operationModeConfigTab) {
                        // 移除所有标签的激活状态
                        document.querySelectorAll('#configTab .nav-link').forEach(tab => {
                            tab.classList.remove('active');
                            tab.setAttribute('aria-selected', 'false');
                        });

                        // 移除所有内容面板的激活状态
                        document.querySelectorAll('#configTabContent .tab-pane').forEach(pane => {
                            pane.classList.remove('show', 'active');
                        });

                        // 激活操作模式标签
                        operationModeConfigTab.classList.add('active');
                        operationModeConfigTab.setAttribute('aria-selected', 'true');

                        // 激活操作模式内容面板
                        const operationModeConfig = document.getElementById('operationModeConfig');
                        if (operationModeConfig) {
                            operationModeConfig.classList.add('show', 'active');
                        }
                    }
                }
            } else {
                // 管理员模式下显示所有选项卡
                promptsConfigTab.style.display = '';
                drugDictionaryConfigTab.style.display = '';
            }
        }
    }


function validate(data, rules) {
  const errors = [];
  
  // 遍历所有规则进行验证
  for (const rule of rules) {
    const { path, message, validater } = rule;
    
    // 使用JSONPath查询匹配的值和路径
    const matches = jsonpath.nodes(data, path);
    
    if (matches.length === 0) {
      // 路径不存在，如果验证不通过，添加错误
      if (!validater(undefined, data, '', rule)) {
        errors.push({
          originalPath: path, 
          message,
          value: undefined
        });
      }
      continue;
    }
    
    // 验证每个匹配的值
    for (const match of matches) { 
      const isValid = validater(match.value, data, match.path.join('.'), rule);
      
      if (!isValid) {
        errors.push({
          path: match.path.join('.'),  
          originalPath: path,  
          message,
          value: match.value
        });
      }
    }
  }
  
  return errors.length > 0 ? errors : null;
}

const schema_rules = [
  {
    path: '$..cdr_lot_no',
    message: 'cdr_lot_no 长度不能超过“35”个字符',
    validater: (value,data, path) => { 
      if(!value) return true;
      return value.length <= 35;
    }
  },
]