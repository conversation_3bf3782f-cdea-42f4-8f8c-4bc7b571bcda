/**
 * 数据对比算法模块
 * 实现三阶段数据对比：初次报告 → 随访报告 → 已录入数据
 */

class DataComparer {
    constructor(keyConfig = {}) {
        this.keyConfig = keyConfig;
        this.changeId = 0;
    }

    /**
     * 执行三阶段数据对比
     * @param {Object} initialReport - 初次报告
     * @param {Object} followupReport - 随访报告  
     * @param {Object} finalData - 已录入数据
     * @returns {Array} 变更列表
     */
    compareThreeStages(initialReport, followupReport, finalData) {
        const changes = [];
        
        // 第一阶段：初次报告 → 随访报告
        const stage1Changes = this.compareObjects(
            initialReport, 
            followupReport, 
            '', 
            'initial_to_followup'
        );
        changes.push(...stage1Changes);

        // 第二阶段：随访报告 → 已录入数据
        const stage2Changes = this.compareObjects(
            followupReport, 
            finalData, 
            '', 
            'followup_to_final'
        );
        changes.push(...stage2Changes);

        return changes;
    }

    /**
     * 比较两个对象
     * @param {Object} obj1 - 源对象
     * @param {Object} obj2 - 目标对象
     * @param {string} basePath - 基础路径
     * @param {string} stage - 对比阶段
     * @returns {Array} 变更列表
     */
    compareObjects(obj1, obj2, basePath = '', stage = '') {
        const changes = [];
        const allKeys = new Set([...Object.keys(obj1 || {}), ...Object.keys(obj2 || {})]);

        for (const key of allKeys) {
            const currentPath = basePath ? `${basePath}.${key}` : key;
            const val1 = obj1?.[key];
            const val2 = obj2?.[key];

            if (val1 === undefined && val2 !== undefined) {
                // 新增字段
                changes.push(this.createChange('ADD', currentPath, null, val2, stage));
            } else if (val1 !== undefined && val2 === undefined) {
                // 删除字段
                changes.push(this.createChange('DELETE', currentPath, val1, null, stage));
            } else if (val1 !== undefined && val2 !== undefined) {
                if (Array.isArray(val1) && Array.isArray(val2)) {
                    // 数组对比
                    const arrayChanges = this.compareArrays(val1, val2, currentPath, stage);
                    changes.push(...arrayChanges);
                } else if (this.isObject(val1) && this.isObject(val2)) {
                    // 对象递归对比
                    const objectChanges = this.compareObjects(val1, val2, currentPath, stage);
                    changes.push(...objectChanges);
                } else if (val1 !== val2) {
                    // 基础值对比
                    changes.push(this.createChange('MODIFY', currentPath, val1, val2, stage));
                }
            }
        }

        return changes;
    }

    /**
     * 比较数组
     * @param {Array} arr1 - 源数组
     * @param {Array} arr2 - 目标数组
     * @param {string} basePath - 基础路径
     * @param {string} stage - 对比阶段
     * @returns {Array} 变更列表
     */
    compareArrays(arr1, arr2, basePath, stage) {
        const changes = [];
        const moduleName = this.getModuleName(basePath);
        const keyFields = this.keyConfig[moduleName]?.keyFields || [];

        if (keyFields.length === 0) {
            // 没有配置主键，按索引对比
            return this.compareArraysByIndex(arr1, arr2, basePath, stage);
        }

        // 基于主键对比
        const map1 = this.createKeyMap(arr1, keyFields);
        const map2 = this.createKeyMap(arr2, keyFields);
        const allKeys = new Set([...map1.keys(), ...map2.keys()]);

        for (const key of allKeys) {
            const item1 = map1.get(key);
            const item2 = map2.get(key);
            // 使用安全的路径生成，避免特殊字符问题
            const safeKey = this.escapePathKey(key);
            const itemPath = `${basePath}[${safeKey}]`;

            if (!item1 && item2) {
                // 新增项
                changes.push(this.createChange('ADD', itemPath, null, item2.data, stage));
            } else if (item1 && !item2) {
                // 删除项
                changes.push(this.createChange('DELETE', itemPath, item1.data, null, stage));
            } else if (item1 && item2) {
                // 修改项
                const itemChanges = this.compareObjects(item1.data, item2.data, itemPath, stage);
                changes.push(...itemChanges);
            }
        }

        return changes;
    }

    /**
     * 按索引比较数组
     * @param {Array} arr1 - 源数组
     * @param {Array} arr2 - 目标数组
     * @param {string} basePath - 基础路径
     * @param {string} stage - 对比阶段
     * @returns {Array} 变更列表
     */
    compareArraysByIndex(arr1, arr2, basePath, stage) {
        const changes = [];
        const maxLength = Math.max(arr1.length, arr2.length);

        for (let i = 0; i < maxLength; i++) {
            const item1 = arr1[i];
            const item2 = arr2[i];
            const itemPath = `${basePath}[${i}]`;

            if (item1 === undefined && item2 !== undefined) {
                changes.push(this.createChange('ADD', itemPath, null, item2, stage));
            } else if (item1 !== undefined && item2 === undefined) {
                changes.push(this.createChange('DELETE', itemPath, item1, null, stage));
            } else if (item1 !== undefined && item2 !== undefined) {
                if (this.isObject(item1) && this.isObject(item2)) {
                    const itemChanges = this.compareObjects(item1, item2, itemPath, stage);
                    changes.push(...itemChanges);
                } else if (item1 !== item2) {
                    changes.push(this.createChange('MODIFY', itemPath, item1, item2, stage));
                }
            }
        }

        return changes;
    }

    /**
     * 创建主键映射
     * @param {Array} array - 数组
     * @param {Array} keyFields - 主键字段
     * @returns {Map} 主键映射
     */
    createKeyMap(array, keyFields) {
        const map = new Map();
        
        array.forEach((item, index) => {
            const key = this.generateKey(item, keyFields) || `__index_${index}`;
            map.set(key, { data: item, index });
        });

        return map;
    }

    /**
     * 生成主键
     * @param {Object} item - 数据项
     * @param {Array} keyFields - 主键字段
     * @returns {string} 主键
     */
    generateKey(item, keyFields) {
        if (!keyFields || keyFields.length === 0) {
            return null;
        }

        const keyValues = keyFields.map(field => {
            const value = this.getNestedValue(item, field);
            return value !== undefined ? String(value) : '';
        });

        return keyValues.join('|');
    }

    /**
     * 获取嵌套对象的值
     * @param {Object} obj - 对象
     * @param {string} path - 路径
     * @returns {*} 值
     */
    getNestedValue(obj, path) {
        return path.split('.').reduce((current, key) => {
            return current && current[key] !== undefined ? current[key] : undefined;
        }, obj);
    }

    /**
     * 获取模块名称
     * @param {string} path - 路径
     * @returns {string} 模块名称
     */
    getModuleName(path) {
        const parts = path.split('.');
        return parts[0] || '';
    }

    /**
     * 创建变更记录
     * @param {string} type - 变更类型
     * @param {string} path - 路径
     * @param {*} oldValue - 旧值
     * @param {*} newValue - 新值
     * @param {string} stage - 阶段
     * @returns {Object} 变更记录
     */
    createChange(type, path, oldValue, newValue, stage) {
        return {
            id: `change_${++this.changeId}`,
            type,
            path,
            oldValue: this.formatValue(oldValue),
            newValue: this.formatValue(newValue),
            stage,
            timestamp: new Date().toISOString(),
            confirmed: false
        };
    }

    /**
     * 格式化值
     * @param {*} value - 值
     * @returns {string} 格式化后的值
     */
    formatValue(value) {
        if (value === null || value === undefined) {
            return '';
        }
        if (Array.isArray(value)) {
            if (value.length === 0) {
                return '空列表';
            }
            // 如果是简单数组（字符串、数字等）
            if (value.every(item => typeof item !== 'object')) {
                return value.join(', ');
            }
            // 如果是对象数组，返回完整的JSON字符串
            return JSON.stringify(value, null, 2);
        }
        if (typeof value === 'object') {
            return JSON.stringify(value, null, 2);
        }
        return String(value);
    }

    /**
     * 转义路径键中的特殊字符
     * @param {string} key - 原始键
     * @returns {string} 转义后的键
     */
    escapePathKey(key) {
        if (!key || typeof key !== 'string') {
            return key;
        }

        // 将特殊字符替换为安全字符
        return key
            .replace(/\[/g, '〔')  // 替换左方括号
            .replace(/\]/g, '〕')  // 替换右方括号
            .replace(/%/g, '％')   // 替换百分号
            .replace(/\./g, '·')   // 替换点号
            .replace(/\|/g, '｜'); // 替换竖线
    }

    /**
     * 反转义路径键中的特殊字符
     * @param {string} key - 转义后的键
     * @returns {string} 原始键
     */
    unescapePathKey(key) {
        if (!key || typeof key !== 'string') {
            return key;
        }

        return key
            .replace(/〔/g, '[')   // 恢复左方括号
            .replace(/〕/g, ']')   // 恢复右方括号
            .replace(/％/g, '%')   // 恢复百分号
            .replace(/·/g, '.')   // 恢复点号
            .replace(/｜/g, '|');  // 恢复竖线
    }

    /**
     * 判断是否为对象
     * @param {*} value - 值
     * @returns {boolean} 是否为对象
     */
    isObject(value) {
        return value !== null && typeof value === 'object' && !Array.isArray(value);
    }

    /**
     * 更新主键配置
     * @param {Object} newConfig - 新配置
     */
    updateKeyConfig(newConfig) {
        this.keyConfig = { ...this.keyConfig, ...newConfig };
    }
}

// 导出到全局
window.DataComparer = DataComparer;
