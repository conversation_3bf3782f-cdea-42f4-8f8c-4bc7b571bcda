var workbook;
// 使用一个对象来存储多个图表实例
var charts = {};
var canvasCounter = 0; // 初始化图表计数器

// 创建Web Worker实例
const worker = new Worker(
  new URL("./workerXlsx.js", document.currentScript.src).href
);

// 接收报告切换后，方案编号更换
chrome.runtime.onMessage.addListener(function (request, sender, sendResponse) {
  if (request.type === "changeStydyNum") {
    updateLocalStorageAndTriggerEvent("studyNum", request.studyNum);
    // 获取当前 URL
    let currentUrl = new URL(window.location.href);

    // 更改 studyNum 参数
    currentUrl.searchParams.set("studyNum", request.studyNum);

    // 使用新的 URL 刷新页面
    window.location.href = currentUrl.toString();
  }
});

document.addEventListener("DOMContentLoaded", function () {
  // 处理租户信息
  getTenantInfo();

  // 监听 localStorage 的变化
  window.addEventListener("localStorageModified", checkStorageConsistency);

  document
    .getElementById("file-upload")
    .addEventListener("change", handleFileUpload);
  const distinctFieldSelect = document.getElementById("distinct-field-select");

  function handleFileUpload(event) {
    const file = event.target.files[0];
    if (file) {
      showLoadingSpinner();
      const reader = new FileReader();
      reader.onload = function (e) {
        const data = e.target.result;
        // 发送数据到Web Worker进行解析
        worker.postMessage(data);
      };
      reader.readAsBinaryString(file);
    }
  }

  // 监听Web Worker的消息
  worker.onmessage = function (event) {
    const { status, data } = event.data;
    if (status === "completed") {
      displayData(data);
      const headers = Array.from(
        document.querySelectorAll("#data-preview thead th")
      ).map((th) => th?.textContent);
      fillDistinctFieldDropdown(headers); // 填充去重字段下拉菜单
      hideLoadingSpinner();
    }
  };

  function showLoadingSpinner() {
    document.getElementById("loading-spinner").style.display = "block";
  }

  function hideLoadingSpinner() {
    document.getElementById("loading-spinner").style.display = "none";
  }

  // 填充去重字段下拉菜单
  function fillDistinctFieldDropdown(headers) {
    distinctFieldSelect.innerHTML = "";
    headers.forEach((header) => {
      const option = document.createElement("option");
      option.value = header;
      option.textContent = header;
      distinctFieldSelect.appendChild(option);
    });
  }

  document
    .getElementById("data-preview")
    .addEventListener("click", function (event) {
      if (event.target.classList.contains("btn-info")) {
        const content = event.target
          .closest("td")
          .querySelector("div")?.textContent;
        showModalDetail(content);
      }
    });

  document
    .getElementById("saveApiKeyBtn")
    .addEventListener("click", function () {
      const apiKeyInput = document.getElementById("apiKeyInput").value;
      const endpointInput = document.getElementById("endpointInput").value;

      if (apiKeyInput && endpointInput) {
        window.localStorage.setItem("azure-api-key", apiKeyInput);
        window.localStorage.setItem("azure-api-url", endpointInput);
        const apiKeyModal = bootstrap.Modal.getInstance(
          document.getElementById("apiKeyModal")
        );
        apiKeyModal.hide();
      } else {
        alert("请填写所有字段");
      }
    });
});
function displayData(data) {
  const container = document.getElementById("table-responsive");
  const table = document.getElementById("data-preview");
  const tbody = table.getElementsByTagName("tbody")[0];
  const thead = table.getElementsByTagName("thead")[0];
  tbody.innerHTML = "";
  thead.innerHTML = "";
  // 创建一个容器来模拟完整数据的高度

  // 渲染表头
  if (data.length > 0) {
    const headerRow = document.createElement("tr");
    data[0].forEach((header) => {
      const th = document.createElement("th");
      const content = document.createElement("div");
      content.style.width = "200px";
      content.style.overflow = "auto";
      content.textContent = header;
      th.appendChild(content);
      headerRow.appendChild(th);
    });
    thead.appendChild(headerRow);
  }

  // 虚拟滚动相关变量
  const rowHeight = 40; // 假设每行高度为40px
  const visibleRows = Math.ceil(window.innerHeight / rowHeight);
  let startIndex = 0;

  const scrollContainer = document.createElement("div");
  scrollContainer.style.minHeight = "300px";
  scrollContainer.style.position = "relative";
  tbody.appendChild(scrollContainer);

  function renderRows() {
    const scrollTop = container.scrollTop;
    startIndex = Math.floor(scrollTop / rowHeight);
    const endIndex = Math.min(startIndex + visibleRows, data.length - 1);

    scrollContainer.innerHTML = "";

    for (let i = startIndex; i < endIndex; i++) {
      const row = data[i + 1];
      const tr = document.createElement("tr");
      tr.style.position = "absolute";
      tr.style.top = `${i * rowHeight}px`;

      data[0].forEach((_, colIndex) => {
        const td = document.createElement("td");
        const content = document.createElement("div");
        content.style.width = "200px";
        content.style.maxHeight = "100px";
        content.style.overflow = "auto";
        content.textContent = row[colIndex] ? row[colIndex] : "";
        td.appendChild(content);

        if (content?.textContent && content?.textContent.length > 150) {
          const detailButton = document.createElement("button");
          detailButton.textContent = "详情";
          detailButton.className = "btn btn-sm btn-info";
          td.appendChild(detailButton);
        }

        tr.appendChild(td);
      });

      scrollContainer.appendChild(tr);
    }
  }

  // 初始渲染
  renderRows();

  // 监听滚动事件
  container.addEventListener("scroll", renderRows);

  // 监听窗口大小变化
  window.addEventListener("resize", () => {
    visibleRows = Math.ceil(window.innerHeight / rowHeight);
    renderRows();
  });
}

// 动态创建并显示模态窗口的函数
function showModalDetail(data) {
  const modalId = "detailMessageModal";
  let modal = document.getElementById(modalId);
  if (!modal) {
    modal = document.createElement("div");
    modal.className = "modal fade";
    modal.id = modalId;
    modal.innerHTML = `
      <div class="modal-dialog modal-xl">
          <div class="modal-content">
              <div class="modal-header">
                  <h5 class="modal-title">详细内容</h5>
                  <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
              </div>
              <div class="modal-body">
                  <div class="modal-table-container table-responsive">
                      <table class="table fixed-header-table">
                          <thead><tr></tr></thead>
                          <tbody></tbody>
                      </table>
                  </div>
              </div>
              <div class="modal-footer">
                  <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
              </div>
          </div>
      </div>`;
    document.body.appendChild(modal);
  }

  // 填充表格内容，假设数据是以逗号分隔的值
  const table = modal.querySelector(".fixed-header-table");
  const thead = table.querySelector("thead");
  const tbody = table.querySelector("tbody");
  thead.innerHTML = "<tr><th>Header</th></tr>"; // 示例头部
  tbody.innerHTML = data
    ?.split("\n")
    .map((line) => `<tr><td>${line}</td></tr>`)
    .join("");

  new bootstrap.Modal(modal).show();
}

const addFilterBtn = document.getElementById("addFilter");
addFilterBtn.addEventListener("click", () => {
  const filterDiv = document.createElement("div");
  filterDiv.className = "filter-container d-flex align-items-center mb-2";

  const select = document.createElement("select");
  select.className = "form-control filter-select me-2";
  const headers = Array.from(
    document.querySelectorAll("#data-preview thead th")
  ).map((th) => th?.textContent);
  headers.forEach((header, index) => {
    const option = document.createElement("option");
    option.value = index;
    option.textContent = header;
    select.appendChild(option);
  });

  const uniqueInputId =
    "tag-input-" + document.querySelectorAll(".filter-input").length;
  const input = document.createElement("input");
  input.id = uniqueInputId;
  input.className = "form-control filter-input flex-grow-1 me-2";

  const orButton = document.createElement("button");
  orButton.textContent = "OR";
  orButton.className = "btn btn-secondary btn-sm me-2";
  orButton.dataset.isActive = "false";
  orButton.onclick = function () {
    this.dataset.isActive =
      this.dataset.isActive === "false" ? "true" : "false";
    this.classList.toggle("btn-success");
    this.classList.toggle("btn-secondary");
  };

  const notMatchBtn = document.createElement("button");
  notMatchBtn.textContent = "Not";
  notMatchBtn.className = "btn btn-warning btn-sm me-2";
  notMatchBtn.dataset.notMatch = "false";
  notMatchBtn.onclick = function () {
    this.dataset.notMatch =
      this.dataset.notMatch === "false" ? "true" : "false";
    this.classList.toggle("btn-outline-warning");
    this.classList.toggle("btn-warning");
  };

  const exactMatchBtn = document.createElement("button");
  exactMatchBtn.textContent = "Exact";
  exactMatchBtn.className = "btn btn-info btn-sm me-2";
  exactMatchBtn.dataset.exactMatch = "false";
  exactMatchBtn.onclick = function () {
    this.dataset.exactMatch =
      this.dataset.exactMatch === "false" ? "true" : "false";
    this.classList.toggle("btn-outline-info");
    this.classList.toggle("btn-info");
  };

  // 修改的部分：Distinct 按钮样式
  const distinctBtn = document.createElement("button");
  distinctBtn.textContent = "Distinct";
  distinctBtn.className = "btn btn-primary btn-sm me-2";
  distinctBtn.dataset.distinct = "false";
  distinctBtn.onclick = function () {
    this.dataset.distinct =
      this.dataset.distinct === "false" ? "true" : "false";
    this.classList.toggle("btn-outline-primary");
    this.classList.toggle("btn-primary");
  };

  const removeBtn = document.createElement("button");
  removeBtn.textContent = "Remove";
  removeBtn.className = "btn btn-danger btn-sm";
  removeBtn.onclick = function () {
    filterDiv.remove();
  };

  filterDiv.appendChild(select);
  filterDiv.appendChild(input);
  filterDiv.appendChild(orButton);
  filterDiv.appendChild(notMatchBtn);
  filterDiv.appendChild(exactMatchBtn);
  filterDiv.appendChild(distinctBtn); // 添加新的按钮
  filterDiv.appendChild(removeBtn);
  document.getElementById("filters-container").appendChild(filterDiv);

  // Initialize Tagify on the newly created input
  input.tagifyInstance = new Tagify(input);
});

document.addEventListener("paste", function (e) {
  var tagifyInput = e.target.closest("tags.tagify");
  if (tagifyInput) {
    var inputElement = tagifyInput.previousElementSibling; // this should be the <input> element Tagify is attached to
    var tagifyInstance =
      inputElement.nextElementSibling.nextElementSibling.tagifyInstance; // Assuming the Tagify instance is stored here
    if (tagifyInstance) {
      e.preventDefault(); // Prevent the default paste action
      var clipboardData = e.clipboardData || window.clipboardData;
      var pastedData = clipboardData.getData("Text");
      tagifyInstance.removeAllTags(); // Clear existing input to avoid duplicating tags
      var tags = pastedData
        ?.split(/\r\n|\r|\n/)
        ?.filter((tag) => tag?.trim() !== "");
      tags.forEach((tag) => {
        tagifyInstance.addTags([tag.trim()]); // Add each tag
        tagifyInstance.update(); // This ensures the internal state is updated
      });
    } else {
      console.error(
        "No Tagify instance found associated with the input element."
      );
    }
  }
});

document.getElementById("process-btn").addEventListener("click", processData);

function processData() {
  const removeNonMatching = document.getElementById(
    "remove-non-matching"
  ).checked;
  const rows = Array.from(document.querySelectorAll("#data-preview tbody tr"));
  const filters = Array.from(
    document.querySelectorAll(".filter-container")
  ).map((div) => ({
    column: parseInt(div.querySelector("select").value, 10),
    filter: new Tagify(div.querySelector("input")).value.map(
      (item) => item.value
    ),
    useOr: div.querySelector('button[data-is-active="true"]') !== null, // Check if the OR button is active
    notMatch: div.querySelector('button[data-not-match="true"]') !== null, // Check if the Not Match button is active
    exactMatch: div.querySelector('button[data-exact-match="true"]') !== null, // Check if the Exact Match button is active
    matchDistinct: div.querySelector('button[data-distinct="true"]') !== null, // Check if match distinct is active
  }));

  // 普通筛选
  let filteredRows = rows.filter((row) => {
    const orFilters = filters.filter((f) => f.useOr && !f.matchDistinct);
    const andFilters = filters.filter((f) => !f.useOr && !f.matchDistinct);

    const orValid =
      orFilters.length === 0 ||
      orFilters.some((f) => {
        const cellText = row.cells[f.column]?.textContent;
        return (
          f.filter.some((filter) =>
            f.exactMatch ? cellText === filter : cellText.includes(filter)
          ) !== f.notMatch
        );
      });

    const andValid =
      andFilters.length === 0 ||
      andFilters.every((f) => {
        const cellText = row.cells[f.column]?.textContent;
        return (
          f.filter.some((filter) =>
            f.exactMatch ? cellText === filter : cellText.includes(filter)
          ) !== f.notMatch
        );
      });

    return orValid && andValid;
  });

  // distinct 筛选
  const distinctFields = Array.from(
    document.getElementById("distinct-field-select").selectedOptions
  ).map((option) => option.value);
  const headers = Array.from(
    document.querySelectorAll("#data-preview thead th")
  ).map((th) => th?.textContent);
  const distinctIndexes = distinctFields.map((field) => headers.indexOf(field)); // 获取所有去重字段的索引

  const distinctSets = new Map();
  filteredRows.forEach((row) => {
    const key = distinctIndexes
      .map((index) => row.cells[index]?.textContent)
      .join("|");
    if (!distinctSets.has(key)) {
      distinctSets.set(key, []);
    }
    distinctSets.get(key).push(row);
  });

  let finalFilteredRows = [];
  Array.from(distinctSets.values()).forEach((set) => {
    const allMatchDistinct = filters
      .filter((f) => f.matchDistinct)
      .every((f) => {
        return set.every((row) => {
          const cellText = row.cells[f.column]?.textContent;
          const matches = f.filter.some((filter) =>
            f.exactMatch ? cellText === filter : cellText.includes(filter)
          );

          if (f.notMatch) {
            return !matches;
          } else {
            return matches;
          }
        });
      });

    if (allMatchDistinct) {
      finalFilteredRows = finalFilteredRows.concat(set);
    }
  });

  if (removeNonMatching) {
    finalFilteredRows = finalFilteredRows.map((row) => {
      const newRow = row.cloneNode(true);
      filters.forEach((filter) => {
        const cell = newRow.cells[filter.column];
        if (cell && cell?.textContent.includes("\n")) {
          const lines = cell?.textContent?.split("\n");
          const filteredLines = lines.filter((line) =>
            filter.filter.some((f) =>
              filter.exactMatch ? line === f : line.includes(f)
            )
          );
          cell.textContent = filteredLines.join("\n");
        }
      });
      return newRow;
    });
  }

  const tbody = document.querySelector("#data-preview tbody");
  tbody.innerHTML = "";
  finalFilteredRows.forEach((row) => tbody.appendChild(row));

  document.getElementById("download-btn").style.display = "inline-block";
}

document.getElementById("download-btn").addEventListener("click", function () {
  const filteredData = Array.from(
    document.querySelectorAll("#data-preview tbody tr")
  ).map((tr) => Array.from(tr.cells).map((td) => td?.textContent));
  const headers = Array.from(
    document.querySelectorAll("#data-preview thead th")
  ).map((th) => th?.textContent);

  const newSheet = XLSX.utils.aoa_to_sheet([headers, ...filteredData]);
  const newWorkbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(newWorkbook, newSheet, "Filtered Data");
  XLSX.writeFile(newWorkbook, "filtered_data.xlsx");
});

function fillDropdowns(rowSelect, colSelect) {
  const headers = Array.from(
    document.querySelectorAll("#data-preview thead th")
  ).map((th) => th?.textContent);
  headers.forEach((header) => {
    const rowOption = document.createElement("option");
    const colOption = document.createElement("option");
    rowOption.value = header;
    colOption.value = header;
    rowOption.textContent = header;
    colOption.textContent = header;
    rowSelect.appendChild(rowOption);
    colSelect.appendChild(colOption);
  });
}

function generateStatistics(statsContainer) {
  const rowSelect = statsContainer.querySelector(
    ".statistics-select:nth-of-type(1)"
  );
  const colSelect = statsContainer.querySelector(
    ".statistics-select:nth-of-type(2)"
  );
  const canvas = statsContainer.querySelector("canvas");
  const rowField = rowSelect.value;
  const colField = colSelect.value;
  const distinctFields = Array.from(
    document.getElementById("distinct-field-select").selectedOptions
  ).map((option) => option.value); // 获取所有选中的去重字段

  const dataRows = Array.from(
    document.querySelectorAll("#data-preview tbody tr")
  );
  const headers = Array.from(
    document.querySelectorAll("#data-preview thead th")
  ).map((th) => th?.textContent);
  const rowIndex = headers.indexOf(rowField);
  const colIndex = headers.indexOf(colField);
  const distinctIndexes = distinctFields.map((field) => headers.indexOf(field)); // 获取所有去重字段的索引

  const distinctSet = new Set(); // Store distinct field values to avoid duplicate counts
  const stats = {};
  const rowTotals = {};
  const colTotals = {};

  dataRows.forEach((row) => {
    const rowData = row.cells[rowIndex]?.textContent;
    const colData = row.cells[colIndex]?.textContent;
    const distinctData = distinctIndexes
      .map((index) => row.cells[index]?.textContent)
      .join("|"); // 用 | 分隔符连接所有去重字段的值

    if (!distinctSet.has(distinctData)) {
      distinctSet.add(distinctData);
      stats[rowData] = stats[rowData] || {};
      stats[rowData][colData] = (stats[rowData][colData] || 0) + 1;
      rowTotals[rowData] = (rowTotals[rowData] || 0) + 1;
      colTotals[colData] = (colTotals[colData] || 0) + 1;
    }
  });

  const rowKeys = Object.keys(stats).sort();
  const colKeys = Object.keys(colTotals).sort();

  // Correctly reference the resultTable variable instead of 'table'
  const resultTable = statsContainer.querySelector("table");
  const thead = resultTable.querySelector("thead tr");
  const tbody = resultTable.querySelector("tbody");

  thead.innerHTML =
    `<th>${rowField} \\ ${colField}</th>` +
    colKeys.map((key) => `<th>${key}</th>`).join("") +
    "<th>Total</th>";
  // 在 generateStatistics 函数中修改tbody的innerHTML来添加 data-row-key 和 data-col-key
  // 修改 total 行和列的生成方式
  tbody.innerHTML = rowKeys
    .map((rowKey) => {
      const cells = colKeys
        .map((colKey) => {
          return `<td data-row-key="${rowField}" data-col-key="${colField}" data-row-value="${rowKey}" data-col-value="${colKey}" class="stat-cell">${
            stats[rowKey][colKey] || 0
          }</td>`;
        })
        .join("");
      const rowTotal = rowTotals[rowKey] || 0;
      return `<tr><th>${rowKey}</th>${cells}<th class="stat-cell-total" data-row-key="${rowField}" data-col-key="Total" data-row-value="${rowKey}" data-col-value="all">${rowTotal}</th></tr>`;
    })
    .join("");

  const totalCells = colKeys
    .map(
      (colKey) =>
        `<td class="stat-cell-total" data-row-key="Total" data-col-key="${colField}" data-row-value="all" data-col-value="${colKey}">${colTotals[colKey]}</td>`
    )
    .join("");
  const grandTotal = Object.values(colTotals).reduce((a, b) => a + b, 0);
  tbody.innerHTML += `<tr><th>Total</th>${totalCells}<th class="stat-cell-total" data-row-key="Total" data-col-key="Total" data-row-value="all" data-col-value="all">${grandTotal}</th></tr>`;

  // Update the chart
  updateChart(canvas, rowKeys, colKeys, stats, rowTotals);
}

document.addEventListener("DOMContentLoaded", function () {
  // 监听所有 .stat-cell 的点击事件
  document
    .getElementById("statistics-views")
    .addEventListener("click", function (event) {
      if (
        event.target.classList.contains("stat-cell") ||
        event.target.classList.contains("stat-cell-total")
      ) {
        const cell = event.target;
        const rowKey = cell.dataset.rowValue;
        const colKey = cell.dataset.colValue;
        const fieldNameRow = cell.dataset.rowKey;
        const fieldNameCol = cell.dataset.colKey;
        displayDetails(fieldNameRow, rowKey, fieldNameCol, colKey);
      }
    });
});

const addStatisticsSelectorBtn = document.getElementById(
  "addStatisticsSelector"
);
addStatisticsSelectorBtn.addEventListener("click", () => {
  const addStatisticsSelectorBtn = document.getElementById(
    "addStatisticsSelector"
  );
  addStatisticsSelectorBtn.addEventListener("click", () => {
    const container = document.getElementById("statistics-views");
    const statsContainerId = "stats-container-" + canvasCounter;
    const statsContainer = document.createElement("div");
    statsContainer.className = "statistics-container mb-3 accordion-item";
    statsContainer.id = statsContainerId;

    const headerId = "heading-" + canvasCounter;
    const collapseId = "collapse-" + canvasCounter;

    statsContainer.innerHTML = `
        <h2 class="accordion-header" id="${headerId}">
            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#${collapseId}" aria-expanded="true" aria-controls="${collapseId}">
                Statistics View ${canvasCounter + 1}
            </button>
        </h2>
        <div id="${collapseId}" class="accordion-collapse collapse show" aria-labelledby="${headerId}" data-bs-parent="#statistics-views">
            <div class="accordion-body">
                <select class="form-control mb-2 statistics-select" id="row-select-${canvasCounter}"></select>
                <select class="form-control mb-2 statistics-select" id="col-select-${canvasCounter}"></select>
                <button class="btn btn-primary mb-3" id="generateStatistics-${statsContainerId}">Generate Statistics</button>
                <table class="table table-bordered">
                    <thead><tr></tr></thead>
                    <tbody></tbody>
                </table>
                <div class="chart-container">
                    <canvas id="chart-${canvasCounter}"></canvas>
                </div>
            </div>
        </div>
      `;

    container.appendChild(statsContainer);

    const generateStatisticsBtn = document.getElementById(
      `generateStatistics-${statsContainerId}`
    );
    generateStatisticsBtn.addEventListener("click", () => {
      generateStatistics(document.getElementById(statsContainerId));
    });

    const rowSelect = statsContainer.querySelector(
      `#row-select-${canvasCounter}`
    );
    const colSelect = statsContainer.querySelector(
      `#col-select-${canvasCounter}`
    );

    fillDropdowns(rowSelect, colSelect);

    // 监听统计视图的维度选择变化，动态设置面板标题
    rowSelect.addEventListener("change", updateStatsTitle);
    colSelect.addEventListener("change", updateStatsTitle);

    canvasCounter++;
  });
  const container = document.getElementById("statistics-views");
  const statsContainerId = "stats-container-" + canvasCounter;
  const statsContainer = document.createElement("div");
  statsContainer.className = "statistics-container mb-3 accordion-item";
  statsContainer.id = statsContainerId;

  const headerId = "heading-" + canvasCounter;
  const collapseId = "collapse-" + canvasCounter;

  statsContainer.innerHTML = `
    <h2 class="accordion-header" id="${headerId}">
        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#${collapseId}" aria-expanded="true" aria-controls="${collapseId}">
            Statistics View ${canvasCounter + 1}
        </button>
    </h2>
    <div id="${collapseId}" class="accordion-collapse collapse show" aria-labelledby="${headerId}" data-bs-parent="#statistics-views">
        <div class="accordion-body">
            <select class="form-control mb-2 statistics-select" id="row-select-${canvasCounter}"></select>
            <select class="form-control mb-2 statistics-select" id="col-select-${canvasCounter}"></select>
            <button class="btn btn-primary mb-3" id="generateStatistics-${statsContainerId}">Generate Statistics</button>
            <table class="table table-bordered">
                <thead><tr></tr></thead>
                <tbody></tbody>
            </table>
            <div class="chart-container">
                <canvas id="chart-${canvasCounter}"></canvas>
            </div>
        </div>
    </div>
  `;

  container.appendChild(statsContainer);

  const generateStatisticsBtn = document.getElementById(
    `generateStatistics-${statsContainerId}`
  );
  generateStatisticsBtn.addEventListener("click", () => {
    generateStatistics(document.getElementById(statsContainerId));
  });

  const rowSelect = statsContainer.querySelector(
    `#row-select-${canvasCounter}`
  );
  const colSelect = statsContainer.querySelector(
    `#col-select-${canvasCounter}`
  );

  fillDropdowns(rowSelect, colSelect);

  // 监听统计视图的维度选择变化，动态设置面板标题
  rowSelect.addEventListener("change", updateStatsTitle);
  colSelect.addEventListener("change", updateStatsTitle);

  canvasCounter++;
});

function updateStatsTitle() {
  const containers = document.querySelectorAll(".statistics-container");
  containers.forEach((container) => {
    const rowSelect = container.querySelector(
      ".statistics-select:nth-of-type(1)"
    );
    const colSelect = container.querySelector(
      ".statistics-select:nth-of-type(2)"
    );
    const button = container.querySelector(".accordion-button");

    if (rowSelect && colSelect && button) {
      const rowValue = rowSelect.options[rowSelect.selectedIndex]?.textContent;
      const colValue = colSelect.options[colSelect.selectedIndex]?.textContent;
      button.textContent = `${rowValue} \\ ${colValue}`;
    }
  });
}

document.addEventListener("DOMContentLoaded", function () {
  const initialStatsViews = document.querySelectorAll(".statistics-container");
  initialStatsViews.forEach((container) => {
    const rowSelect = container.querySelector(
      ".statistics-select:nth-of-type(1)"
    );
    const colSelect = container.querySelector(
      ".statistics-select:nth-of-type(2)"
    );
    rowSelect.addEventListener("change", updateStatsTitle);
    colSelect.addEventListener("change", updateStatsTitle);
  });
});

function updateChart(canvas, rowKeys, colKeys, stats, rowTotals) {
  const ctx = canvas.getContext("2d");
  const canvasId = canvas.id;

  if (charts[canvasId]) {
    charts[canvasId].destroy();
  }

  const labels = rowKeys;
  const datasets = colKeys.map((colKey) => {
    return {
      label: colKey,
      data: rowKeys.map((rowKey) => stats[rowKey][colKey] || 0),
      backgroundColor: randomColor(),
      borderColor: randomColor(),
      borderWidth: 1,
    };
  });

  // 存储新的图表实例到charts对象
  charts[canvasId] = new Chart(ctx, {
    type: "bar",
    data: {
      labels: labels,
      datasets: datasets,
    },
    options: {
      scales: {
        y: {
          beginAtZero: true,
        },
      },
    },
  });
}

function randomColor() {
  return `rgb(${Math.floor(Math.random() * 255)}, ${Math.floor(
    Math.random() * 255
  )}, ${Math.floor(Math.random() * 255)})`;
}

function displayDetails(
  fieldNameRow,
  fieldValueRow,
  fieldNameCol,
  fieldValueCol
) {
  const dataRows = Array.from(
    document.querySelectorAll("#data-preview tbody tr")
  );
  const headers = Array.from(
    document.querySelectorAll("#data-preview thead th")
  ).map((th) => th?.textContent);

  let matchingRows;
  // 特别处理 "Total" 行和列
  if (fieldValueRow === "all" || fieldValueCol === "all") {
    matchingRows = dataRows.filter((row) => {
      const rowMatch =
        fieldValueRow === "all" ||
        row.cells[headers.indexOf(fieldNameRow)].textContent.trim() ===
          fieldValueRow;
      const colMatch =
        fieldValueCol === "all" ||
        row.cells[headers.indexOf(fieldNameCol)].textContent.trim() ===
          fieldValueCol;
      return rowMatch && colMatch;
    });
  } else {
    // 正常的单元格处理
    matchingRows = dataRows.filter((row) => {
      const rowValue =
        row.cells[headers.indexOf(fieldNameRow)]?.textContent.trim();
      const colValue =
        row.cells[headers.indexOf(fieldNameCol)]?.textContent.trim();
      return rowValue === fieldValueRow && colValue === fieldValueCol;
    });
  }

  const modalBody = matchingRows
    .map(
      (row) =>
        `<tr>${Array.from(row.cells)
          .map(
            (td) =>
              `<td><div style="max-height: 100px; overflow-y: auto;">${td?.textContent}</div></td>`
          )
          .join("")}</tr>`
    )
    .join("");
  showModal(fieldNameRow, fieldNameCol, modalBody, headers);
}

function showModal(fieldNameRow, fieldNameCol, modalBody, headers) {
  const existingModal = document.getElementById("detailModal");
  if (existingModal) {
    // Ensures the modal is fully removed from the DOM
    existingModal.parentNode.removeChild(existingModal);
  }

  const headerHtml = headers.map((header) => `<th>${header}</th>`).join("");

  const modalHtml = `
<div class="modal fade" id="detailModal" tabindex="-1" aria-labelledby="detailModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-xl">
      <div class="modal-content">
          <div class="modal-header">
              <h5 class="modal-title" id="detailModalLabel">Detailed Data for ${fieldNameRow} - ${fieldNameCol}</h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
              <div class="modal-table-container">
                  <table class="fixed-header-table">
                      <thead><tr>${headerHtml}</tr></thead>
                      <tbody>${modalBody}</tbody>
                  </table>
              </div>
          </div>
          <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
          </div>
      </div>
  </div>
</div>
`;

  document.body.insertAdjacentHTML("beforeend", modalHtml);
  new bootstrap.Modal(document.getElementById("detailModal")).show();
}

document
  .getElementById("generate-report-btn")
  .addEventListener("click", function () {
    generateReport();
  });

function generateReport() {
  const statsTables = document.querySelectorAll("#statistics-views table");
  if (statsTables.length === 0) {
    document.getElementById("original-textarea").value =
      "No statistics available to generate a report.";
    return;
  }

  const today = new Date().toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });
  let reportContent = `Detailed Analysis Report:\nOn ${today}, the following PT data were analyzed across multiple views:\n\n`;

  // 用于存储所有统计数据
  let allData = {};

  // 只处理第一个统计视图中的总数
  const primaryTable = statsTables[0];
  const primaryRows = Array.from(primaryTable.querySelectorAll("tbody tr"));
  const primaryLastRow = primaryRows.pop(); // 提取最后一行（汇总）

  // 获取表头内容
  const primaryHeaders = Array.from(primaryTable.querySelectorAll("thead th"))
    .slice(1, -1)
    .map((th) => th?.textContent);

  // 解析统计维度名称
  const primaryStatDimensionCell =
    primaryTable.querySelector("thead th")?.textContent;
  const primaryStatDimension = primaryStatDimensionCell?.split("\\")[1].trim();

  primaryRows.forEach((row) => {
    const pt = row.cells[0]?.textContent.trim();
    if (pt === "Total") return; // 跳过汇总行
    const details = Array.from(row.cells)
      .slice(1, -1)
      .map((cell, index) => ({
        header: primaryHeaders[index],
        value: parseFloat(cell?.textContent.trim()),
      }));

    if (!allData[pt]) {
      allData[pt] = {
        total: 0,
        ageGroups: new Set(),
        otherStats: {},
      };
    }

    // 将当前视图的数据和表头添加到总数据中
    details.forEach((detail) => {
      if (detail.value > 0) {
        allData[pt].total += detail.value;
        if (detail.header.includes("岁")) {
          allData[pt].ageGroups.add(parseInt(detail.header));
        } else {
          if (!allData[pt].otherStats[primaryStatDimension]) {
            allData[pt].otherStats[primaryStatDimension] = {};
          }
          allData[pt].otherStats[primaryStatDimension][detail.header] =
            (allData[pt].otherStats[primaryStatDimension][detail.header] || 0) +
            detail.value;
        }
      }
    });
  });

  // 解析最后一行汇总数据
  primaryRows.push(primaryLastRow);
  const ptTotalData = {};
  Array.from(primaryLastRow.cells)
    .slice(1, -1)
    .forEach((cell, index) => {
      const value = parseFloat(cell?.textContent.trim());
      ptTotalData[primaryHeaders[index]] = value;
    });

  // 处理剩余的统计视图
  statsTables.forEach((table, tableIndex) => {
    if (tableIndex === 0) return; // 跳过第一个统计视图

    const rows = Array.from(table.querySelectorAll("tbody tr"));

    // 获取表头内容
    const headers = Array.from(table.querySelectorAll("thead th"))
      .slice(1, -1)
      .map((th) => th?.textContent);

    // 解析统计维度名称
    const statDimensionCell = table.querySelector("thead th")?.textContent;
    const statDimension = statDimensionCell?.split("\\")[1].trim();

    rows.forEach((row) => {
      const pt = row.cells[0]?.textContent.trim();
      if (pt === "Total") return; // 跳过汇总行
      const details = Array.from(row.cells)
        .slice(1, -1)
        .map((cell, index) => ({
          header: headers[index],
          value: parseFloat(cell?.textContent.trim()),
        }));

      if (!allData[pt]) {
        allData[pt] = {
          total: 0,
          ageGroups: new Set(),
          otherStats: {},
        };
      }

      // 将当前视图的数据和表头添加到总数据中
      details.forEach((detail) => {
        if (detail.value > 0) {
          if (detail.header.includes("岁")) {
            allData[pt].ageGroups.add(parseInt(detail.header));
          } else {
            if (!allData[pt].otherStats[statDimension]) {
              allData[pt].otherStats[statDimension] = {};
            }
            allData[pt].otherStats[statDimension][detail.header] =
              (allData[pt].otherStats[statDimension][detail.header] || 0) +
              detail.value;
          }
        }
      });
    });
  });

  // 格式化输出每个PT及其对应的统计数据
  Object.entries(allData).forEach(([pt, data]) => {
    let ageGroupsArray = Array.from(data.ageGroups).sort((a, b) => a - b);
    if (ageGroupsArray.length === 1) {
      reportContent += `PT: ${pt}，总共产生了${data.total}条数据\n`;
      reportContent += `年龄均为${ageGroupsArray[0]}岁\n`;
    } else {
      let ageRange =
        ageGroupsArray.length > 0
          ? `${ageGroupsArray[0]}岁 to ${
              ageGroupsArray[ageGroupsArray.length - 1]
            }岁`
          : "无年龄数据";
      reportContent += `PT: ${pt}，总共产生了${data.total}条数据\n`;
      reportContent += `年龄范围: ${ageRange}\n`;
    }

    // 输出其他统计维度
    Object.entries(data.otherStats).forEach(([statName, statData]) => {
      const statEntries = Object.entries(statData);
      if (statEntries.length === 1) {
        // 只有一种详细数据，采用修改后的话术
        const [value, count] = statEntries[0];
        reportContent += `${statName}均为${value}\n`;
      } else {
        // 多种详细数据，采用原有格式
        reportContent += `${statName}分布如下:\n`;
        statEntries.forEach(([value, count]) => {
          reportContent += `  ${value}: ${count}\n`;
        });
      }
    });

    reportContent += "\n"; // 添加空行以分隔不同的PT
  });

  // 设置textarea的内容
  document.getElementById("original-textarea").value = reportContent;
}

const saveSettingsBtn = document.getElementById("saveSettings");
saveSettingsBtn.addEventListener("click", () => {
  const filters = Array.from(
    document.querySelectorAll(".filter-container")
  ).map((div) => ({
    column: parseInt(div.querySelector("select").value, 10),
    filter: new Tagify(div.querySelector("input")).value.map(
      (item) => item.value
    ),
    useOr: div.querySelector('button[data-is-active="true"]') !== null,
    notMatch: div.querySelector('button[data-not-match="true"]') !== null,
    exactMatch: div.querySelector('button[data-exact-match="true"]') !== null,
    matchDistinct: div.querySelector('button[data-distinct="true"]') !== null, // Check if Distinct button is active
  }));

  const statistics = Array.from(
    document.querySelectorAll(".statistics-container")
  ).map((container) => ({
    row: container.querySelector(".statistics-select:nth-of-type(1)").value,
    col: container.querySelector(".statistics-select:nth-of-type(2)").value,
  }));

  const distinctFields = Array.from(
    document.getElementById("distinct-field-select").selectedOptions
  ).map((option) => option.value);

  const settings = {
    filters: filters,
    statistics: statistics,
    removeNonMatching: document.getElementById("remove-non-matching").checked,
    distinctFields: distinctFields,
  };

  const blob = new Blob([JSON.stringify(settings, null, 2)], {
    type: "application/json",
  });
  saveAs(blob, "settings.json");
});

const loadSettingsBtn = document.getElementById("loadSettings");
loadSettingsBtn.addEventListener("click", () => {
  document.getElementById("load-settings-input").click();
});

document
  .getElementById("load-settings-input")
  .addEventListener("change", function (event) {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = function (e) {
        const settings = JSON.parse(e.target.result);
        applySettings(settings);
      };
      reader.readAsText(file);
    }
  });

function applySettings(settings) {
  document.getElementById("filters-container").innerHTML =
    '<button class="btn btn-secondary mb-3" onclick="addFilter()">Add Filter</button>';
  document.getElementById("statistics-views").innerHTML = "";

  settings.filters.forEach((filter) => {
    addFilter();
    const lastFilterDiv =
      document.querySelectorAll(".filter-container").length - 1;
    const filterDiv =
      document.querySelectorAll(".filter-container")[lastFilterDiv];
    filterDiv.querySelector("select").value = filter.column;
    const tagifyInstance = new Tagify(filterDiv.querySelector("input"));
    tagifyInstance.addTags(filter.filter);
    if (filter.useOr) {
      filterDiv.querySelector('button[data-is-active="false"]').click();
    }
    if (filter.notMatch) {
      filterDiv.querySelector('button[data-not-match="false"]').click();
    }
    if (filter.exactMatch) {
      filterDiv.querySelector('button[data-exact-match="false"]').click();
    }
    if (filter.matchDistinct) {
      filterDiv.querySelector('button[data-distinct="false"]').click(); // Restore Distinct button state
    }
  });

  settings.statistics.forEach((stat) => {
    addStatisticsSelector();
    const lastStatsContainer =
      document.querySelectorAll(".statistics-container").length - 1;
    const statsContainer = document.querySelectorAll(".statistics-container")[
      lastStatsContainer
    ];
    statsContainer.querySelector(".statistics-select:nth-of-type(1)").value =
      stat.row;
    statsContainer.querySelector(".statistics-select:nth-of-type(2)").value =
      stat.col;
    generateStatistics(statsContainer);
  });

  const distinctFieldSelect = document.getElementById("distinct-field-select");
  Array.from(distinctFieldSelect.options).forEach((option) => {
    if (settings.distinctFields.includes(option.value)) {
      option.selected = true;
    }
  });

  // 应用移除不匹配行的选项
  document.getElementById("remove-non-matching").checked =
    settings.removeNonMatching;

  // 触发数据处理
  processData();

  // 调用 updateStatsTitle 更新标题
  updateStatsTitle();

  document.querySelectorAll(".statistics-container").forEach((container) => {
    generateStatistics(container);
  });
}

document
  .getElementById("translate-report-btn")
  .addEventListener("click", function () {
    translateReport();
  });

// 从本地缓存中获取azure openai的访问授权
const apiKey = window.localStorage.getItem("azure-api-key");
const endpoint = window.localStorage.getItem("azure-api-url");

async function translateReport() {
  const reportText = document.getElementById("original-textarea").value;

  if (!reportText) {
    alert("Please generate the report first.");
    return;
  }

  // 检查是否设置了 API Key 和 Endpoint
  if (!apiKey || !endpoint) {
    new bootstrap.Modal(document.getElementById("apiKeyModal")).show();
    return;
  }

  const response = await fetch(endpoint, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Api-Key": `${apiKey}`,
      Accept: "text/event-stream",
    },
    body: JSON.stringify({
      messages: [
        {
          role: "system",
          content: "你是一个翻译机器人，用户传入中文，你返回对应的英文信息",
        },
        {
          role: "user",
          content: reportText,
        },
      ],
      stream: true,
      max_tokens: 800,
      temperature: 0.7,
      frequency_penalty: 0,
      presence_penalty: 0,
      top_p: 0.95,
      stop: null,
    }),
  });

  if (!response.ok) {
    console.error("Translation request failed", response.statusText);
    alert("Translation failed. Please check the console for more details.");
    return;
  }

  const reader = response.body.getReader();
  const decoder = new TextDecoder("utf-8");
  let translatedText = "";

  const processStream = async () => {
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      const chunk = decoder.decode(value, { stream: true });
      const lines = chunk?.split("\n")?.filter((line) => line?.trim() !== "");
      for (const line of lines) {
        if (line.startsWith("data: ")) {
          const jsonString = line.slice(6);
          try {
            const json = JSON.parse(jsonString);
            const content = json.choices?.[0]?.delta?.content;
            if (content) {
              translatedText += content;
              document.getElementById("translated-textarea").value =
                translatedText;
            }
          } catch (error) {
            console.error("Error parsing JSON:", error);
          }
        }
      }
    }
  };

  await processStream();
}

/**
 * 获取租户信息，优先从 URL 参数中获取，然后是 sessionStorage，最后是 localStorage
 * @returns {Object} - 包含租户信息的对象
 */
function getTenantInfo() {
  const urlParams = new URLSearchParams(window.location.search);
  let tenantName = urlParams.get("tenantName");
  let tenantId = urlParams.get("tenantId");
  let tenantEnv = urlParams.get("tenantEnv");
  let studyNum = urlParams.get("studyNum");
  let userName = urlParams.get("argusUsername"); // 之前的 userName
  let userId = urlParams.get("userId"); // 之前的 userId

  if (tenantName && tenantId && tenantEnv && studyNum && userName && userId) {
    // 保存到 sessionStorage 和 localStorage
    sessionStorage.setItem("tenantName", tenantName);
    sessionStorage.setItem("tenantId", tenantId);
    sessionStorage.setItem("tenantEnv", tenantEnv);
    sessionStorage.setItem("studyNum", studyNum);
    sessionStorage.setItem("userName", userName); // 保存 userName
    sessionStorage.setItem("userId", userId); // 保存 userId

    updateLocalStorageAndTriggerEvent("tenantName", tenantName);
    updateLocalStorageAndTriggerEvent("tenantId", tenantId);
    updateLocalStorageAndTriggerEvent("tenantEnv", tenantEnv);
    updateLocalStorageAndTriggerEvent("studyNum", studyNum);
    updateLocalStorageAndTriggerEvent("userName", userName); // 更新 userName
    updateLocalStorageAndTriggerEvent("userId", userId); // 更新 userId
  } else if (sessionStorage.getItem("tenantName")) {
    tenantName = sessionStorage.getItem("tenantName");
    tenantId = sessionStorage.getItem("tenantId");
    tenantEnv = sessionStorage.getItem("tenantEnv");
    studyNum = sessionStorage.getItem("studyNum");
    userName = sessionStorage.getItem("userName"); // 从 sessionStorage 获取 userName
    userId = sessionStorage.getItem("userId"); // 从 sessionStorage 获取 userId

    // 同步到 localStorage
    updateLocalStorageAndTriggerEvent("tenantName", tenantName);
    updateLocalStorageAndTriggerEvent("tenantId", tenantId);
    updateLocalStorageAndTriggerEvent("tenantEnv", tenantEnv);
    updateLocalStorageAndTriggerEvent("studyNum", studyNum);
    updateLocalStorageAndTriggerEvent("userName", userName); // 同步 userName
    updateLocalStorageAndTriggerEvent("userId", userId); // 同步 userId
  } else if (localStorage.getItem("tenantName")) {
    tenantName = localStorage.getItem("tenantName");
    tenantId = localStorage.getItem("tenantId");
    tenantEnv = localStorage.getItem("tenantEnv");
    studyNum = localStorage.getItem("studyNum");
    userName = localStorage.getItem("userName"); // 从 localStorage 获取 userName
    userId = localStorage.getItem("userId"); // 从 localStorage 获取 userId

    sessionStorage.setItem("tenantName", tenantName);
    sessionStorage.setItem("tenantId", tenantId);
    sessionStorage.setItem("tenantEnv", tenantEnv);
    sessionStorage.setItem("studyNum", studyNum);
    sessionStorage.setItem("userName", userName); // 保存 userName 到 sessionStorage
    sessionStorage.setItem("userId", userId); // 保存 userId 到 sessionStorage
  } else {
    // 无租户信息，使用默认值
    tenantName = "-";
    tenantId = "-";
    tenantEnv = "-";
    studyNum = "-";
    userName = "-"; // 默认的 userName

    sessionStorage.setItem("tenantName", tenantName);
    sessionStorage.setItem("tenantId", tenantId);
    sessionStorage.setItem("tenantEnv", tenantEnv);
    sessionStorage.setItem("studyNum", studyNum);
    sessionStorage.setItem("userName", userName);
    sessionStorage.setItem("userId", userId);

    updateLocalStorageAndTriggerEvent("tenantName", tenantName);
    updateLocalStorageAndTriggerEvent("tenantId", tenantId);
    updateLocalStorageAndTriggerEvent("tenantEnv", tenantEnv);
    updateLocalStorageAndTriggerEvent("studyNum", studyNum);
    updateLocalStorageAndTriggerEvent("userName", userName);
    updateLocalStorageAndTriggerEvent("userId", userId);
  }

  // 保存到 AppState 中
  const AppState = {};
  AppState.tenantId = tenantId;
  AppState.tenantEnv = tenantEnv;
  AppState.studyNum = studyNum;
  AppState.userName = userName; // 保存 userName 到 AppState
  AppState.userId = userId; // 保存 userId 到 AppState

  return { tenantName, tenantId, tenantEnv, studyNum, userName, userId };
}

/**
 * 更新 localStorage 并手动触发一个自定义事件
 * @param {string} key - localStorage 的键
 * @param {string} value - 要设置的值
 */
function updateLocalStorageAndTriggerEvent(key, value) {
  localStorage.setItem(key, value);
  // 手动触发一个自定义事件
  const event = new Event("localStorageModified");
  window.dispatchEvent(event);
}

/**
 * 检查 sessionStorage 和 localStorage 中的租户信息是否一致，如果不一致则刷新页面
 */
function checkStorageConsistency() {
  const tenantNameSession = sessionStorage.getItem('tenantName');
  const tenantNameLocal = localStorage.getItem('tenantName');

  const tenantIDSession = sessionStorage.getItem('tenantId');
  const tenantIDLocal = localStorage.getItem('tenantId');

  const environmentSession = sessionStorage.getItem('tenantEnv');
  const environmentLocal = localStorage.getItem('tenantEnv');

  const studyNumSession = sessionStorage.getItem('studyNum');
  const studyNumLocal = localStorage.getItem('studyNum');

  const userNameSession = sessionStorage.getItem('userName');
  const userNameLocal = localStorage.getItem('userName');

  const userIdSession = sessionStorage.getItem('userId');
  const userIdLocal = localStorage.getItem('userId');

  if (tenantNameSession !== tenantNameLocal ||
      tenantIDSession !== tenantIDLocal ||
      environmentSession !== environmentLocal ||
      userIdSession !== userIdLocal ||
      userNameSession !== userNameLocal ||
      studyNumSession !== studyNumLocal) {

      // 更新 sessionStorage
      sessionStorage.setItem('tenantName', tenantNameLocal);
      sessionStorage.setItem('tenantId', tenantIDLocal);
      sessionStorage.setItem('tenantEnv', environmentLocal);
      sessionStorage.setItem('studyNum', studyNumLocal);
      sessionStorage.setItem('userId', userIdLocal);
      sessionStorage.setItem('userName', userNameLocal);

      // 更新 URL 参数
      const url = new URL(window.location);
      url.searchParams.set('tenantName', tenantNameLocal);
      url.searchParams.set('tenantId', tenantIDLocal);
      url.searchParams.set('tenantEnv', environmentLocal);
      url.searchParams.set('studyNum', studyNumLocal);
      url.searchParams.set('userId', userIdLocal);
      url.searchParams.set('userName', userNameLocal);
      
      // 刷新页面，使用更新后的 URL
      window.location.href = url.toString();
  }
}
