<!DOCTYPE html>
<html lang="zh_CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>环境选择</title>
    <script src="../js/popup.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            width: 300px; /* Adjust width as needed */
            padding: 10px;
        }
        label {
            display: block;
            margin-top: 10px;
            margin-bottom: 5px;
            font-weight: bold;
        }
        select, input[type="text"] {
            width: 100%;
            padding: 8px;
            margin-bottom: 10px;
            box-sizing: border-box; /* Include padding in width */
        }
        button {
            padding: 10px 20px;
            font-size: 16px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <h1>环境选择</h1>

    <label for="environmentSelect">选择环境:</label>
    <select id="environmentSelect">
        <option value="local">Local</option>
        <option value="test">Test</option>
        <option value="uat">UAT</option>
    </select>

    <label for="environmentUrl">环境 URL:</label>
    <input type="text" id="environmentUrl">

    <button id="saveButton">保存</button>

</body>
</html>
