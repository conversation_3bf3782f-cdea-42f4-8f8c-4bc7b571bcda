const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');
const https = require('https');

class StaticServer {
    constructor(options = {}) {
        this.port = options.port || 3000;
        // 实际物理路径
        this.root = options.root || process.cwd();
        // 虚拟目录映射配置
        this.virtualDirs = options.virtualDirs || {};
        // 代理规则配置，格式: { '/api': 'http://target-server.com' }
        this.proxyRules = options.proxyRules || {};
        this.server = null;
    }

    // 启动服务器
    start() {
        this.server = http.createServer((req, res) => {
            this.handleRequest(req, res);
        });

        this.server.listen(this.port, () => {
            console.log(`Static file server running at http://localhost:${this.port}/`);
            console.log(`Root directory: ${this.root}`);
            console.log('Virtual directories:', this.virtualDirs);
            console.log('Proxy rules:', this.proxyRules);
        });
    }

    // 处理请求
    handleRequest(req, res) {
        // 处理OPTIONS请求
        if (req.method === 'OPTIONS') {
            res.setHeader('Access-Control-Allow-Origin', '*');
            res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS, PUT, PATCH, DELETE');
            res.setHeader('Access-Control-Allow-Headers', 'X-Requested-With,content-type');
            res.writeHead(200);
            res.end();
            return;
        }

        // 解析请求URL
        const pathname = decodeURIComponent(url.parse(req.url).pathname);
        
        // 检查是否需要代理
        const proxyTarget = this.getProxyTarget(pathname);
        if (proxyTarget) {
            this.handleProxy(req, res, proxyTarget, pathname);
            return;
        }
        
        // 获取实际文件路径
        const filePath = this.mapPath(pathname);
        
        // 检查文件是否存在
        fs.stat(filePath, (err, stats) => {
            if (err) {
                this.sendError(res, 404, 'File not found');
                return;
            }

            if (stats.isDirectory()) {
                this.sendDirectoryListing(filePath, pathname, res);
            } else {
                this.sendFile(filePath, res);
            }
        });
    }

    // 获取代理目标
    getProxyTarget(pathname) {
        for (const [prefix, target] of Object.entries(this.proxyRules)) {
            if (pathname.startsWith(prefix)) {
                return target;
            }
        }
        return null;
    }

    // 处理代理请求
    handleProxy(req, res, target, pathname) {
        // 构建目标URL
        const targetUrl = new URL(req.url, target);
        const options = {
            method: req.method,
            headers: { ...req.headers },
            protocol: targetUrl.protocol,
            hostname: targetUrl.hostname,
            port: targetUrl.port || (targetUrl.protocol === 'https:' ? 443 : 80),
            path: targetUrl.pathname + targetUrl.search,
        };

        // 删除可能导致问题的请求头
        delete options.headers.host;
        delete options.headers.connection;

        // 选择合适的请求模块
        const requestModule = targetUrl.protocol === 'https:' ? https : http;

        // 创建代理请求
        const proxyReq = requestModule.request(options, (proxyRes) => {
            // 设置CORS头
            res.setHeader('Access-Control-Allow-Origin', '*');
            res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS, PUT, PATCH, DELETE');
            res.setHeader('Access-Control-Allow-Headers', 'X-Requested-With,content-type');
            
            // 复制响应头
            Object.keys(proxyRes.headers).forEach(key => {
                res.setHeader(key, proxyRes.headers[key]);
            });
            res.writeHead(proxyRes.statusCode);
            proxyRes.pipe(res);
        });

        proxyReq.on('error', (err) => {
            console.error('Proxy error:', err);
            this.sendError(res, 502, 'Bad Gateway');
        });

        // 转发请求体
        if (['POST', 'PUT', 'PATCH'].includes(req.method)) {
            req.pipe(proxyReq);
        } else {
            proxyReq.end();
        }
    }

    // 映射虚拟路径到实际路径
    mapPath(pathname) {
        // 检查是否匹配虚拟目录
        for (let virtual in this.virtualDirs) {
            if (pathname.startsWith(virtual)) {
                return path.join(this.virtualDirs[virtual], 
                                pathname.slice(virtual.length));
            }
        }
        // 如果没有匹配的虚拟目录，使用根目录
        return path.join(this.root, pathname);
    }

    // 发送文件内容
    sendFile(filePath, res) {
        const stream = fs.createReadStream(filePath);
        
        // 设置Content-Type和CORS头
        const ext = path.extname(filePath).toLowerCase();
        const contentType = this.getMimeType(ext);
        res.setHeader('Content-Type', contentType);
        res.setHeader('Access-Control-Allow-Origin', '*');
        res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS, PUT, PATCH, DELETE');
        res.setHeader('Access-Control-Allow-Headers', 'X-Requested-With,content-type');

        // 流式传输文件
        stream.pipe(res);
        
        stream.on('error', () => {
            this.sendError(res, 500, 'Error reading file');
        });
    }

    // 发送目录列表
    sendDirectoryListing(dirPath, pathname, res) {
        fs.readdir(dirPath, (err, files) => {
            if (err) {
                this.sendError(res, 500, 'Error reading directory');
                return;
            }

            const html = this.generateDirectoryHtml(pathname, files);
            res.setHeader('Content-Type', 'text/html');
            res.setHeader('Access-Control-Allow-Origin', '*');
            res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS, PUT, PATCH, DELETE');
            res.setHeader('Access-Control-Allow-Headers', 'X-Requested-With,content-type');
            res.end(html);
        });
    }

    // 生成目录列表HTML
    generateDirectoryHtml(pathname, files) {
        const items = files.map(file => {
            const isDir = fs.statSync(path.join(this.mapPath(pathname), file)).isDirectory();
            const href = path.join(pathname, file) + (isDir ? '/' : '');
            return `<li><a href="${href}">${file}${isDir ? '/' : ''}</a></li>`;
        });

        return `
            <!DOCTYPE html>
            <html>
            <head>
                <title>Directory listing for ${pathname}</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; }
                    h1 { color: #333; }
                    ul { list-style-type: none; padding: 0; }
                    li { margin: 5px 0; }
                    a { text-decoration: none; color: #0066cc; }
                    a:hover { text-decoration: underline; }
                </style>
            </head>
            <body>
                <h1>Directory listing for ${pathname}</h1>
                <ul>
                    ${pathname !== '/' ? '<li><a href="../">../</a></li>' : ''}
                    ${items.join('\n')}
                </ul>
            </body>
            </html>
        `;
    }

    // 发送错误响应
    sendError(res, code, message) {
        res.statusCode = code;
        res.end(`<h1>${code} - ${message}</h1>`);
    }

    // 获取MIME类型
    getMimeType(ext) {
        const mimeTypes = {
            '.html': 'text/html',
            '.css': 'text/css',
            '.js': 'text/javascript',
            '.json': 'application/json',
            '.png': 'image/png',
            '.jpg': 'image/jpeg',
            '.gif': 'image/gif',
            '.svg': 'image/svg+xml',
            '.ico': 'image/x-icon',
            '.txt': 'text/plain',
        };
        return mimeTypes[ext] || 'application/octet-stream';
    }
}
 
const server = new StaticServer({
    port: 3866,
    root: './',  // 实际物理根目录
    virtualDirs: {
        '/pv-manus-front': './',
    },
    proxyRules: {
        '/base-server': 'https://copilot-test.pharmaronclinical.com',  
        '/kb-server': 'https://copilot-test.pharmaronclinical.com',  
    }
});

server.start();
