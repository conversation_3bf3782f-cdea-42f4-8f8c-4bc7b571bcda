{"manifest_version": 3, "name": "Argus助手", "version": "2025.0522.103239", "description": "协助数据录入人员快速完成报告从扫描件到系统的录入工作", "icons": {"16": "img/icon.png", "32": "img/icon.png", "48": "img/icon.png", "128": "img/icon.png"}, "permissions": ["debugger", "webNavigation", "tabs", "activeTab", "storage", "background", "notifications", "scripting", "management", "browsingData", "system.cpu", "system.memory", "system.display", "webRequest", "declarativeNetRequest"], "host_permissions": ["<all_urls>", "file://*", "https://*/*", "http://*/*"], "background": {"service_worker": "background.js"}, "action": {"default_popup": "html/popup.html"}, "web_accessible_resources": [{"resources": ["js/*", "html/recognition.html", "html/followup.html", "html/question.html", "html/navigation.html", "html/loading.html", "pv-migrate-config/index.html", "html/aose.html", "html/quickLogin.html", "js/Sortable.min.js", "js/dropzone.min.js", "js/bootstrap.bundle.min.js", "js/marked.min.js", "js/crypto-js.min.js", "js/markdown-it.min.js", "js/d3.min.js", "js/markmap-view-index.js", "js/markmap-toolbar-index.js", "js/inject.js", "js/jquery.min.js", "js/jsoneditor.min.js", "js/thumbmark.umd.js", "js/recognition.js", "js/followup.js", "js/question.js", "js/navigation.js", "js/followUpOperationMedicalHistory.js", "js/loading.js", "js/report_QC.js", "js/supplementaryInput.js", "js/quickLogin.js", "js/fetchDict.js", "css/recognition.css", "css/bootstrap.min.css", "css/dropzone.min.css", "css/all.min.css", "css/style.css", "css/katex.min.css", "css/jsoneditor.min.css", "css/loading.css", "css/quickLogin.css", "webfonts/fa-solid-900.woff2", "webfonts/fa-solid-900.woff", "webfonts/fa-solid-900.ttf"], "matches": ["<all_urls>"]}], "content_security_policy": {"extension_pages": "script-src 'self'; object-src 'self'"}}