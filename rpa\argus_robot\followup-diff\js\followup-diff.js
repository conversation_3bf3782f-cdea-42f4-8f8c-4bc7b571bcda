/* globals Alpine KeyGenerator DataComparer bootstrap */
/**
 * 病例随访数据对比工具主应用
 * 使用Alpine.js实现响应式数据绑定和状态管理
 */

// Alpine.js 数据存储
document.addEventListener('alpine:init', () => {
    // 主应用数据
    Alpine.data('followupDiffApp', () => ({
        // 状态管理
        loading: false,
        error: null,
        dataLoaded: false,

        // 阶段控制
        currentStage: 'mapping', // 'mapping' | 'stage1' | 'stage2' | 'completed'
        mappingCompleted: false,
        stage1Confirmed: false,
        stage2Confirmed: false,

        // 数据
        initialReport: null,
        followupReport: null,
        finalData: null,

        // 映射配置
        listModules: [], // 需要映射的list模块
        mappingConfig: {}, // 映射关系配置
        currentMappingModule: null, // 当前正在映射的模块

        // 映射配置
        listModules: [], // 需要映射的list模块
        mappingConfig: {}, // 映射关系配置
        currentMappingModule: null, // 当前正在映射的模块

        // 两个独立的变更列表
        stage1Changes: [], // 初次 vs 随访的变更
        stage2Changes: [], // 随访 vs 录入的变更

        // 保留原有变更列表以兼容现有代码
        changeList: [],

        // 配置
        keyConfig: {},

        // 字段分析
        currentAnalyzingModule: null,
        fieldSuggestions: [],

        // 工具实例
        dataComparer: null,
        keyGenerator: null,

        // 初始化
        init() {
            this.keyGenerator = new KeyGenerator();
            this.keyConfig = this.keyGenerator.getDefaultConfig();
            this.dataComparer = new DataComparer(this.keyConfig);
            
            console.log('病例随访数据对比工具已初始化');
        },

        // 加载示例数据
        async loadSampleData() {
            this.loading = true;
            this.error = null;

            try {
                const [initial, followup, final] = await Promise.all([
                    this.fetchData('data/report-000.json'),
                    this.fetchData('data/report-001.json'),
                    this.fetchData('data/report-002.json')
                ]);

                this.initialReport = initial;
                this.followupReport = followup;
                this.finalData = final;
                this.dataLoaded = true;

                // 识别需要映射的list模块
                this.identifyListModules();

                // 如果有需要映射的模块，进入映射阶段，否则直接进入第一阶段
                if (this.listModules.length > 0) {
                    this.currentStage = 'mapping';
                    this.initializeMappingConfig();
                } else {
                    this.currentStage = 'stage1';
                    this.performStage1Comparison();
                }

                console.log('数据加载完成', {
                    initial: this.initialReport,
                    followup: this.followupReport,
                    final: this.finalData
                });

            } catch (err) {
                this.error = `数据加载失败: ${err.message}`;
                console.error('数据加载错误:', err);
            } finally {
                this.loading = false;
            }
        },

        // 获取数据
        async fetchData(url) {
            const response = await fetch(url);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return await response.json();
        },

        // 执行第一阶段数据对比
        performStage1Comparison() {
            if (!this.initialReport || !this.followupReport) {
                return;
            }

            this.stage1Changes = this.dataComparer.compareStage1(
                this.initialReport,
                this.followupReport
            );

            // 更新兼容性变更列表
            this.updateChangeList();

            console.log('第一阶段对比完成，发现变更:', this.stage1Changes.length);
        },

        // 执行第二阶段数据对比
        performStage2Comparison() {
            if (!this.followupReport || !this.finalData) {
                return;
            }

            this.stage2Changes = this.dataComparer.compareStage2(
                this.followupReport,
                this.finalData
            );

            // 更新兼容性变更列表
            this.updateChangeList();

            console.log('第二阶段对比完成，发现变更:', this.stage2Changes.length);
        },

        // 更新兼容性变更列表（合并两个阶段的变更）
        updateChangeList() {
            this.changeList = [...this.stage1Changes, ...this.stage2Changes];
        },

        // 重新对比数据
        recompareData() {
            if (this.currentStage === 'stage1' || this.currentStage === 'stage2') {
                this.performStage1Comparison();
            }
            if (this.currentStage === 'stage2' && this.stage1Confirmed) {
                this.performStage2Comparison();
            }
        },

        // 确认第一阶段
        confirmStage1() {
            const confirmedCount = this.stage1Changes.filter(change => change.confirmed).length;
            if (confirmedCount === 0) {
                alert('请先确认至少一项第一阶段的变更');
                return false;
            }

            this.stage1Confirmed = true;
            this.currentStage = 'stage2';

            // 执行第二阶段对比
            this.performStage2Comparison();

            console.log(`第一阶段已确认，共确认 ${confirmedCount} 项变更`);
            return true;
        },

        // 确认第二阶段
        confirmStage2() {
            const confirmedCount = this.stage2Changes.filter(change => change.confirmed).length;
            if (confirmedCount === 0) {
                alert('请先确认至少一项第二阶段的变更');
                return false;
            }

            this.stage2Confirmed = true;
            this.currentStage = 'completed';

            console.log(`第二阶段已确认，共确认 ${confirmedCount} 项变更`);
            return true;
        },

        // 重置阶段状态
        resetStages() {
            // 如果有需要映射的模块，重置到映射阶段，否则重置到第一阶段
            this.currentStage = this.listModules.length > 0 ? 'mapping' : 'stage1';
            this.mappingCompleted = false;
            this.stage1Confirmed = false;
            this.stage2Confirmed = false;
            this.stage1Changes = [];
            this.stage2Changes = [];
            this.updateChangeList();

            // 重新初始化映射配置
            if (this.listModules.length > 0) {
                this.initializeMappingConfig();
            }
        },

        // 获取当前阶段的变更列表
        getCurrentStageChanges() {
            if (this.currentStage === 'stage1') {
                return this.stage1Changes;
            } else if (this.currentStage === 'stage2') {
                return this.stage2Changes;
            }
            return [];
        },

        // 检查是否可以进入第二阶段
        canEnterStage2() {
            return this.stage1Confirmed && this.finalData && this.canEnterStage1();
        },

        // 检查是否可以自动录入
        canAutoSubmit() {
            return this.stage2Confirmed && this.stage2Changes.filter(change => change.confirmed).length > 0;
        },

        // 识别需要映射的list模块
        identifyListModules() {
            this.listModules = [];

            if (!this.initialReport || !this.followupReport) {
                return;
            }

            // 检查初次报告和随访报告中的list类型字段
            const allKeys = new Set([
                ...Object.keys(this.initialReport),
                ...Object.keys(this.followupReport)
            ]);

            for (const key of allKeys) {
                const initialValue = this.initialReport[key];
                const followupValue = this.followupReport[key];

                // 如果任一数据源中该字段是数组且包含对象，则需要映射
                if ((Array.isArray(initialValue) && initialValue.length > 0 && typeof initialValue[0] === 'object') ||
                    (Array.isArray(followupValue) && followupValue.length > 0 && typeof followupValue[0] === 'object')) {

                    this.listModules.push({
                        name: key,
                        initialData: initialValue || [],
                        followupData: followupValue || [],
                        finalData: this.finalData ? (this.finalData[key] || []) : []
                    });
                }
            }

            console.log('识别到需要映射的list模块:', this.listModules);
        },

        // 初始化映射配置
        initializeMappingConfig() {
            this.mappingConfig = {};

            this.listModules.forEach(module => {
                this.mappingConfig[module.name] = {
                    stage1Mappings: [], // 初次 -> 随访的映射关系
                    stage2Mappings: []  // 随访 -> 录入的映射关系
                };
            });

            // 设置第一个模块为当前映射模块
            if (this.listModules.length > 0) {
                this.currentMappingModule = this.listModules[0].name;
            }
        },

        // 完成映射配置
        completeMappingConfig() {
            this.mappingCompleted = true;
            this.currentStage = 'stage1';

            // 应用映射配置到数据对比器
            this.applyMappingToComparer();

            // 执行第一阶段对比
            this.performStage1Comparison();

            console.log('映射配置完成，进入第一阶段对比');
        },

        // 应用映射配置到数据对比器
        applyMappingToComparer() {
            // 这里将映射关系应用到DataComparer中
            // 暂时先记录，后续在DataComparer中实现具体的映射逻辑
            this.dataComparer.mappingConfig = this.mappingConfig;
        },

        // 检查是否可以进入第一阶段
        canEnterStage1() {
            return this.mappingCompleted || this.listModules.length === 0;
        },

        // 添加映射关系
        addMapping(moduleName, stage, sourceIndex, targetIndex) {
            if (!this.mappingConfig[moduleName]) {
                this.mappingConfig[moduleName] = {
                    stage1Mappings: [],
                    stage2Mappings: []
                };
            }

            const mappingKey = stage === 1 ? 'stage1Mappings' : 'stage2Mappings';
            const mappings = this.mappingConfig[moduleName][mappingKey];

            // 检查是否已存在映射
            const existingIndex = mappings.findIndex(m => m.source === sourceIndex);
            if (existingIndex >= 0) {
                // 更新现有映射
                mappings[existingIndex].target = targetIndex;
            } else {
                // 添加新映射
                mappings.push({
                    source: sourceIndex,
                    target: targetIndex,
                    id: `${moduleName}_${stage}_${sourceIndex}_${targetIndex}`
                });
            }

            console.log(`添加映射: ${moduleName} 阶段${stage} ${sourceIndex} -> ${targetIndex}`);
        },

        // 删除映射关系
        removeMapping(moduleName, stage, sourceIndex) {
            if (!this.mappingConfig[moduleName]) return;

            const mappingKey = stage === 1 ? 'stage1Mappings' : 'stage2Mappings';
            const mappings = this.mappingConfig[moduleName][mappingKey];

            const index = mappings.findIndex(m => m.source === sourceIndex);
            if (index >= 0) {
                mappings.splice(index, 1);
                console.log(`删除映射: ${moduleName} 阶段${stage} ${sourceIndex}`);
            }
        },

        // 获取映射关系
        getMapping(moduleName, stage, sourceIndex) {
            if (!this.mappingConfig[moduleName]) return null;

            const mappingKey = stage === 1 ? 'stage1Mappings' : 'stage2Mappings';
            const mappings = this.mappingConfig[moduleName][mappingKey];

            return mappings.find(m => m.source === sourceIndex);
        },

        // 切换当前映射模块
        setCurrentMappingModule(moduleName) {
            this.currentMappingModule = moduleName;
        },

        // 获取当前映射模块的数据
        getCurrentMappingModuleData() {
            if (!this.currentMappingModule) return null;

            return this.listModules.find(m => m.name === this.currentMappingModule);
        },

        // 检查映射是否完成
        isMappingComplete() {
            // 简化检查：只要有映射配置就认为完成
            // 实际应用中可以添加更严格的验证
            return Object.keys(this.mappingConfig).length > 0;
        },

        // 渲染list项目卡片
        renderListItem(item, index, type = 'source') {
            const keyFields = this.getItemDisplayFields(item);

            return keyFields.map(field => `
                <div class="field-row">
                    <span class="field-name">${field.name}:</span>
                    <span class="field-value">${this.escapeHtml(field.value)}</span>
                </div>
            `).join('');
        },

        // 获取项目显示字段
        getItemDisplayFields(item) {
            const fields = [];
            const maxFields = 3; // 最多显示3个字段

            Object.keys(item).slice(0, maxFields).forEach(key => {
                if (key !== '报告分类' && key !== '报告模块') {
                    const value = item[key];
                    if (value !== null && value !== undefined && value !== '') {
                        fields.push({
                            name: key,
                            value: Array.isArray(value) ? `[${value.length}项]` : String(value)
                        });
                    }
                }
            });

            return fields;
        },

        // 拖拽开始
        startDrag(event, sourceType, sourceIndex, moduleName) {
            const dragData = {
                sourceType,
                sourceIndex,
                moduleName,
                stage: sourceType === 'initial' ? 1 : 2
            };

            event.dataTransfer.setData('text/plain', JSON.stringify(dragData));
            event.dataTransfer.effectAllowed = 'move';

            // 添加拖拽样式
            event.target.classList.add('dragging');

            console.log('开始拖拽:', dragData);
        },

        // 处理放置
        handleDrop(event, targetType, moduleName, stage) {
            event.preventDefault();

            try {
                const dragData = JSON.parse(event.dataTransfer.getData('text/plain'));

                // 获取目标索引
                const targetElement = event.target.closest('.mapping-item');
                if (!targetElement) return;

                const targetIndex = parseInt(targetElement.dataset.index);

                // 验证拖拽的有效性
                if (dragData.moduleName !== moduleName || dragData.stage !== stage) {
                    console.warn('无效的拖拽操作');
                    return;
                }

                // 添加映射关系
                this.addMapping(moduleName, stage, dragData.sourceIndex, targetIndex);

                // 重新绘制连线
                this.drawConnections(moduleName, stage);

            } catch (error) {
                console.error('处理拖拽放置时出错:', error);
            }
        },

        // 绘制连线
        drawConnections(moduleName, stage) {
            const mappingKey = stage === 1 ? 'stage1Mappings' : 'stage2Mappings';
            const mappings = this.mappingConfig[moduleName]?.[mappingKey] || [];

            // 获取对应的SVG元素
            const stageId = stage === 1 ? 'mapping-stage1' : 'mapping-stage2';
            const svg = document.querySelector(`#${stageId} .connection-svg`);

            if (!svg) return;

            // 清除现有连线
            svg.innerHTML = '';

            // 绘制每个映射关系的连线
            mappings.forEach(mapping => {
                this.drawConnection(svg, mapping, stage);
            });
        },

        // 绘制单条连线
        drawConnection(svg, mapping, stage) {
            const sourcePrefix = stage === 1 ? 'initial' : 'followup2';
            const targetPrefix = stage === 1 ? 'followup' : 'final';

            const sourceElement = document.getElementById(`${sourcePrefix}-${mapping.source}`);
            const targetElement = document.getElementById(`${targetPrefix}-${mapping.target}`);

            if (!sourceElement || !targetElement) return;

            const svgRect = svg.getBoundingClientRect();
            const sourceRect = sourceElement.getBoundingClientRect();
            const targetRect = targetElement.getBoundingClientRect();

            // 计算连线的起点和终点
            const startX = sourceRect.right - svgRect.left;
            const startY = sourceRect.top + sourceRect.height / 2 - svgRect.top;
            const endX = targetRect.left - svgRect.left;
            const endY = targetRect.top + targetRect.height / 2 - svgRect.top;

            // 创建连线路径
            const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
            const controlX1 = startX + (endX - startX) * 0.3;
            const controlX2 = startX + (endX - startX) * 0.7;

            const pathData = `M ${startX} ${startY} C ${controlX1} ${startY} ${controlX2} ${endY} ${endX} ${endY}`;

            path.setAttribute('d', pathData);
            path.setAttribute('stroke', '#007bff');
            path.setAttribute('stroke-width', '2');
            path.setAttribute('fill', 'none');
            path.setAttribute('class', 'connection-line');

            // 添加删除连线的点击事件
            path.addEventListener('click', () => {
                this.removeMapping(this.currentMappingModule, stage, mapping.source);
                this.drawConnections(this.currentMappingModule, stage);
            });

            svg.appendChild(path);

            // 添加箭头
            const marker = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
            marker.setAttribute('cx', endX - 5);
            marker.setAttribute('cy', endY);
            marker.setAttribute('r', '3');
            marker.setAttribute('fill', '#007bff');
            marker.setAttribute('class', 'connection-marker');

            svg.appendChild(marker);
        },

        // 渲染数据阶段
        renderDataStage(data, stage) {
            if (!data) return '<p class="text-muted">暂无数据</p>';

            let html = '';
            Object.keys(data).forEach(moduleName => {
                if (moduleName === 'datetime') return; // 跳过元数据

                const moduleData = data[moduleName];
                html += this.renderModule(moduleName, moduleData, stage);
            });

            return html;
        },

        // 渲染模块
        renderModule(moduleName, moduleData, stage) {
            let html = `
                <div class="data-module">
                    <div class="data-module-header">${moduleName}</div>
                    <div class="data-module-body">
            `;

            if (Array.isArray(moduleData)) {
                html += this.renderArray(moduleData, moduleName, stage);
            } else if (typeof moduleData === 'object' && moduleData !== null) {
                html += this.renderObject(moduleData, stage);
            } else {
                html += `<div class="field-item">
                    <span class="field-label">值</span>
                    <span class="field-value">${this.escapeHtml(String(moduleData))}</span>
                </div>`;
            }

            html += '</div></div>';
            return html;
        },

        // 渲染对象
        renderObject(obj, stage, level = 0) {
            let html = '';
            Object.keys(obj).forEach(key => {
                const value = obj[key];
                const changeClass = this.getFieldChangeClass(key, stage);
                const levelClass = level > 0 ? `nested-level-${Math.min(level, 3)}` : '';

                if (Array.isArray(value)) {
                    // 数组类型 - 复杂字段，另起一行
                    html += `<div class="field-item complex-field ${changeClass} ${levelClass}">
                        <div class="field-label">${this.escapeHtml(key)}</div>
                        <div class="field-value">
                            ${this.renderArrayAsFields(value, stage, level + 1)}
                        </div>
                    </div>`;
                } else if (typeof value === 'object' && value !== null) {
                    // 嵌套对象 - 复杂字段，另起一行
                    html += `<div class="field-item complex-field ${changeClass} ${levelClass}">
                        <div class="field-label">${this.escapeHtml(key)}</div>
                        <div class="field-value">
                            <div class="nested-object-container">
                                ${this.renderObject(value, stage, level + 1)}
                            </div>
                        </div>
                    </div>`;
                } else {
                    // 基础类型 - 单行显示
                    html += `<div class="field-item basic-field ${changeClass} ${levelClass}">
                        <span class="field-label">${this.escapeHtml(key)}</span>
                        <span class="field-value">${this.formatBasicFieldValue(value)}</span>
                    </div>`;
                }
            });
            return html;
        },

        // 渲染数组
        renderArray(array, moduleName, stage) {
            if (array.length === 0) {
                return '<p class="text-muted small">暂无数据</p>';
            }

            let html = '<div class="array-container">';
            array.forEach((item, index) => {
                html += `<div class="array-item">
                    <div class="array-item-header">项目 ${index + 1}</div>
                    ${this.renderObject(item, stage)}
                </div>`;
            });
            html += '</div>';
            return html;
        },

        // 渲染数组为字段形式（用于嵌套显示）
        renderArrayAsFields(array, stage, level = 0) {
            if (array.length === 0) {
                return '<span class="text-muted small">空列表</span>';
            }

            // 如果是简单数组（字符串、数字等）
            if (array.every(item => typeof item !== 'object')) {
                return `<div class="simple-array-container">
                    ${array.map(item =>
                        `<span class="badge bg-light text-dark">${this.escapeHtml(String(item))}</span>`
                    ).join('')}
                </div>`;
            }

            // 如果是对象数组，紧凑显示每个对象
            let html = '<div class="nested-array-container">';
            array.forEach((item, index) => {
                html += `<div class="nested-array-item">
                    <div class="nested-array-item-header">项目 ${index + 1}</div>
                    <div class="nested-array-item-content">
                        ${this.renderObject(item, stage, level)}
                    </div>
                </div>`;
            });
            html += '</div>';
            return html;
        },

        // 格式化基础字段值（不包含复杂对象处理）
        formatBasicFieldValue(value) {
            if (value === null || value === undefined || value === '') {
                return '<span class="text-muted">-</span>';
            }
            return this.escapeHtml(String(value));
        },

        // 格式化字段值（已弃用，由renderObject处理复杂类型）
        formatFieldValue(value) {
            // 这个方法现在主要用于向后兼容，复杂对象由renderObject处理
            return this.formatBasicFieldValue(value);
        },



        // 获取字段变更样式类
        getFieldChangeClass(fieldName, stage) {
            // 这里可以根据变更列表来确定样式
            // 简化实现，实际应该检查changeList
            return '';
        },

        // HTML转义
        escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        },

        // 获取变更数量
        getChangeCount(type) {
            return this.changeList.filter(change => change.type === type).length;
        },

        // 获取已确认变更数量
        getConfirmedChangeCount() {
            return this.changeList.filter(change => change.confirmed).length;
        },

        // 按模块分组变更列表
        getChangesByModule(changeList = null) {
            const changes = changeList || this.changeList;
            const grouped = {};

            changes.forEach(change => {
                const moduleName = this.getModuleNameFromPath(change.path);
                if (!grouped[moduleName]) {
                    grouped[moduleName] = [];
                }
                grouped[moduleName].push(change);
            });

            return grouped;
        },

        // 从路径中提取模块名称
        getModuleNameFromPath(path) {
            const parts = path.split('.');
            return parts[0] || '未知模块';
        },

        // 格式化显示路径（还原转义字符）
        formatDisplayPath(path, moduleName) {
            let displayPath = path.replace(moduleName + '.', '');

            // 还原转义的特殊字符
            displayPath = displayPath
                .replace(/〔/g, '[')   // 恢复左方括号
                .replace(/〕/g, ']')   // 恢复右方括号
                .replace(/％/g, '%')   // 恢复百分号
                .replace(/·/g, '.')   // 恢复点号
                .replace(/｜/g, '|');  // 恢复竖线

            return displayPath;
        },

        // 判断是否应该显示原值
        shouldShowOldValue(change) {
            // 对于删除操作，总是显示原值
            if (change.type === 'DELETE') {
                return true;
            }
            // 对于修改操作，如果有原值就显示
            if (change.type === 'MODIFY') {
                return change.oldValue !== undefined && change.oldValue !== null;
            }
            // 对于新增操作，不显示原值
            return false;
        },

        // 判断是否应该显示新值
        shouldShowNewValue(change) {
            // 对于新增操作，总是显示新值
            if (change.type === 'ADD') {
                return true;
            }
            // 对于修改操作，总是显示新值（包括空值）
            if (change.type === 'MODIFY') {
                return change.newValue !== undefined && change.newValue !== null;
            }
            // 对于删除操作，不显示新值
            return false;
        },

        // 获取模块的变更统计
        getModuleChangeStats(changes) {
            const stats = {
                total: changes.length,
                confirmed: changes.filter(c => c.confirmed).length,
                byType: {
                    ADD: changes.filter(c => c.type === 'ADD').length,
                    MODIFY: changes.filter(c => c.type === 'MODIFY').length,
                    DELETE: changes.filter(c => c.type === 'DELETE').length
                }
            };
            return stats;
        },

        // 切换变更确认状态
        toggleChangeConfirmation(change) {
            change.confirmed = !change.confirmed;
        },

        // 批量确认模块变更
        confirmAllModuleChanges(moduleChanges) {
            moduleChanges.forEach(change => {
                change.confirmed = true;
            });
        },

        // 批量取消模块变更确认
        cancelAllModuleChanges(moduleChanges) {
            moduleChanges.forEach(change => {
                change.confirmed = false;
            });
        },

        // 切换模块所有变更的确认状态
        toggleAllModuleChanges(moduleChanges) {
            const allConfirmed = moduleChanges.every(change => change.confirmed);
            moduleChanges.forEach(change => {
                change.confirmed = !allConfirmed;
            });
        },

        // 获取变更项样式类
        getChangeItemClass(change) {
            return change.confirmed ? 'confirmed' : 'pending';
        },

        // 获取变更类型徽章样式类
        getChangeTypeBadgeClass(type) {
            const classes = {
                'ADD': 'bg-success',
                'MODIFY': 'bg-warning text-dark',
                'DELETE': 'bg-danger'
            };
            return classes[type] || 'bg-secondary';
        },

        // 获取变更类型文本
        getChangeTypeText(type) {
            const texts = {
                'ADD': '新增',
                'MODIFY': '修改',
                'DELETE': '删除'
            };
            return texts[type] || type;
        },

        // 格式化变更值显示
        formatChangeValue(value) {
            // 处理null、undefined和空字符串
            if (value === null || value === undefined) {
                return '<span class="text-muted">空</span>';
            }
            if (value === '') {
                return '<span class="text-muted empty-value">空字符串</span>';
            }

            // 尝试解析JSON格式的值（列表项详情）
            try {
                const parsed = JSON.parse(value);
                if (Array.isArray(parsed)) {
                    return this.formatArrayValue(parsed);
                } else if (typeof parsed === 'object' && parsed !== null) {
                    return this.formatObjectValue(parsed);
                }
            } catch (e) {
                // 不是JSON，按普通字符串处理
            }

            // 普通值的处理 - 不再截断
            return this.escapeHtml(value);
        },

        // 格式化数组值显示
        formatArrayValue(array) {
            if (array.length === 0) {
                return '<span class="text-muted">空列表</span>';
            }

            // 如果是简单数组
            if (array.every(item => typeof item !== 'object')) {
                return array.map(item =>
                    `<span class="badge bg-light text-dark me-1">${this.escapeHtml(String(item))}</span>`
                ).join('');
            }

            // 如果是对象数组，显示为字段列表
            return array.map((item, index) => {
                return `<div class="list-item-detail mb-2">
                    <div class="list-item-header">项目 ${index + 1}:</div>
                    <div class="list-item-fields">
                        ${this.formatObjectFields(item)}
                    </div>
                </div>`;
            }).join('');
        },

        // 格式化对象值显示
        formatObjectValue(obj) {
            if (Object.keys(obj).length === 0) {
                return '<span class="text-muted">空对象</span>';
            }

            return `<div class="object-detail">
                ${this.formatObjectFields(obj)}
            </div>`;
        },

        // 格式化对象字段
        formatObjectFields(obj) {
            return Object.keys(obj).map(key => {
                const value = obj[key];
                let displayValue;

                if (Array.isArray(value)) {
                    if (value.length === 0) {
                        displayValue = '<span class="text-muted">空列表</span>';
                    } else if (value.every(item => typeof item !== 'object')) {
                        displayValue = value.map(item =>
                            `<span class="badge bg-secondary me-1">${this.escapeHtml(String(item))}</span>`
                        ).join('');
                    } else {
                        displayValue = `<span class="text-info">${value.length}个对象</span>`;
                    }
                } else if (typeof value === 'object' && value !== null) {
                    displayValue = '<span class="text-warning">对象</span>';
                } else {
                    displayValue = this.escapeHtml(String(value));
                }

                return `<div class="field-row">
                    <span class="field-name">${this.escapeHtml(key)}:</span>
                    <span class="field-value">${displayValue}</span>
                </div>`;
            }).join('');
        },

        // 更新主键配置
        updateKeyConfig(moduleName, value) {
            const fields = value.split(',').map(f => f.trim()).filter(f => f);
            if (!this.keyConfig[moduleName]) {
                this.keyConfig[moduleName] = {};
            }
            this.keyConfig[moduleName].keyFields = fields;

            // 实时验证配置
            this.validateKeyConfig(moduleName);

            // 触发界面更新（Alpine.js会自动重新计算错误数量）
            this.$nextTick(() => {
                // 强制更新按钮上的错误计数
                console.log(`配置更新: ${moduleName}, 当前错误数量: ${this.getConfigErrorCount()}`);
            });
        },

        // 重置主键配置
        resetKeyConfig(moduleName) {
            const defaultConfig = this.keyGenerator.getDefaultConfig();
            if (defaultConfig[moduleName]) {
                this.keyConfig[moduleName] = { ...defaultConfig[moduleName] };
            }
        },

        // 验证主键配置
        validateKeyConfig(moduleName) {
            if (!this.initialReport || !this.keyConfig[moduleName]) {
                return { valid: true, message: '' };
            }

            const moduleData = this.initialReport[moduleName];
            if (!moduleData || !Array.isArray(moduleData) || moduleData.length === 0) {
                return { valid: true, message: '模块无数据' };
            }

            const keyFields = this.keyConfig[moduleName].keyFields;
            if (!keyFields || keyFields.length === 0) {
                return { valid: true, message: '使用索引作为主键' };
            }

            // 检查字段是否存在
            const firstItem = moduleData[0];
            const missingFields = keyFields.filter(field => !(field in firstItem));

            if (missingFields.length > 0) {
                return {
                    valid: false,
                    message: `字段不存在: ${missingFields.join(', ')}`
                };
            }

            // 检查主键唯一性
            const keys = new Set();
            let duplicates = 0;

            moduleData.forEach(item => {
                const key = keyFields.map(field => item[field] || '').join('|');
                if (keys.has(key)) {
                    duplicates++;
                } else {
                    keys.add(key);
                }
            });

            if (duplicates > 0) {
                return {
                    valid: false,
                    message: `发现 ${duplicates} 个重复主键`
                };
            }

            return { valid: true, message: '配置有效' };
        },

        // 获取配置验证状态
        getConfigValidation(moduleName) {
            return this.validateKeyConfig(moduleName);
        },

        // 获取所有配置错误的数量
        getConfigErrorCount() {
            if (!this.dataLoaded || !this.keyConfig) {
                return 0;
            }

            let errorCount = 0;
            Object.keys(this.keyConfig).forEach(moduleName => {
                const validation = this.validateKeyConfig(moduleName);
                if (!validation.valid) {
                    errorCount++;
                }
            });

            return errorCount;
        },

        // 获取配置错误的详细信息
        getConfigErrors() {
            if (!this.dataLoaded || !this.keyConfig) {
                return [];
            }

            const errors = [];
            Object.keys(this.keyConfig).forEach(moduleName => {
                const validation = this.validateKeyConfig(moduleName);
                if (!validation.valid) {
                    errors.push({
                        module: moduleName,
                        message: validation.message,
                        canAutoFix: this.canAutoFixError(moduleName, validation)
                    });
                }
            });

            return errors;
        },

        // 判断错误是否可以自动修复
        canAutoFixError(moduleName, validation) {
            // 如果是字段不存在的错误，可以尝试自动修复
            if (validation.message && validation.message.includes('字段不存在')) {
                return true;
            }
            // 如果是重复主键错误，可以尝试添加更多字段
            if (validation.message && validation.message.includes('重复主键')) {
                return true;
            }
            return false;
        },

        // 自动修复配置错误
        autoFixConfigError(moduleName) {
            if (!this.initialReport || !this.initialReport[moduleName]) {
                return false;
            }

            const moduleData = this.initialReport[moduleName];
            if (!Array.isArray(moduleData) || moduleData.length === 0) {
                return false;
            }

            // 使用智能分析生成新的主键配置
            const suggestion = this.keyGenerator.generateKeyFieldSuggestions(moduleData);
            if (suggestion && suggestion.keyFields && suggestion.keyFields.length > 0) {
                this.keyConfig[moduleName] = {
                    keyFields: suggestion.keyFields,
                    description: suggestion.description
                };
                console.log(`自动修复 ${moduleName} 的配置:`, suggestion.keyFields);
                return true;
            }

            return false;
        },

        // 批量自动修复所有错误
        autoFixAllErrors() {
            const errors = this.getConfigErrors();
            let fixedCount = 0;

            errors.forEach(error => {
                if (error.canAutoFix) {
                    if (this.autoFixConfigError(error.module)) {
                        fixedCount++;
                    }
                }
            });

            if (fixedCount > 0) {
                console.log(`成功自动修复 ${fixedCount} 个配置错误`);
                // 触发界面更新
                this.$nextTick(() => {
                    console.log(`修复后错误数量: ${this.getConfigErrorCount()}`);
                });
            }

            return fixedCount;
        },

        // 分析模块字段
        analyzeModuleFields(moduleName) {
            if (!this.initialReport || !this.initialReport[moduleName]) {
                this.fieldSuggestions = [];
                return;
            }

            const moduleData = this.initialReport[moduleName];
            if (!Array.isArray(moduleData) || moduleData.length === 0) {
                this.fieldSuggestions = [];
                return;
            }

            // 获取所有字段
            const allFields = new Set();
            moduleData.forEach(item => {
                Object.keys(item).forEach(field => allFields.add(field));
            });

            this.fieldSuggestions = Array.from(allFields).sort();
            this.currentAnalyzingModule = moduleName;
        },

        // 添加字段到配置
        addFieldToConfig(moduleName, field) {
            if (!this.keyConfig[moduleName]) {
                this.keyConfig[moduleName] = { keyFields: [] };
            }

            const currentFields = this.keyConfig[moduleName].keyFields || [];
            if (!currentFields.includes(field)) {
                this.keyConfig[moduleName].keyFields = [...currentFields, field];
                this.validateKeyConfig(moduleName);
            }
        },

        // 显示配置模态框
        showConfigModal() {
            const modal = new bootstrap.Modal(document.getElementById('configModal'));
            modal.show();
        },

        // 保存配置
        saveConfig() {
            this.dataComparer.updateKeyConfig(this.keyConfig);

            // 重新执行当前阶段的对比
            this.recompareData();

            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('configModal'));
            if (modal) {
                modal.hide();
            }

            // 这里可以添加保存到服务器的逻辑
            console.log('配置已保存:', this.keyConfig);
        },

        // 自动录入
        async autoSubmit() {
            if (!this.canAutoSubmit()) {
                alert('请先完成两个阶段的确认');
                return;
            }

            const confirmedStage2Changes = this.stage2Changes.filter(change => change.confirmed);

            if (confirmedStage2Changes.length === 0) {
                alert('请先确认要录入的第二阶段变更项');
                return;
            }

            try {
                const changeListData = {
                    id: `changelist_${Date.now()}`,
                    timestamp: new Date().toISOString(),
                    stage1Summary: {
                        total: this.stage1Changes.length,
                        confirmed: this.stage1Changes.filter(c => c.confirmed).length
                    },
                    stage2Changes: confirmedStage2Changes,
                    metadata: {
                        stage1Confirmed: this.stage1Confirmed,
                        stage2Confirmed: this.stage2Confirmed,
                        totalStage2Changes: this.stage2Changes.length,
                        confirmedStage2Changes: confirmedStage2Changes.length
                    }
                };

                console.log('准备提交变更列表:', changeListData);

                // 这里应该调用实际的API
                // const response = await fetch('/api/followup/auto-submit', {
                //     method: 'POST',
                //     headers: { 'Content-Type': 'application/json' },
                //     body: JSON.stringify(changeListData)
                // });

                // 模拟API调用
                await new Promise(resolve => setTimeout(resolve, 1000));

                alert(`成功录入 ${confirmedStage2Changes.length} 项第二阶段变更`);

            } catch (err) {
                alert(`录入失败: ${err.message}`);
                console.error('自动录入错误:', err);
            }
        },

        // 导出变更列表
        exportChangeList() {
            const data = {
                exportTime: new Date().toISOString(),
                changeList: this.changeList,
                summary: {
                    total: this.changeList.length,
                    confirmed: this.getConfirmedChangeCount(),
                    byType: {
                        add: this.getChangeCount('ADD'),
                        modify: this.getChangeCount('MODIFY'),
                        delete: this.getChangeCount('DELETE')
                    }
                }
            };

            const blob = new Blob([JSON.stringify(data, null, 2)], { 
                type: 'application/json' 
            });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `changelist_${new Date().toISOString().slice(0, 10)}.json`;
            a.click();
            URL.revokeObjectURL(url);
        }
    }));
});
