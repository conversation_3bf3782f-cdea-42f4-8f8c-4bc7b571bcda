* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: '<PERSON><PERSON>', <PERSON>l, sans-serif;
  background-color: #f5f5f5;
  color: #333;
  height: 100vh;
  width: 100vw;
  margin: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.container {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  width: 100%;
  max-width: 1200px;
}

.main-panel {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  gap: 20px;
  margin-bottom: 20px;
}

.top-panel {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 20px;
  margin-bottom: 20px;
}

.info-card {
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  flex: 1 1 calc(50% - 20px);
  transition: box-shadow 0.3s, transform 0.3s;
}

.info-card:hover {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  transform: translateY(-5px);
}

.info-card h3 {
  margin-bottom: 15px;
  font-size: 20px;
  color: #716DCC;
}

.info-card p {
  margin-bottom: 10px;
  font-size: 16px;
  color: #555;
}

.upload-section {
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  display: flex;
  flex-direction: column;
  flex: 0 0 300px;  /* 右侧上传区域固定宽度为300px */
  gap: 20px;
  transition: box-shadow 0.3s, transform 0.3s;
}

.upload-section:hover {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  transform: translateY(-5px);
}

.upload-section h2 {
  margin-bottom: 20px;
  font-size: 24px;
  color: #716DCC;
}

.dropzone {
  border: 2px dashed #716DCC;
  padding: 40px;
  text-align: center;
  border-radius: 8px;
  background: #f9f9f9;
  transition: border-color 0.3s, background-color 0.3s;
}

.dropzone:hover {
  border-color: #716DCC;
  background-color: #f0f8ff;
}

@media (max-width: 768px) {
  .main-panel {
      flex-direction: column;
  }

  .upload-section {
      flex: 1 1 100%;
  }

  .info-section {
      flex: 1 1 100%;
  }
}

.panel-icon {
  width: 30px;
  height: 30px;
  cursor: pointer;
}

.image-result-container {
  position: relative;
  display: flex;
  flex-direction: row;
  border: 1px solid #ddd;
  margin-bottom: 10px;
  z-index: 10; /* 确保面板层级低于侧边栏 */
}

.image-panel, .result-panel {
  flex: 1;
  padding: 10px;
}

.image-panel img {
  max-width: 100%;
  height: auto;
}

.result-panel {
  background-color: #fff;
}

.hidden {
  display: none;
}

.sidebar {
  position: fixed;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  background-color: #333;
  padding: 10px;
  border-radius: 0 5px 5px 0;
  z-index: 1000; /* 确保侧边栏在最前面显示 */
}

.sidebar i {
  font-size: 24px;
  color: #fff;
  margin-bottom: 10px;
  cursor: pointer;
}

.sidebar button {
  background-color: #333;
  border: none;
  color: #fff;
  padding: 10px;
  margin-bottom: 10px;
  cursor: pointer;
}

.loading-spinner {
  display: none;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 300px; /* 增加宽度 */
  height: auto; /* 高度根据内容自动调整 */
  max-height: 70vh; /* 设置最大高度，避免内容过多时撑破页面 */
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 9999;
  text-align: center;
  padding: 20px;
  border-radius: 10px;
  background-color: #716DCC; /* 初始背景颜色 */
  color: white;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
}

.spinner-icon {
  width: 60px;
  height: 60px;
  border: 8px solid #f3f3f3;
  border-top: 8px solid #716DCC;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

#loadingProgress {
  background-color: #716DCC;
}

.loading-info {
  margin-top: 20px;
  font-size: 14px; /* 调整字体大小 */
  line-height: 1.5; /* 调整行高以提高可读性 */
}

#currentNode {
  font-weight: bold; /* 使当前节点的名称更加显眼 */
  margin-bottom: 10px; /* 增加与其他文本的间距 */
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes flash {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.container {
  max-width: 100% !important;
}

.image-result-container table,
.image-result-container table tbody,
.image-result-container table td,
.image-result-container table tfoot,
.image-result-container table th,
.image-result-container table thead,
.image-result-container table tr {
  border-width: 2px !important;
  text-align: center;
  padding: 8px;
}

.jsoneditor table,
.jsoneditor table tbody,
.jsoneditor table td,
.jsoneditor table tfoot,
.jsoneditor table th,
.jsoneditor table thead,
.jsoneditor table tr {
  border-width: 0 !important;
  text-align: center !important;
  padding: 0 !important;
}

.modal-dialog {
  max-width: 80% !important;
}

.result-panel svg {
  width: 100% !important;
  height: 500px !important;
  display: block;
  overflow: visible !important;
}

.ck svg {
  width: 10px !important;
  height: 10px !important;
  overflow: visible !important;
}

.result-panel {
  position: relative;
  padding: 0;
}

svg {
  width: 100%;
  height: auto;
  overflow: visible !important;
}

.markmap-container {
  width: 100%;
  height: 100%;
}

.markmap-container svg {
  all: unset;
  width: 100% !important;
  height: 500px !important;
}

/* 提示词配置的样式 */
.language-section {
  margin-bottom: 20px;
}

.language-section h4 {
  margin-bottom: 10px;
}

.prompt-item {
  margin-bottom: 15px;
}

.prompt-item label {
  display: block;
  font-weight: bold;
  margin-bottom: 5px;
}

.prompt-textarea {
  width: 100%;
  resize: vertical;
}

.prompts-wrapper {
  display: flex;
  height: 500px; /* 可以根据需要调整高度 */
}

.prompts-navigation {
  flex: 0 0 400px; /* 增加导航栏的宽度，避免遮挡 */
  overflow-y: auto;
  border-right: 1px solid #ddd;
}

/* 模块分组标题样式 */
.nav-module-title {
  font-weight: bold;
  padding: 10px;
  background-color: #e9ecef;
  border-top: 1px solid #ddd;
}

.prompts-content {
  flex: 1;
  padding-left: 20px;
  overflow-y: auto;
}

.nav-lang-title {
  font-weight: bold;
  padding: 10px;
  background-color: #f0f0f0;
}

.nav-prompt-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.nav-prompt-item {
  padding: 8px 10px;
  cursor: pointer;
  white-space: nowrap; /* 不换行，避免文字折行 */
  overflow: hidden; /* 超出部分隐藏 */
  text-overflow: ellipsis; /* 显示省略号 */
}

.nav-prompt-item:hover {
  background-color: #e0e0e0;
}

/* 鼠标悬停时显示完整提示词的悬浮提示 */
.nav-prompt-item:hover::after {
  content: attr(data-full-title);
  position: absolute;
  left: 260px; /* 根据导航栏宽度调整 */
  top: 0;
  white-space: normal;
  width: 300px; /* 根据需要调整 */
  background-color: #fff;
  border: 1px solid #ddd;
  padding: 10px;
  z-index: 1000;
}

.nav-prompt-item.active {
  background-color: #d0d0d0;
  font-weight: bold;
}
.upload-panel-container {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
}

.info-section, .upload-section {
  box-sizing: border-box;
  padding: 20px;
}

.info-section {
  flex: 1 1 60%;
  background-color: #f0f0f0;
  border-right: 1px solid #ddd;
  display: flex;
  flex-wrap: wrap;
}

.info-section > div {
  flex: 1 1 50%;
  margin-bottom: 20px;
}

.info-section h3 {
  margin-bottom: 10px;
}

.info-section p {
  margin-bottom: 5px;
}

.upload-section {
  flex: 1 1 40%;
}

@media (max-width: 768px) {
  .info-section, .upload-section {
      flex: 1 1 100%;
  }

  .info-section > div {
      flex: 1 1 100%;
  }

  .info-section {
      border-right: none;
      border-bottom: 1px solid #ddd;
  }
}

#loadingMessage {
  font-weight: bold;
  margin-bottom: 10px;
}

/* 浮动面板 */
.floating-panel {
  position: fixed;
  top: 50%;
  right: 0;
  transform: translateY(-50%);
  background: #fff;
  border-radius: 10px 0 0 10px;
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  transition: transform 0.3s ease-in-out;
  width: 250px;
  overflow: visible; /* 确保折叠按钮不被裁剪 */
  display: flex;
  flex-direction: column;
}

/* 选项卡导航 */
.tab-nav {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  border-bottom: 1px solid #ddd;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 10px;
  cursor: pointer;
  background: #f9f9f9;
  font-size: 14px;
  user-select: none;
  transition: background-color 0.3s;
}

.tab-item.active {
  background: #fff;
  border-bottom: 2px solid #716DCC;
  font-weight: bold;
}

.tab-item:hover {
  background: #e9e9e9;
}

/* 选项卡内容 */
.tab-content {
  flex: 1;
  overflow-y: auto;
}

.tab-pane {
  display: none;
  padding: 10px;
}

.tab-pane.active {
  display: block;
}

/* 标签组容器 */
.label-group {
  border: 1px solid #ddd;
  border-radius: 5px;
  padding: 10px;
  margin-bottom: 10px;
  background-color: #fff;
  transition: transform 0.3s, box-shadow 0.3s, background 0.3s;
  display: flex;
  flex-direction: column;
}

/* 标签按钮 */
.label-button {
  padding: 10px;
  cursor: pointer;
  border: none;
  border-radius: 3px;
  background: #f9f9f9;
  transition: background-color 0.3s;
  font-size: 14px;
  color: #333;
  width: 100%;
  margin-bottom: 5px;
}

/* 去除最后一个按钮的下边距 */
.label-button:last-child {
  margin-bottom: 0;
}

/* 鼠标悬停效果 */
.label-button:hover {
  background-color: #e0e0e0;
}

/* 标签组的悬停效果 */
.label-group:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
  background: linear-gradient(135deg, #f9f9f9 0%, #e0e0e0 100%);
}

/* 调整标签网格 */
.label-grid {
  padding: 10px;
}

/* 折叠按钮 */
.label-panel-toggle {
  position: absolute;
  top: 50%;
  left: -15px; /* 调整以使按钮部分露出 */
  transform: translateY(-50%);
  background: #716DCC;
  color: #fff;
  width: 30px;
  height: 60px; /* 增大高度以便更容易点击 */
  border-radius: 5px 0 0 5px; /* 调整边角 */
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  z-index: 1001; /* 确保在其他元素之上 */
}

/* 折叠按钮图标 */
.label-panel-toggle i {
  transition: transform 0.3s;
  font-size: 20px;
}

/* 折叠状态下的调整 */
.label-panel-toggle.collapsed {
  left: -100px;
  background: none;
  color: #716DCC;
  width: auto;
  height: auto;
}

.label-panel-toggle.collapsed::after {
  content: "数据标注";
  font-size: 16px;
  margin-left: 5px;
}

.label-panel-toggle.collapsed i {
  transform: rotate(180deg);
}

/* 键值对数据展示 */
.key-value-data {
  padding: 10px;
  font-size: 14px;
  color: #333;
}

.key-value-data .key {
  font-weight: bold;
  margin-right: 5px;
}

.key-value-data .value {
  color: #555;
}

/* 折叠状态调整 */
.floating-panel.collapsed {
  transform: translateY(-50%) translateX(100%);
}

.floating-panel.collapsed .tab-nav,
.floating-panel.collapsed .tab-content {
  display: none;
}

.floating-panel.collapsed .label-panel-toggle {
  left: -100px;
}

.floating-panel.collapsed .label-panel-toggle::after {
  content: "数据标注";
  font-size: 16px;
  margin-left: 5px;
}

.key-value-data {
  padding: 5px;
  font-size: 12px;
  color: #333;
  max-height: 300px;
  overflow-y: auto;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table tr {
  border-bottom: 1px solid #ddd;
}

.data-table tr:last-child {
  border-bottom: none;
}

.key-cell {
  font-weight: bold;
  padding: 4px 2px;
  width: 40%; /* 根据需要调整 */
  vertical-align: top;
  word-break: break-all;
}

.value-cell {
  padding: 4px 2px;
  width: 60%; /* 根据需要调整 */
  word-break: break-all;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.value-cell:hover {
  white-space: normal;
}

/* 数据项盒子 */
.data-item {
  border: 1px solid #ddd;
  border-radius: 5px;
  padding: 10px;
  margin-bottom: 10px;
  background-color: #fff;
  transition: transform 0.3s, box-shadow 0.3s, background 0.3s;
}

/* 针对不同类型的 data-item 进行样式设置 */
.data-item.type-required {
  /* 可根据需要为必填字段添加特殊样式 */
}

.data-item.type-optional {
  /* 可选字段的样式，默认即可 */
}

.data-item.type-readonly {
  background-color: #f5f5f5; /* 为只读字段设置浅灰色背景 */
}

.data-item.type-readonly .value {
  color: #888;          /* 灰色文本 */
  cursor: not-allowed;  /* 禁用指针 */
}

/* 字段名称样式 */
.data-item .key {
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
  font-size: 14px;
  display: flex;
  align-items: center;
}

/* 字段名称后面的指示器样式 */
.data-item .key span {
  margin-left: 5px;
}

/* 字段名称后面的图标样式 */
.data-item .key span i {
  margin-left: 5px;
  font-size: 14px;
  vertical-align: middle;
}
/* 值的样式 */
.data-item .value {
  color: #555;
  font-size: 13px;
  line-height: 1.4;
  word-wrap: break-word;
}

/* 鼠标悬停效果 */
.data-item:hover {
  background: linear-gradient(135deg, #f9f9f9 0%, #e0e0e0 100%);
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
}

/* 必填字段的星号样式 */
.data-item.type-required .key span {
  color: red;
}

/* 可选字段的 (可选) 标签颜色 */
.data-item.type-optional .key span {
  color: #888;
}

/* 只读字段的值样式 */
.value.readonly {
  color: #888;
  cursor: not-allowed;
}

/* 占位符样式 */
.value.placeholder {
  color: #888; /* 占位符文本颜色 */
}

/* 鼠标悬停动画 */
@keyframes hoverAnimation {
  0% {
      transform: translateY(0);
      box-shadow: 0 0 0 rgba(0, 0, 0, 0);
  }
  50% {
      transform: translateY(-3px);
      box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
  }
  100% {
      transform: translateY(-5px);
      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
  }
}

.data-item:hover,
.label-button:hover {
  animation: hoverAnimation 0.3s forwards;
  background: linear-gradient(135deg, #f9f9f9 0%, #e0e0e0 100%);
}

.data-item:hover,
.label-button:hover {
  box-shadow: 0 0 15px #716DCC;
}

.value.placeholder {
  color: #888; /* 占位符文本颜色 */
}

.value:hover {
  background-color: #f0f0f0;
  cursor: pointer;
}

#drugDictionaryEditorContainer {
  max-height: 600px; /* 设定最大高度，可根据需求调整 */
  overflow-y: auto;   /* 当内容超出时显示垂直滚动条 */
}

#imageResultsText, #narrativeResultsText, .json-edit-wrap {
  height: calc(100vh - 300px);
  overflow: auto;
}

.modal-dialog {
  margin: 10px auto !important;
}

#exportTab .nav-link i.fas{
  display: none;
}
#exportTab .nav-link.active i.fas{
  display: inline-block;
  margin: 0 6px;
  color: var(--bs-nav-link-color);
}