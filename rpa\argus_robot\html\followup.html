<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自适应数据表格（支持字段顺序、拖拽、批量修改状态）</title>
    <style>
        body {
            font-family: Arial, sans-serif;
        }

        .actions {
            margin-bottom: 10px;
        }

        .actions button, .actions select {
            margin-left: 10px;
            padding: 5px 10px;
            font-size: 14px;
        }

        .actions label {
            font-size: 14px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            table-layout: fixed;
            word-wrap: break-word;
        }

        th, td {
            border: 1px solid #ddd;
            padding: 8px 10px;
            text-align: left;
            font-size: 14px;
        }

        th {
            background-color: #f5f5f5;
        }

        tr.added {
            background-color: #e6ffed; /* 新增记录，淡绿色背景 */
        }

        tr.modified {
            background-color: #fffbe6; /* 修改记录，淡黄色背景 */
        }

        tr.deleted {
            background-color: #ffe6e6; /* 已删除记录，淡红色背景 */
            text-decoration: line-through;
        }

        tr.matched {
            /* 完全匹配的记录，默认样式 */
        }

        .highlight {
            background-color: #ffd666; /* 变化的字段，高亮黄色 */
        }

        /* 拖拽样式 */
        tr.dragging {
            opacity: 0.5;
            border: 2px dashed #aaa;
        }

        /* 选中样式 */
        tr.selected {
            background-color: #d0e7ff;
        }

        /* 鼠标悬停效果 */
        tr:hover {
            background-color: #f1f1f1;
        }

        /* 美化滚动条 */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        ::-webkit-scrollbar-thumb {
            background-color: #ccc;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-track {
            background-color: #f5f5f5;
        }

        /* 状态图标 */
        .icon {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 5px;
        }

        /* 新增状态图标 - 绿色 */
        .icon.added::before {
            content: '';
            display: inline-block;
            width: 10px;
            height: 10px;
            background-color: #28a745; /* 绿色 */
            border-radius: 50%;
        }

        /* 修改状态图标 - 黄色 */
        .icon.modified::before {
            content: '';
            display: inline-block;
            width: 10px;
            height: 10px;
            background-color: #ffc107; /* 黄色 */
            border-radius: 50%;
        }

        /* 删除状态图标 - 红色 */
        .icon.deleted::before {
            content: '';
            display: inline-block;
            width: 10px;
            height: 10px;
            background-color: #dc3545; /* 红色 */
            border-radius: 50%;
        }

        /* 完全匹配状态图标 - 蓝色 */
        .icon.matched::before {
            content: '';
            display: inline-block;
            width: 10px;
            height: 10px;
            background-color: #17a2b8; /* 蓝色 */
            border-radius: 50%;
        }

        /* 自定义复选框容器 */
        .custom-checkbox {
            position: relative;
            display: inline-block;
            width: 20px;
            height: 20px;
            cursor: pointer;
        }

        /* 隐藏原生复选框 */
        .custom-checkbox input {
            opacity: 0;
            width: 0;
            height: 0;
            position: absolute;
        }

        /* 自定义复选框容器 */
        .custom-checkbox {
            position: relative;
            display: inline-block;
            width: 20px;
            height: 20px;
            cursor: pointer;
        }

        /* 隐藏原生复选框 */
        .custom-checkbox input {
            opacity: 0;
            width: 0;
            height: 0;
            position: absolute;
        }

        /* 三角形复选框样式 */
        .custom-checkbox span {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            transform: translate(-50%, -50%);
            border-left: 8px solid transparent;
            border-right: 8px solid transparent;
            border-bottom: 14px solid #ddd; /* 默认颜色 */
        }

        /* 选中样式 */
        .custom-checkbox input:checked + span {
            border-bottom-color: #007bff; /* 默认选中状态颜色 */
        }

        /* 根据状态改变三角形颜色 */
        .custom-checkbox.added input:checked + span {
            border-bottom-color: #28a745; /* 绿色 */
        }

        .custom-checkbox.modified input:checked + span {
            border-bottom-color: #ffc107; /* 黄色 */
        }

        .custom-checkbox.deleted input:checked + span {
            border-bottom-color: #dc3545; /* 红色 */
        }

        .custom-checkbox.matched input:checked + span {
            border-bottom-color: #17a2b8; /* 蓝色 */
        }
    </style>
</head>
<body>

<div class="actions">
    <label><input type="checkbox" id="selectAll"> 全选</label>
    <select id="batchStatus">
        <option value="">批量修改状态</option>
        <option value="added">设为新增</option>
        <option value="modified">设为修改</option>
        <option value="deleted">设为已删除</option>
        <option value="matched">设为完全匹配</option>
    </select>
    <button id="mergeButton">合并</button> <!-- 新增合并按钮 -->
    <button id="finishButton">完成</button>
</div>

<table id="dataTable">
    <thead>
    <tr id="tableHeader">
        <!-- 表头将在JavaScript中动态生成 -->
    </tr>
    </thead>
    <tbody>
    <!-- 数据行将在JavaScript中生成 -->
    </tbody>
</table>
<script>
    document.write('<script src="../js/followup.js?_v='+Date.now()+'"><\/script>');
</script> 
</body>
</html>