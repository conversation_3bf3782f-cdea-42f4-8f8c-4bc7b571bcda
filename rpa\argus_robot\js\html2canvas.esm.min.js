var extendStatics=function(A,e){return(extendStatics=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(A,e){A.__proto__=e}||function(A,e){for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&(A[t]=e[t])})(A,e)};function __extends(A,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function t(){this.constructor=A}extendStatics(A,e),A.prototype=null===e?Object.create(e):(t.prototype=e.prototype,new t)}var __assign=function(){return(__assign=Object.assign||function(A){for(var e,t=1,r=arguments.length;t<r;t++)for(var n in e=arguments[t])Object.prototype.hasOwnProperty.call(e,n)&&(A[n]=e[n]);return A}).apply(this,arguments)};function __awaiter(A,o,s,i){return new(s=s||Promise)(function(t,e){function r(A){try{B(i.next(A))}catch(A){e(A)}}function n(A){try{B(i.throw(A))}catch(A){e(A)}}function B(A){var e;A.done?t(A.value):((e=A.value)instanceof s?e:new s(function(A){A(e)})).then(r,n)}B((i=i.apply(A,o||[])).next())})}function __generator(t,r){var n,B,o,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]},A={next:e(0),throw:e(1),return:e(2)};return"function"==typeof Symbol&&(A[Symbol.iterator]=function(){return this}),A;function e(e){return function(A){return function(e){if(n)throw new TypeError("Generator is already executing.");for(;s;)try{if(n=1,B&&(o=2&e[0]?B.return:e[0]?B.throw||((o=B.return)&&o.call(B),0):B.next)&&!(o=o.call(B,e[1])).done)return o;switch(B=0,(e=o?[2&e[0],o.value]:e)[0]){case 0:case 1:o=e;break;case 4:return s.label++,{value:e[1],done:!1};case 5:s.label++,B=e[1],e=[0];continue;case 7:e=s.ops.pop(),s.trys.pop();continue;default:if(!(o=0<(o=s.trys).length&&o[o.length-1])&&(6===e[0]||2===e[0])){s=0;continue}if(3===e[0]&&(!o||e[1]>o[0]&&e[1]<o[3])){s.label=e[1];break}if(6===e[0]&&s.label<o[1]){s.label=o[1],o=e;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(e);break}o[2]&&s.ops.pop(),s.trys.pop();continue}e=r.call(t,s)}catch(A){e=[6,A],B=0}finally{n=o=0}if(5&e[0])throw e[1];return{value:e[0]?e[1]:void 0,done:!0}}([e,A])}}}function __spreadArray(A,e,t){if(t||2===arguments.length)for(var r,n=0,B=e.length;n<B;n++)!r&&n in e||((r=r||Array.prototype.slice.call(e,0,n))[n]=e[n]);return A.concat(r||e)}for(var Bounds=function(){function n(A,e,t,r){this.left=A,this.top=e,this.width=t,this.height=r}return n.prototype.add=function(A,e,t,r){return new n(this.left+A,this.top+e,this.width+t,this.height+r)},n.fromClientRect=function(A,e){return new n(e.left+A.windowBounds.left,e.top+A.windowBounds.top,e.width,e.height)},n.fromDOMRectList=function(A,e){e=Array.from(e).find(function(A){return 0!==A.width});return e?new n(e.left+A.windowBounds.left,e.top+A.windowBounds.top,e.width,e.height):n.EMPTY},n.EMPTY=new n(0,0,0,0),n}(),parseBounds=function(A,e){return Bounds.fromClientRect(A,e.getBoundingClientRect())},parseDocumentSize=function(A){var e=A.body,t=A.documentElement;if(!e||!t)throw new Error("Unable to get document size");A=Math.max(Math.max(e.scrollWidth,t.scrollWidth),Math.max(e.offsetWidth,t.offsetWidth),Math.max(e.clientWidth,t.clientWidth)),t=Math.max(Math.max(e.scrollHeight,t.scrollHeight),Math.max(e.offsetHeight,t.offsetHeight),Math.max(e.clientHeight,t.clientHeight));return new Bounds(0,0,A,t)},toCodePoints$1=function(A){for(var e=[],t=0,r=A.length;t<r;){var n,B=A.charCodeAt(t++);55296<=B&&B<=56319&&t<r?56320==(64512&(n=A.charCodeAt(t++)))?e.push(((1023&B)<<10)+(1023&n)+65536):(e.push(B),t--):e.push(B)}return e},fromCodePoint$1=function(){for(var A=[],e=0;e<arguments.length;e++)A[e]=arguments[e];if(String.fromCodePoint)return String.fromCodePoint.apply(String,A);var t=A.length;if(!t)return"";for(var r=[],n=-1,B="";++n<t;){var o=A[n];o<=65535?r.push(o):(o-=65536,r.push(55296+(o>>10),o%1024+56320)),(n+1===t||16384<r.length)&&(B+=String.fromCharCode.apply(String,r),r.length=0)}return B},chars$2="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",lookup$2="undefined"==typeof Uint8Array?[]:new Uint8Array(256),i$2=0;i$2<chars$2.length;i$2++)lookup$2[chars$2.charCodeAt(i$2)]=i$2;for(var chars$1$1="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",lookup$1$1="undefined"==typeof Uint8Array?[]:new Uint8Array(256),i$1$1=0;i$1$1<chars$1$1.length;i$1$1++)lookup$1$1[chars$1$1.charCodeAt(i$1$1)]=i$1$1;for(var decode$1=function(A){var e,t,r,n,B=.75*A.length,o=A.length,s=0;"="===A[A.length-1]&&(B--,"="===A[A.length-2]&&B--);for(var B=new("undefined"!=typeof ArrayBuffer&&"undefined"!=typeof Uint8Array&&void 0!==Uint8Array.prototype.slice?ArrayBuffer:Array)(B),i=Array.isArray(B)?B:new Uint8Array(B),a=0;a<o;a+=4)e=lookup$1$1[A.charCodeAt(a)],t=lookup$1$1[A.charCodeAt(a+1)],r=lookup$1$1[A.charCodeAt(a+2)],n=lookup$1$1[A.charCodeAt(a+3)],i[s++]=e<<2|t>>4,i[s++]=(15&t)<<4|r>>2,i[s++]=(3&r)<<6|63&n;return B},polyUint16Array$1=function(A){for(var e=A.length,t=[],r=0;r<e;r+=2)t.push(A[r+1]<<8|A[r]);return t},polyUint32Array$1=function(A){for(var e=A.length,t=[],r=0;r<e;r+=4)t.push(A[r+3]<<24|A[r+2]<<16|A[r+1]<<8|A[r]);return t},UTRIE2_SHIFT_2$1=5,UTRIE2_SHIFT_1$1=11,UTRIE2_INDEX_SHIFT$1=2,UTRIE2_SHIFT_1_2$1=UTRIE2_SHIFT_1$1-UTRIE2_SHIFT_2$1,UTRIE2_LSCP_INDEX_2_OFFSET$1=65536>>UTRIE2_SHIFT_2$1,UTRIE2_DATA_BLOCK_LENGTH$1=1<<UTRIE2_SHIFT_2$1,UTRIE2_DATA_MASK$1=UTRIE2_DATA_BLOCK_LENGTH$1-1,UTRIE2_LSCP_INDEX_2_LENGTH$1=1024>>UTRIE2_SHIFT_2$1,UTRIE2_INDEX_2_BMP_LENGTH$1=UTRIE2_LSCP_INDEX_2_OFFSET$1+UTRIE2_LSCP_INDEX_2_LENGTH$1,UTRIE2_UTF8_2B_INDEX_2_OFFSET$1=UTRIE2_INDEX_2_BMP_LENGTH$1,UTRIE2_UTF8_2B_INDEX_2_LENGTH$1=32,UTRIE2_INDEX_1_OFFSET$1=UTRIE2_INDEX_2_BMP_LENGTH$1+UTRIE2_UTF8_2B_INDEX_2_LENGTH$1,UTRIE2_OMITTED_BMP_INDEX_1_LENGTH$1=65536>>UTRIE2_SHIFT_1$1,UTRIE2_INDEX_2_BLOCK_LENGTH$1=1<<UTRIE2_SHIFT_1_2$1,UTRIE2_INDEX_2_MASK$1=UTRIE2_INDEX_2_BLOCK_LENGTH$1-1,slice16$1=function(A,e,t){return A.slice?A.slice(e,t):new Uint16Array(Array.prototype.slice.call(A,e,t))},slice32$1=function(A,e,t){return A.slice?A.slice(e,t):new Uint32Array(Array.prototype.slice.call(A,e,t))},createTrieFromBase64$1=function(A,e){var t=decode$1(A),r=Array.isArray(t)?polyUint32Array$1(t):new Uint32Array(t),A=Array.isArray(t)?polyUint16Array$1(t):new Uint16Array(t),t=slice16$1(A,12,r[4]/2),A=2===r[5]?slice16$1(A,(24+r[4])/2):slice32$1(r,Math.ceil((24+r[4])/4));return new Trie$1(r[0],r[1],r[2],r[3],t,A)},Trie$1=function(){function A(A,e,t,r,n,B){this.initialValue=A,this.errorValue=e,this.highStart=t,this.highValueIndex=r,this.index=n,this.data=B}return A.prototype.get=function(A){var e;if(0<=A){if(A<55296||56319<A&&A<=65535)return e=this.index[A>>UTRIE2_SHIFT_2$1],this.data[e=(e<<UTRIE2_INDEX_SHIFT$1)+(A&UTRIE2_DATA_MASK$1)];if(A<=65535)return e=this.index[UTRIE2_LSCP_INDEX_2_OFFSET$1+(A-55296>>UTRIE2_SHIFT_2$1)],this.data[e=(e<<UTRIE2_INDEX_SHIFT$1)+(A&UTRIE2_DATA_MASK$1)];if(A<this.highStart)return e=this.index[e=UTRIE2_INDEX_1_OFFSET$1-UTRIE2_OMITTED_BMP_INDEX_1_LENGTH$1+(A>>UTRIE2_SHIFT_1$1)],e=this.index[e+=A>>UTRIE2_SHIFT_2$1&UTRIE2_INDEX_2_MASK$1],this.data[e=(e<<UTRIE2_INDEX_SHIFT$1)+(A&UTRIE2_DATA_MASK$1)];if(A<=1114111)return this.data[this.highValueIndex]}return this.errorValue},A}(),chars$3="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",lookup$3="undefined"==typeof Uint8Array?[]:new Uint8Array(256),i$3=0;i$3<chars$3.length;i$3++)lookup$3[chars$3.charCodeAt(i$3)]=i$3;var base64$1="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",LETTER_NUMBER_MODIFIER=50,BK=1,CR$1=2,LF$1=3,CM=4,NL=5,WJ=7,ZW=8,GL=9,SP=10,ZWJ$1=11,B2=12,BA=13,BB=14,HY=15,CB=16,CL=17,CP=18,EX=19,IN=20,NS=21,OP=22,QU=23,IS=24,NU=25,PO=26,PR=27,SY=28,AI=29,AL=30,CJ=31,EB=32,EM=33,H2=34,H3=35,HL=36,ID=37,JL=38,JV=39,JT=40,RI$1=41,SA=42,XX=43,ea_OP=[9001,65288],BREAK_MANDATORY="!",BREAK_NOT_ALLOWED$1="×",BREAK_ALLOWED$1="÷",UnicodeTrie$1=createTrieFromBase64$1(base64$1),ALPHABETICS=[AL,HL],HARD_LINE_BREAKS=[BK,CR$1,LF$1,NL],SPACE$1=[SP,ZW],PREFIX_POSTFIX=[PR,PO],LINE_BREAKS=HARD_LINE_BREAKS.concat(SPACE$1),KOREAN_SYLLABLE_BLOCK=[JL,JV,JT,H2,H3],HYPHEN=[HY,BA],codePointsToCharacterClasses=function(A,r){void 0===r&&(r="strict");var n=[],B=[],o=[];return A.forEach(function(A,e){var t=UnicodeTrie$1.get(A);if(LETTER_NUMBER_MODIFIER<t?(o.push(!0),t-=LETTER_NUMBER_MODIFIER):o.push(!1),-1!==["normal","auto","loose"].indexOf(r)&&-1!==[8208,8211,12316,12448].indexOf(A))return B.push(e),n.push(CB);if(t!==CM&&t!==ZWJ$1)return B.push(e),t===CJ?n.push("strict"===r?NS:ID):t===SA||t===AI?n.push(AL):t===XX?131072<=A&&A<=196605||196608<=A&&A<=262141?n.push(ID):n.push(AL):void n.push(t);if(0===e)return B.push(e),n.push(AL);t=n[e-1];return-1===LINE_BREAKS.indexOf(t)?(B.push(B[e-1]),n.push(t)):(B.push(e),n.push(AL))}),[B,n,o]},isAdjacentWithSpaceIgnored=function(A,e,t,r){var n=r[t];if(Array.isArray(A)?-1!==A.indexOf(n):A===n)for(var B=t;B<=r.length;){if((s=r[++B])===e)return!0;if(s!==SP)break}if(n===SP)for(B=t;0<B;){var o=r[--B];if(Array.isArray(A)?-1!==A.indexOf(o):A===o)for(var s,i=t;i<=r.length;){if((s=r[++i])===e)return!0;if(s!==SP)break}if(o!==SP)break}return!1},previousNonSpaceClassType=function(A,e){for(var t=A;0<=t;){var r=e[t];if(r!==SP)return r;t--}return 0},_lineBreakAtIndex=function(A,e,t,r,n){if(0===t[r])return BREAK_NOT_ALLOWED$1;var B=r-1;if(Array.isArray(n)&&!0===n[B])return BREAK_NOT_ALLOWED$1;var o=B-1,s=1+B,i=e[B],r=0<=o?e[o]:0,n=e[s];if(i===CR$1&&n===LF$1)return BREAK_NOT_ALLOWED$1;if(-1!==HARD_LINE_BREAKS.indexOf(i))return BREAK_MANDATORY;if(-1!==HARD_LINE_BREAKS.indexOf(n))return BREAK_NOT_ALLOWED$1;if(-1!==SPACE$1.indexOf(n))return BREAK_NOT_ALLOWED$1;if(previousNonSpaceClassType(B,e)===ZW)return BREAK_ALLOWED$1;if(UnicodeTrie$1.get(A[B])===ZWJ$1)return BREAK_NOT_ALLOWED$1;if((i===EB||i===EM)&&UnicodeTrie$1.get(A[s])===ZWJ$1)return BREAK_NOT_ALLOWED$1;if(i===WJ||n===WJ)return BREAK_NOT_ALLOWED$1;if(i===GL)return BREAK_NOT_ALLOWED$1;if(-1===[SP,BA,HY].indexOf(i)&&n===GL)return BREAK_NOT_ALLOWED$1;if(-1!==[CL,CP,EX,IS,SY].indexOf(n))return BREAK_NOT_ALLOWED$1;if(previousNonSpaceClassType(B,e)===OP)return BREAK_NOT_ALLOWED$1;if(isAdjacentWithSpaceIgnored(QU,OP,B,e))return BREAK_NOT_ALLOWED$1;if(isAdjacentWithSpaceIgnored([CL,CP],NS,B,e))return BREAK_NOT_ALLOWED$1;if(isAdjacentWithSpaceIgnored(B2,B2,B,e))return BREAK_NOT_ALLOWED$1;if(i===SP)return BREAK_ALLOWED$1;if(i===QU||n===QU)return BREAK_NOT_ALLOWED$1;if(n===CB||i===CB)return BREAK_ALLOWED$1;if(-1!==[BA,HY,NS].indexOf(n)||i===BB)return BREAK_NOT_ALLOWED$1;if(r===HL&&-1!==HYPHEN.indexOf(i))return BREAK_NOT_ALLOWED$1;if(i===SY&&n===HL)return BREAK_NOT_ALLOWED$1;if(n===IN)return BREAK_NOT_ALLOWED$1;if(-1!==ALPHABETICS.indexOf(n)&&i===NU||-1!==ALPHABETICS.indexOf(i)&&n===NU)return BREAK_NOT_ALLOWED$1;if(i===PR&&-1!==[ID,EB,EM].indexOf(n)||-1!==[ID,EB,EM].indexOf(i)&&n===PO)return BREAK_NOT_ALLOWED$1;if(-1!==ALPHABETICS.indexOf(i)&&-1!==PREFIX_POSTFIX.indexOf(n)||-1!==PREFIX_POSTFIX.indexOf(i)&&-1!==ALPHABETICS.indexOf(n))return BREAK_NOT_ALLOWED$1;if(-1!==[PR,PO].indexOf(i)&&(n===NU||-1!==[OP,HY].indexOf(n)&&e[1+s]===NU)||-1!==[OP,HY].indexOf(i)&&n===NU||i===NU&&-1!==[NU,SY,IS].indexOf(n))return BREAK_NOT_ALLOWED$1;if(-1!==[NU,SY,IS,CL,CP].indexOf(n))for(var a=B;0<=a;){if((c=e[a])===NU)return BREAK_NOT_ALLOWED$1;if(-1===[SY,IS].indexOf(c))break;a--}if(-1!==[PR,PO].indexOf(n))for(var c,a=-1!==[CL,CP].indexOf(i)?o:B;0<=a;){if((c=e[a])===NU)return BREAK_NOT_ALLOWED$1;if(-1===[SY,IS].indexOf(c))break;a--}if(JL===i&&-1!==[JL,JV,H2,H3].indexOf(n)||-1!==[JV,H2].indexOf(i)&&-1!==[JV,JT].indexOf(n)||-1!==[JT,H3].indexOf(i)&&n===JT)return BREAK_NOT_ALLOWED$1;if(-1!==KOREAN_SYLLABLE_BLOCK.indexOf(i)&&-1!==[IN,PO].indexOf(n)||-1!==KOREAN_SYLLABLE_BLOCK.indexOf(n)&&i===PR)return BREAK_NOT_ALLOWED$1;if(-1!==ALPHABETICS.indexOf(i)&&-1!==ALPHABETICS.indexOf(n))return BREAK_NOT_ALLOWED$1;if(i===IS&&-1!==ALPHABETICS.indexOf(n))return BREAK_NOT_ALLOWED$1;if(-1!==ALPHABETICS.concat(NU).indexOf(i)&&n===OP&&-1===ea_OP.indexOf(A[s])||-1!==ALPHABETICS.concat(NU).indexOf(n)&&i===CP)return BREAK_NOT_ALLOWED$1;if(i===RI$1&&n===RI$1){for(var g=t[B],Q=1;0<g&&e[--g]===RI$1;)Q++;if(Q%2!=0)return BREAK_NOT_ALLOWED$1}return i===EB&&n===EM?BREAK_NOT_ALLOWED$1:BREAK_ALLOWED$1},cssFormattedClasses=function(t,A){var e=codePointsToCharacterClasses(t,(A=A||{lineBreak:"normal",wordBreak:"normal"}).lineBreak),r=e[0],n=e[1],e=e[2];return[r,n="break-all"===A.wordBreak||"break-word"===A.wordBreak?n.map(function(A){return-1!==[NU,AL,SA].indexOf(A)?ID:A}):n,"keep-all"===A.wordBreak?e.map(function(A,e){return A&&19968<=t[e]&&t[e]<=40959}):void 0]},Break=function(){function A(A,e,t,r){this.codePoints=A,this.required=e===BREAK_MANDATORY,this.start=t,this.end=r}return A.prototype.slice=function(){return fromCodePoint$1.apply(void 0,this.codePoints.slice(this.start,this.end))},A}(),LineBreaker=function(A,e){var t=toCodePoints$1(A),e=cssFormattedClasses(t,e),r=e[0],n=e[1],B=e[2],o=t.length,s=0,i=0;return{next:function(){if(o<=i)return{done:!0,value:null};for(var A=BREAK_NOT_ALLOWED$1;i<o&&(A=_lineBreakAtIndex(t,n,r,++i,B))===BREAK_NOT_ALLOWED$1;);if(A===BREAK_NOT_ALLOWED$1&&i!==o)return{done:!0,value:null};var e=new Break(t,A,s,i);return s=i,{value:e,done:!1}}}},FLAG_UNRESTRICTED=1,FLAG_ID=2,FLAG_INTEGER=4,FLAG_NUMBER=8,LINE_FEED=10,SOLIDUS=47,REVERSE_SOLIDUS=92,CHARACTER_TABULATION=9,SPACE=32,QUOTATION_MARK=34,EQUALS_SIGN=61,NUMBER_SIGN=35,DOLLAR_SIGN=36,PERCENTAGE_SIGN=37,APOSTROPHE=39,LEFT_PARENTHESIS=40,RIGHT_PARENTHESIS=41,LOW_LINE=95,HYPHEN_MINUS=45,EXCLAMATION_MARK=33,LESS_THAN_SIGN=60,GREATER_THAN_SIGN=62,COMMERCIAL_AT=64,LEFT_SQUARE_BRACKET=91,RIGHT_SQUARE_BRACKET=93,CIRCUMFLEX_ACCENT=61,LEFT_CURLY_BRACKET=123,QUESTION_MARK=63,RIGHT_CURLY_BRACKET=125,VERTICAL_LINE=124,TILDE=126,CONTROL=128,REPLACEMENT_CHARACTER=65533,ASTERISK=42,PLUS_SIGN=43,COMMA=44,COLON=58,SEMICOLON=59,FULL_STOP=46,NULL=0,BACKSPACE=8,LINE_TABULATION=11,SHIFT_OUT=14,INFORMATION_SEPARATOR_ONE=31,DELETE=127,EOF=-1,ZERO=48,a=97,e=101,f=102,u=117,z=122,A=65,E=69,F=70,U=85,Z=90,isDigit=function(A){return ZERO<=A&&A<=57},isSurrogateCodePoint=function(A){return 55296<=A&&A<=57343},isHex=function(e){return isDigit(e)||A<=e&&e<=F||a<=e&&e<=f},isLowerCaseLetter=function(A){return a<=A&&A<=z},isUpperCaseLetter=function(e){return A<=e&&e<=Z},isLetter=function(A){return isLowerCaseLetter(A)||isUpperCaseLetter(A)},isNonASCIICodePoint=function(A){return CONTROL<=A},isWhiteSpace=function(A){return A===LINE_FEED||A===CHARACTER_TABULATION||A===SPACE},isNameStartCodePoint=function(A){return isLetter(A)||isNonASCIICodePoint(A)||A===LOW_LINE},isNameCodePoint=function(A){return isNameStartCodePoint(A)||isDigit(A)||A===HYPHEN_MINUS},isNonPrintableCodePoint=function(A){return NULL<=A&&A<=BACKSPACE||A===LINE_TABULATION||SHIFT_OUT<=A&&A<=INFORMATION_SEPARATOR_ONE||A===DELETE},isValidEscape=function(A,e){return A===REVERSE_SOLIDUS&&e!==LINE_FEED},isIdentifierStart=function(A,e,t){return A===HYPHEN_MINUS?isNameStartCodePoint(e)||isValidEscape(e,t):!!isNameStartCodePoint(A)||!(A!==REVERSE_SOLIDUS||!isValidEscape(A,e))},isNumberStart=function(A,e,t){return A===PLUS_SIGN||A===HYPHEN_MINUS?!!isDigit(e)||e===FULL_STOP&&isDigit(t):isDigit(A===FULL_STOP?e:A)},stringToNumber=function(A){var t=0,r=1;A[t]!==PLUS_SIGN&&A[t]!==HYPHEN_MINUS||(A[t]===HYPHEN_MINUS&&(r=-1),t++);for(var n=[];isDigit(A[t]);)n.push(A[t++]);var B=n.length?parseInt(fromCodePoint$1.apply(void 0,n),10):0;A[t]===FULL_STOP&&t++;for(var o=[];isDigit(A[t]);)o.push(A[t++]);var s=o.length,i=s?parseInt(fromCodePoint$1.apply(void 0,o),10):0;A[t]!==E&&A[t]!==e||t++;var a=1;A[t]!==PLUS_SIGN&&A[t]!==HYPHEN_MINUS||(A[t]===HYPHEN_MINUS&&(a=-1),t++);for(var c=[];isDigit(A[t]);)c.push(A[t++]);var g=c.length?parseInt(fromCodePoint$1.apply(void 0,c),10):0;return r*(B+i*Math.pow(10,-s))*Math.pow(10,a*g)},LEFT_PARENTHESIS_TOKEN={type:2},RIGHT_PARENTHESIS_TOKEN={type:3},COMMA_TOKEN={type:4},SUFFIX_MATCH_TOKEN={type:13},PREFIX_MATCH_TOKEN={type:8},COLUMN_TOKEN={type:21},DASH_MATCH_TOKEN={type:9},INCLUDE_MATCH_TOKEN={type:10},LEFT_CURLY_BRACKET_TOKEN={type:11},RIGHT_CURLY_BRACKET_TOKEN={type:12},SUBSTRING_MATCH_TOKEN={type:14},BAD_URL_TOKEN={type:23},BAD_STRING_TOKEN={type:1},CDO_TOKEN={type:25},CDC_TOKEN={type:24},COLON_TOKEN={type:26},SEMICOLON_TOKEN={type:27},LEFT_SQUARE_BRACKET_TOKEN={type:28},RIGHT_SQUARE_BRACKET_TOKEN={type:29},WHITESPACE_TOKEN={type:31},EOF_TOKEN={type:32},Tokenizer=function(){function A(){this._value=[]}return A.prototype.write=function(A){this._value=this._value.concat(toCodePoints$1(A))},A.prototype.read=function(){for(var A=[],e=this.consumeToken();e!==EOF_TOKEN;)A.push(e),e=this.consumeToken();return A},A.prototype.consumeToken=function(){var A=this.consumeCodePoint();switch(A){case QUOTATION_MARK:return this.consumeStringToken(QUOTATION_MARK);case NUMBER_SIGN:var e=this.peekCodePoint(0),t=this.peekCodePoint(1),r=this.peekCodePoint(2);if(isNameCodePoint(e)||isValidEscape(t,r)){var n=isIdentifierStart(e,t,r)?FLAG_ID:FLAG_UNRESTRICTED;return{type:5,value:this.consumeName(),flags:n}}break;case DOLLAR_SIGN:if(this.peekCodePoint(0)===EQUALS_SIGN)return this.consumeCodePoint(),SUFFIX_MATCH_TOKEN;break;case APOSTROPHE:return this.consumeStringToken(APOSTROPHE);case LEFT_PARENTHESIS:return LEFT_PARENTHESIS_TOKEN;case RIGHT_PARENTHESIS:return RIGHT_PARENTHESIS_TOKEN;case ASTERISK:if(this.peekCodePoint(0)===EQUALS_SIGN)return this.consumeCodePoint(),SUBSTRING_MATCH_TOKEN;break;case PLUS_SIGN:if(isNumberStart(A,this.peekCodePoint(0),this.peekCodePoint(1)))return this.reconsumeCodePoint(A),this.consumeNumericToken();break;case COMMA:return COMMA_TOKEN;case HYPHEN_MINUS:var r=A,n=this.peekCodePoint(0),B=this.peekCodePoint(1);if(isNumberStart(r,n,B))return this.reconsumeCodePoint(A),this.consumeNumericToken();if(isIdentifierStart(r,n,B))return this.reconsumeCodePoint(A),this.consumeIdentLikeToken();if(n===HYPHEN_MINUS&&B===GREATER_THAN_SIGN)return this.consumeCodePoint(),this.consumeCodePoint(),CDC_TOKEN;break;case FULL_STOP:if(isNumberStart(A,this.peekCodePoint(0),this.peekCodePoint(1)))return this.reconsumeCodePoint(A),this.consumeNumericToken();break;case SOLIDUS:if(this.peekCodePoint(0)===ASTERISK)for(this.consumeCodePoint();;){var o=this.consumeCodePoint();if(o===ASTERISK&&(o=this.consumeCodePoint())===SOLIDUS)return this.consumeToken();if(o===EOF)return this.consumeToken()}break;case COLON:return COLON_TOKEN;case SEMICOLON:return SEMICOLON_TOKEN;case LESS_THAN_SIGN:if(this.peekCodePoint(0)===EXCLAMATION_MARK&&this.peekCodePoint(1)===HYPHEN_MINUS&&this.peekCodePoint(2)===HYPHEN_MINUS)return this.consumeCodePoint(),this.consumeCodePoint(),CDO_TOKEN;break;case COMMERCIAL_AT:var B=this.peekCodePoint(0),s=this.peekCodePoint(1),i=this.peekCodePoint(2);if(isIdentifierStart(B,s,i))return{type:7,value:this.consumeName()};break;case LEFT_SQUARE_BRACKET:return LEFT_SQUARE_BRACKET_TOKEN;case REVERSE_SOLIDUS:if(isValidEscape(A,this.peekCodePoint(0)))return this.reconsumeCodePoint(A),this.consumeIdentLikeToken();break;case RIGHT_SQUARE_BRACKET:return RIGHT_SQUARE_BRACKET_TOKEN;case CIRCUMFLEX_ACCENT:if(this.peekCodePoint(0)===EQUALS_SIGN)return this.consumeCodePoint(),PREFIX_MATCH_TOKEN;break;case LEFT_CURLY_BRACKET:return LEFT_CURLY_BRACKET_TOKEN;case RIGHT_CURLY_BRACKET:return RIGHT_CURLY_BRACKET_TOKEN;case u:case U:s=this.peekCodePoint(0),i=this.peekCodePoint(1);return s!==PLUS_SIGN||!isHex(i)&&i!==QUESTION_MARK||(this.consumeCodePoint(),this.consumeUnicodeRangeToken()),this.reconsumeCodePoint(A),this.consumeIdentLikeToken();case VERTICAL_LINE:if(this.peekCodePoint(0)===EQUALS_SIGN)return this.consumeCodePoint(),DASH_MATCH_TOKEN;if(this.peekCodePoint(0)===VERTICAL_LINE)return this.consumeCodePoint(),COLUMN_TOKEN;break;case TILDE:if(this.peekCodePoint(0)===EQUALS_SIGN)return this.consumeCodePoint(),INCLUDE_MATCH_TOKEN;break;case EOF:return EOF_TOKEN}return isWhiteSpace(A)?(this.consumeWhiteSpace(),WHITESPACE_TOKEN):isDigit(A)?(this.reconsumeCodePoint(A),this.consumeNumericToken()):isNameStartCodePoint(A)?(this.reconsumeCodePoint(A),this.consumeIdentLikeToken()):{type:6,value:fromCodePoint$1(A)}},A.prototype.consumeCodePoint=function(){var A=this._value.shift();return void 0===A?-1:A},A.prototype.reconsumeCodePoint=function(A){this._value.unshift(A)},A.prototype.peekCodePoint=function(A){return A>=this._value.length?-1:this._value[A]},A.prototype.consumeUnicodeRangeToken=function(){for(var A=[],e=this.consumeCodePoint();isHex(e)&&A.length<6;)A.push(e),e=this.consumeCodePoint();for(var t=!1;e===QUESTION_MARK&&A.length<6;)A.push(e),e=this.consumeCodePoint(),t=!0;if(t)return{type:30,start:parseInt(fromCodePoint$1.apply(void 0,A.map(function(A){return A===QUESTION_MARK?ZERO:A})),16),end:parseInt(fromCodePoint$1.apply(void 0,A.map(function(A){return A===QUESTION_MARK?F:A})),16)};var r=parseInt(fromCodePoint$1.apply(void 0,A),16);if(this.peekCodePoint(0)===HYPHEN_MINUS&&isHex(this.peekCodePoint(1))){this.consumeCodePoint();for(var e=this.consumeCodePoint(),n=[];isHex(e)&&n.length<6;)n.push(e),e=this.consumeCodePoint();return{type:30,start:r,end:parseInt(fromCodePoint$1.apply(void 0,n),16)}}return{type:30,start:r,end:r}},A.prototype.consumeIdentLikeToken=function(){var A=this.consumeName();return"url"===A.toLowerCase()&&this.peekCodePoint(0)===LEFT_PARENTHESIS?(this.consumeCodePoint(),this.consumeUrlToken()):this.peekCodePoint(0)===LEFT_PARENTHESIS?(this.consumeCodePoint(),{type:19,value:A}):{type:20,value:A}},A.prototype.consumeUrlToken=function(){var A=[];if(this.consumeWhiteSpace(),this.peekCodePoint(0)===EOF)return{type:22,value:""};var e=this.peekCodePoint(0);if(e===APOSTROPHE||e===QUOTATION_MARK){e=this.consumeStringToken(this.consumeCodePoint());return 0===e.type&&(this.consumeWhiteSpace(),this.peekCodePoint(0)===EOF||this.peekCodePoint(0)===RIGHT_PARENTHESIS)?(this.consumeCodePoint(),{type:22,value:e.value}):(this.consumeBadUrlRemnants(),BAD_URL_TOKEN)}for(;;){var t=this.consumeCodePoint();if(t===EOF||t===RIGHT_PARENTHESIS)return{type:22,value:fromCodePoint$1.apply(void 0,A)};if(isWhiteSpace(t))return this.consumeWhiteSpace(),this.peekCodePoint(0)===EOF||this.peekCodePoint(0)===RIGHT_PARENTHESIS?(this.consumeCodePoint(),{type:22,value:fromCodePoint$1.apply(void 0,A)}):(this.consumeBadUrlRemnants(),BAD_URL_TOKEN);if(t===QUOTATION_MARK||t===APOSTROPHE||t===LEFT_PARENTHESIS||isNonPrintableCodePoint(t))return this.consumeBadUrlRemnants(),BAD_URL_TOKEN;if(t===REVERSE_SOLIDUS){if(!isValidEscape(t,this.peekCodePoint(0)))return this.consumeBadUrlRemnants(),BAD_URL_TOKEN;A.push(this.consumeEscapedCodePoint())}else A.push(t)}},A.prototype.consumeWhiteSpace=function(){for(;isWhiteSpace(this.peekCodePoint(0));)this.consumeCodePoint()},A.prototype.consumeBadUrlRemnants=function(){for(;;){var A=this.consumeCodePoint();if(A===RIGHT_PARENTHESIS||A===EOF)return;isValidEscape(A,this.peekCodePoint(0))&&this.consumeEscapedCodePoint()}},A.prototype.consumeStringSlice=function(A){for(var e="";0<A;){var t=Math.min(5e4,A);e+=fromCodePoint$1.apply(void 0,this._value.splice(0,t)),A-=t}return this._value.shift(),e},A.prototype.consumeStringToken=function(A){for(var e="",t=0;;){var r,n=this._value[t];if(n===EOF||void 0===n||n===A)return{type:0,value:e+=this.consumeStringSlice(t)};if(n===LINE_FEED)return this._value.splice(0,t),BAD_STRING_TOKEN;n!==REVERSE_SOLIDUS||(r=this._value[t+1])!==EOF&&void 0!==r&&(r===LINE_FEED?(e+=this.consumeStringSlice(t),t=-1,this._value.shift()):isValidEscape(n,r)&&(e+=this.consumeStringSlice(t),e+=fromCodePoint$1(this.consumeEscapedCodePoint()),t=-1)),t++}},A.prototype.consumeNumber=function(){var A=[],t=FLAG_INTEGER;for((r=this.peekCodePoint(0))!==PLUS_SIGN&&r!==HYPHEN_MINUS||A.push(this.consumeCodePoint());isDigit(this.peekCodePoint(0));)A.push(this.consumeCodePoint());var r=this.peekCodePoint(0),n=this.peekCodePoint(1);if(r===FULL_STOP&&isDigit(n))for(A.push(this.consumeCodePoint(),this.consumeCodePoint()),t=FLAG_NUMBER;isDigit(this.peekCodePoint(0));)A.push(this.consumeCodePoint());r=this.peekCodePoint(0);var n=this.peekCodePoint(1),B=this.peekCodePoint(2);if((r===E||r===e)&&((n===PLUS_SIGN||n===HYPHEN_MINUS)&&isDigit(B)||isDigit(n)))for(A.push(this.consumeCodePoint(),this.consumeCodePoint()),t=FLAG_NUMBER;isDigit(this.peekCodePoint(0));)A.push(this.consumeCodePoint());return[stringToNumber(A),t]},A.prototype.consumeNumericToken=function(){var A=this.consumeNumber(),e=A[0],t=A[1],r=this.peekCodePoint(0),n=this.peekCodePoint(1),A=this.peekCodePoint(2);return isIdentifierStart(r,n,A)?{type:15,number:e,flags:t,unit:this.consumeName()}:r===PERCENTAGE_SIGN?(this.consumeCodePoint(),{type:16,number:e,flags:t}):{type:17,number:e,flags:t}},A.prototype.consumeEscapedCodePoint=function(){var A=this.consumeCodePoint();if(isHex(A)){for(var e=fromCodePoint$1(A);isHex(this.peekCodePoint(0))&&e.length<6;)e+=fromCodePoint$1(this.consumeCodePoint());isWhiteSpace(this.peekCodePoint(0))&&this.consumeCodePoint();var t=parseInt(e,16);return 0===t||isSurrogateCodePoint(t)||1114111<t?REPLACEMENT_CHARACTER:t}return A===EOF?REPLACEMENT_CHARACTER:A},A.prototype.consumeName=function(){for(var A="";;){var e=this.consumeCodePoint();if(isNameCodePoint(e))A+=fromCodePoint$1(e);else{if(!isValidEscape(e,this.peekCodePoint(0)))return this.reconsumeCodePoint(e),A;A+=fromCodePoint$1(this.consumeEscapedCodePoint())}}},A}(),Parser=function(){function t(A){this._tokens=A}return t.create=function(A){var e=new Tokenizer;return e.write(A),new t(e.read())},t.parseValue=function(A){return t.create(A).parseComponentValue()},t.parseValues=function(A){return t.create(A).parseComponentValues()},t.prototype.parseComponentValue=function(){for(var A=this.consumeToken();31===A.type;)A=this.consumeToken();if(32===A.type)throw new SyntaxError("Error parsing CSS component value, unexpected EOF");this.reconsumeToken(A);for(var e=this.consumeComponentValue();31===(A=this.consumeToken()).type;);if(32===A.type)return e;throw new SyntaxError("Error parsing CSS component value, multiple values found when expecting only one")},t.prototype.parseComponentValues=function(){for(var A=[];;){var e=this.consumeComponentValue();if(32===e.type)return A;A.push(e),A.push()}},t.prototype.consumeComponentValue=function(){var A=this.consumeToken();switch(A.type){case 11:case 28:case 2:return this.consumeSimpleBlock(A.type);case 19:return this.consumeFunction(A)}return A},t.prototype.consumeSimpleBlock=function(A){for(var e={type:A,values:[]},t=this.consumeToken();;){if(32===t.type||isEndingTokenFor(t,A))return e;this.reconsumeToken(t),e.values.push(this.consumeComponentValue()),t=this.consumeToken()}},t.prototype.consumeFunction=function(A){for(var e={name:A.value,values:[],type:18};;){var t=this.consumeToken();if(32===t.type||3===t.type)return e;this.reconsumeToken(t),e.values.push(this.consumeComponentValue())}},t.prototype.consumeToken=function(){var A=this._tokens.shift();return void 0===A?EOF_TOKEN:A},t.prototype.reconsumeToken=function(A){this._tokens.unshift(A)},t}(),isDimensionToken=function(A){return 15===A.type},isNumberToken=function(A){return 17===A.type},isIdentToken=function(A){return 20===A.type},isStringToken=function(A){return 0===A.type},isIdentWithValue=function(A,e){return isIdentToken(A)&&A.value===e},nonWhiteSpace=function(A){return 31!==A.type},nonFunctionArgSeparator=function(A){return 31!==A.type&&4!==A.type},parseFunctionArgs=function(A){var e=[],t=[];return A.forEach(function(A){if(4===A.type){if(0===t.length)throw new Error("Error parsing function args, zero tokens for arg");return e.push(t),void(t=[])}31!==A.type&&t.push(A)}),t.length&&e.push(t),e},isEndingTokenFor=function(A,e){return 11===e&&12===A.type||(28===e&&29===A.type||2===e&&3===A.type)},isLength=function(A){return 17===A.type||15===A.type},isLengthPercentage=function(A){return 16===A.type||isLength(A)},parseLengthPercentageTuple=function(A){return 1<A.length?[A[0],A[1]]:[A[0]]},ZERO_LENGTH={type:17,number:0,flags:FLAG_INTEGER},FIFTY_PERCENT={type:16,number:50,flags:FLAG_INTEGER},HUNDRED_PERCENT={type:16,number:100,flags:FLAG_INTEGER},getAbsoluteValueForTuple=function(A,e,t){var r=A[0],A=A[1];return[getAbsoluteValue(r,e),getAbsoluteValue(void 0!==A?A:r,t)]},getAbsoluteValue=function(A,e){if(16===A.type)return A.number/100*e;if(isDimensionToken(A))switch(A.unit){case"rem":case"em":return 16*A.number;default:return A.number}return A.number},DEG="deg",GRAD="grad",RAD="rad",TURN="turn",angle={name:"angle",parse:function(A,e){if(15===e.type)switch(e.unit){case DEG:return Math.PI*e.number/180;case GRAD:return Math.PI/200*e.number;case RAD:return e.number;case TURN:return 2*Math.PI*e.number}throw new Error("Unsupported angle type")}},isAngle=function(A){return 15===A.type&&(A.unit===DEG||A.unit===GRAD||A.unit===RAD||A.unit===TURN)},parseNamedSide=function(A){switch(A.filter(isIdentToken).map(function(A){return A.value}).join(" ")){case"to bottom right":case"to right bottom":case"left top":case"top left":return[ZERO_LENGTH,ZERO_LENGTH];case"to top":case"bottom":return deg(0);case"to bottom left":case"to left bottom":case"right top":case"top right":return[ZERO_LENGTH,HUNDRED_PERCENT];case"to right":case"left":return deg(90);case"to top left":case"to left top":case"right bottom":case"bottom right":return[HUNDRED_PERCENT,HUNDRED_PERCENT];case"to bottom":case"top":return deg(180);case"to top right":case"to right top":case"left bottom":case"bottom left":return[HUNDRED_PERCENT,ZERO_LENGTH];case"to left":case"right":return deg(270)}return 0},deg=function(A){return Math.PI*A/180},color$1={name:"color",parse:function(A,e){if(18===e.type){var t=SUPPORTED_COLOR_FUNCTIONS[e.name];if(void 0===t)throw new Error('Attempting to parse an unsupported color function "'+e.name+'"');return t(A,e.values)}if(5===e.type){if(3===e.value.length){var r=e.value.substring(0,1),n=e.value.substring(1,2),B=e.value.substring(2,3);return pack(parseInt(r+r,16),parseInt(n+n,16),parseInt(B+B,16),1)}if(4===e.value.length){var r=e.value.substring(0,1),n=e.value.substring(1,2),B=e.value.substring(2,3),o=e.value.substring(3,4);return pack(parseInt(r+r,16),parseInt(n+n,16),parseInt(B+B,16),parseInt(o+o,16)/255)}if(6===e.value.length){r=e.value.substring(0,2),n=e.value.substring(2,4),B=e.value.substring(4,6);return pack(parseInt(r,16),parseInt(n,16),parseInt(B,16),1)}if(8===e.value.length){r=e.value.substring(0,2),n=e.value.substring(2,4),B=e.value.substring(4,6),o=e.value.substring(6,8);return pack(parseInt(r,16),parseInt(n,16),parseInt(B,16),parseInt(o,16)/255)}}if(20===e.type){e=COLORS[e.value.toUpperCase()];if(void 0!==e)return e}return COLORS.TRANSPARENT}},isTransparent=function(A){return 0==(255&A)},asString=function(A){var e=255&A,t=255&A>>8,r=255&A>>16,A=255&A>>24;return e<255?"rgba("+A+","+r+","+t+","+e/255+")":"rgb("+A+","+r+","+t+")"},pack=function(A,e,t,r){return(A<<24|e<<16|t<<8|Math.round(255*r)<<0)>>>0},getTokenColorValue=function(A,e){if(17===A.type)return A.number;if(16!==A.type)return 0;var t=3===e?1:255;return 3===e?A.number/100*t:Math.round(A.number/100*t)},rgb=function(A,e){e=e.filter(nonFunctionArgSeparator);if(3===e.length){var t=e.map(getTokenColorValue),r=t[0],n=t[1],t=t[2];return pack(r,n,t,1)}if(4!==e.length)return 0;e=e.map(getTokenColorValue),r=e[0],n=e[1],t=e[2],e=e[3];return pack(r,n,t,e)};function hue2rgb(A,e,t){return t<0&&(t+=1),1<=t&&--t,t<1/6?(e-A)*t*6+A:t<.5?e:t<2/3?6*(e-A)*(2/3-t)+A:A}var hsl=function(A,e){var t=e.filter(nonFunctionArgSeparator),r=t[0],n=t[1],B=t[2],e=t[3],t=(17===r.type?deg(r.number):angle.parse(A,r))/(2*Math.PI),A=isLengthPercentage(n)?n.number/100:0,r=isLengthPercentage(B)?B.number/100:0,n=void 0!==e&&isLengthPercentage(e)?getAbsoluteValue(e,1):1;if(0==A)return pack(255*r,255*r,255*r,1);B=r<=.5?r*(1+A):r+A-r*A,e=2*r-B,A=hue2rgb(e,B,t+1/3),r=hue2rgb(e,B,t),t=hue2rgb(e,B,t-1/3);return pack(255*A,255*r,255*t,n)},SUPPORTED_COLOR_FUNCTIONS={hsl:hsl,hsla:hsl,rgb:rgb,rgba:rgb},parseColor=function(A,e){return color$1.parse(A,Parser.create(e).parseComponentValue())},COLORS={ALICEBLUE:4042850303,ANTIQUEWHITE:4209760255,AQUA:16777215,AQUAMARINE:2147472639,AZURE:4043309055,BEIGE:4126530815,BISQUE:4293182719,BLACK:255,BLANCHEDALMOND:4293643775,BLUE:65535,BLUEVIOLET:2318131967,BROWN:2771004159,BURLYWOOD:3736635391,CADETBLUE:1604231423,CHARTREUSE:2147418367,CHOCOLATE:3530104575,CORAL:4286533887,CORNFLOWERBLUE:1687547391,CORNSILK:4294499583,CRIMSON:3692313855,CYAN:16777215,DARKBLUE:35839,DARKCYAN:9145343,DARKGOLDENROD:3095837695,DARKGRAY:2846468607,DARKGREEN:6553855,DARKGREY:2846468607,DARKKHAKI:3182914559,DARKMAGENTA:2332068863,DARKOLIVEGREEN:1433087999,DARKORANGE:4287365375,DARKORCHID:2570243327,DARKRED:2332033279,DARKSALMON:3918953215,DARKSEAGREEN:2411499519,DARKSLATEBLUE:1211993087,DARKSLATEGRAY:793726975,DARKSLATEGREY:793726975,DARKTURQUOISE:13554175,DARKVIOLET:2483082239,DEEPPINK:4279538687,DEEPSKYBLUE:12582911,DIMGRAY:1768516095,DIMGREY:1768516095,DODGERBLUE:512819199,FIREBRICK:2988581631,FLORALWHITE:4294635775,FORESTGREEN:579543807,FUCHSIA:4278255615,GAINSBORO:3705462015,GHOSTWHITE:4177068031,GOLD:4292280575,GOLDENROD:3668254975,GRAY:2155905279,GREEN:8388863,GREENYELLOW:2919182335,GREY:2155905279,HONEYDEW:4043305215,HOTPINK:4285117695,INDIANRED:3445382399,INDIGO:1258324735,IVORY:4294963455,KHAKI:4041641215,LAVENDER:3873897215,LAVENDERBLUSH:4293981695,LAWNGREEN:2096890111,LEMONCHIFFON:4294626815,LIGHTBLUE:2916673279,LIGHTCORAL:4034953471,LIGHTCYAN:3774873599,LIGHTGOLDENRODYELLOW:4210742015,LIGHTGRAY:3553874943,LIGHTGREEN:2431553791,LIGHTGREY:3553874943,LIGHTPINK:4290167295,LIGHTSALMON:4288707327,LIGHTSEAGREEN:548580095,LIGHTSKYBLUE:2278488831,LIGHTSLATEGRAY:2005441023,LIGHTSLATEGREY:2005441023,LIGHTSTEELBLUE:2965692159,LIGHTYELLOW:4294959359,LIME:16711935,LIMEGREEN:852308735,LINEN:4210091775,MAGENTA:4278255615,MAROON:2147483903,MEDIUMAQUAMARINE:1724754687,MEDIUMBLUE:52735,MEDIUMORCHID:3126187007,MEDIUMPURPLE:2473647103,MEDIUMSEAGREEN:1018393087,MEDIUMSLATEBLUE:2070474495,MEDIUMSPRINGGREEN:16423679,MEDIUMTURQUOISE:1221709055,MEDIUMVIOLETRED:3340076543,MIDNIGHTBLUE:421097727,MINTCREAM:4127193855,MISTYROSE:4293190143,MOCCASIN:4293178879,NAVAJOWHITE:4292783615,NAVY:33023,OLDLACE:4260751103,OLIVE:2155872511,OLIVEDRAB:1804477439,ORANGE:4289003775,ORANGERED:4282712319,ORCHID:3664828159,PALEGOLDENROD:4008225535,PALEGREEN:2566625535,PALETURQUOISE:2951671551,PALEVIOLETRED:3681588223,PAPAYAWHIP:4293907967,PEACHPUFF:4292524543,PERU:3448061951,PINK:4290825215,PLUM:3718307327,POWDERBLUE:2967529215,PURPLE:2147516671,REBECCAPURPLE:1714657791,RED:4278190335,ROSYBROWN:3163525119,ROYALBLUE:1097458175,SADDLEBROWN:2336560127,SALMON:4202722047,SANDYBROWN:4104413439,SEAGREEN:780883967,SEASHELL:4294307583,SIENNA:2689740287,SILVER:3233857791,SKYBLUE:2278484991,SLATEBLUE:1784335871,SLATEGRAY:1887473919,SLATEGREY:1887473919,SNOW:4294638335,SPRINGGREEN:16744447,STEELBLUE:1182971135,TAN:3535047935,TEAL:8421631,THISTLE:3636451583,TOMATO:4284696575,TRANSPARENT:0,TURQUOISE:1088475391,VIOLET:4001558271,WHEAT:4125012991,WHITE:4294967295,WHITESMOKE:4126537215,YELLOW:4294902015,YELLOWGREEN:2597139199},backgroundClip={name:"background-clip",initialValue:"border-box",prefix:!1,type:1,parse:function(A,e){return e.map(function(A){if(isIdentToken(A))switch(A.value){case"padding-box":return 1;case"content-box":return 2}return 0})}},backgroundColor={name:"background-color",initialValue:"transparent",prefix:!1,type:3,format:"color"},parseColorStop=function(A,e){A=color$1.parse(A,e[0]),e=e[1];return e&&isLengthPercentage(e)?{color:A,stop:e}:{color:A,stop:null}},processColorStops=function(A,t){var e=A[0],r=A[A.length-1];null===e.stop&&(e.stop=ZERO_LENGTH),null===r.stop&&(r.stop=HUNDRED_PERCENT);for(var n=[],B=0,o=0;o<A.length;o++){var s=A[o].stop;null!==s?(B<(s=getAbsoluteValue(s,t))?n.push(s):n.push(B),B=s):n.push(null)}for(var i=null,o=0;o<n.length;o++){var a=n[o];if(null===a)null===i&&(i=o);else if(null!==i){for(var c=o-i,g=(a-n[i-1])/(1+c),Q=1;Q<=c;Q++)n[i+Q-1]=g*Q;i=null}}return A.map(function(A,e){return{color:A.color,stop:Math.max(Math.min(1,n[e]/t),0)}})},getAngleFromCorner=function(A,e,t){var r=e/2,n=t/2,r=getAbsoluteValue(A[0],e)-r,t=n-getAbsoluteValue(A[1],t);return(Math.atan2(t,r)+2*Math.PI)%(2*Math.PI)},calculateGradientDirection=function(A,e,t){var r="number"==typeof A?A:getAngleFromCorner(A,e,t),n=Math.abs(e*Math.sin(r))+Math.abs(t*Math.cos(r)),B=e/2,A=t/2,e=n/2,t=Math.sin(r-Math.PI/2)*e,e=Math.cos(r-Math.PI/2)*e;return[n,B-e,B+e,A-t,A+t]},distance=function(A,e){return Math.sqrt(A*A+e*e)},findCorner=function(A,e,n,B,o){return[[0,0],[0,e],[A,0],[A,e]].reduce(function(A,e){var t=e[0],r=e[1],r=distance(n-t,B-r);return(o?r<A.optimumDistance:r>A.optimumDistance)?{optimumCorner:e,optimumDistance:r}:A},{optimumDistance:o?1/0:-1/0,optimumCorner:null}).optimumCorner},calculateRadius=function(A,e,t,r,n){var B,o,s,i,a=0,c=0;switch(A.size){case 0:0===A.shape?a=c=Math.min(Math.abs(e),Math.abs(e-r),Math.abs(t),Math.abs(t-n)):1===A.shape&&(a=Math.min(Math.abs(e),Math.abs(e-r)),c=Math.min(Math.abs(t),Math.abs(t-n)));break;case 2:0===A.shape?a=c=Math.min(distance(e,t),distance(e,t-n),distance(e-r,t),distance(e-r,t-n)):1===A.shape&&(B=Math.min(Math.abs(t),Math.abs(t-n))/Math.min(Math.abs(e),Math.abs(e-r)),s=(o=findCorner(r,n,e,t,!0))[0],i=o[1],c=B*(a=distance(s-e,(i-t)/B)));break;case 1:0===A.shape?a=c=Math.max(Math.abs(e),Math.abs(e-r),Math.abs(t),Math.abs(t-n)):1===A.shape&&(a=Math.max(Math.abs(e),Math.abs(e-r)),c=Math.max(Math.abs(t),Math.abs(t-n)));break;case 3:0===A.shape?a=c=Math.max(distance(e,t),distance(e,t-n),distance(e-r,t),distance(e-r,t-n)):1===A.shape&&(B=Math.max(Math.abs(t),Math.abs(t-n))/Math.max(Math.abs(e),Math.abs(e-r)),s=(o=findCorner(r,n,e,t,!1))[0],i=o[1],c=B*(a=distance(s-e,(i-t)/B)))}return Array.isArray(A.size)&&(a=getAbsoluteValue(A.size[0],r),c=2===A.size.length?getAbsoluteValue(A.size[1],n):a),[a,c]},linearGradient=function(t,A){var r=deg(180),n=[];return parseFunctionArgs(A).forEach(function(A,e){if(0===e){e=A[0];if(20===e.type&&"to"===e.value)return void(r=parseNamedSide(A));if(isAngle(e))return void(r=angle.parse(t,e))}A=parseColorStop(t,A);n.push(A)}),{angle:r,stops:n,type:1}},prefixLinearGradient=function(t,A){var r=deg(180),n=[];return parseFunctionArgs(A).forEach(function(A,e){if(0===e){e=A[0];if(20===e.type&&-1!==["top","left","right","bottom"].indexOf(e.value))return void(r=parseNamedSide(A));if(isAngle(e))return void(r=(angle.parse(t,e)+deg(270))%deg(360))}A=parseColorStop(t,A);n.push(A)}),{angle:r,stops:n,type:1}},webkitGradient=function(r,A){var e=deg(180),n=[],B=1;return parseFunctionArgs(A).forEach(function(A,e){var t,A=A[0];if(0===e){if(isIdentToken(A)&&"linear"===A.value)return void(B=1);if(isIdentToken(A)&&"radial"===A.value)return void(B=2)}18===A.type&&("from"===A.name?(t=color$1.parse(r,A.values[0]),n.push({stop:ZERO_LENGTH,color:t})):"to"===A.name?(t=color$1.parse(r,A.values[0]),n.push({stop:HUNDRED_PERCENT,color:t})):"color-stop"!==A.name||2===(A=A.values.filter(nonFunctionArgSeparator)).length&&(t=color$1.parse(r,A[1]),A=A[0],isNumberToken(A)&&n.push({stop:{type:16,number:100*A.number,flags:A.flags},color:t})))}),1===B?{angle:(e+deg(180))%deg(360),stops:n,type:B}:{size:3,shape:0,stops:n,position:[],type:B}},CLOSEST_SIDE="closest-side",FARTHEST_SIDE="farthest-side",CLOSEST_CORNER="closest-corner",FARTHEST_CORNER="farthest-corner",CIRCLE="circle",ELLIPSE="ellipse",COVER="cover",CONTAIN="contain",radialGradient=function(n,A){var B=0,o=3,s=[],i=[];return parseFunctionArgs(A).forEach(function(A,e){var t,r=!0;0===e&&(t=!1,r=A.reduce(function(A,e){if(t)if(isIdentToken(e))switch(e.value){case"center":return i.push(FIFTY_PERCENT),A;case"top":case"left":return i.push(ZERO_LENGTH),A;case"right":case"bottom":return i.push(HUNDRED_PERCENT),A}else(isLengthPercentage(e)||isLength(e))&&i.push(e);else if(isIdentToken(e))switch(e.value){case CIRCLE:return B=0,!1;case ELLIPSE:return!(B=1);case"at":return!(t=!0);case CLOSEST_SIDE:return o=0,!1;case COVER:case FARTHEST_SIDE:return!(o=1);case CONTAIN:case CLOSEST_CORNER:return!(o=2);case FARTHEST_CORNER:return!(o=3)}else if(isLength(e)||isLengthPercentage(e))return(o=!Array.isArray(o)?[]:o).push(e),!1;return A},r)),r&&(A=parseColorStop(n,A),s.push(A))}),{size:o,shape:B,stops:s,position:i,type:2}},prefixRadialGradient=function(r,A){var n=0,B=3,o=[],s=[];return parseFunctionArgs(A).forEach(function(A,e){var t=!0;0===e?t=A.reduce(function(A,e){if(isIdentToken(e))switch(e.value){case"center":return s.push(FIFTY_PERCENT),!1;case"top":case"left":return s.push(ZERO_LENGTH),!1;case"right":case"bottom":return s.push(HUNDRED_PERCENT),!1}else if(isLengthPercentage(e)||isLength(e))return s.push(e),!1;return A},t):1===e&&(t=A.reduce(function(A,e){if(isIdentToken(e))switch(e.value){case CIRCLE:return n=0,!1;case ELLIPSE:return!(n=1);case CONTAIN:case CLOSEST_SIDE:return B=0,!1;case FARTHEST_SIDE:return!(B=1);case CLOSEST_CORNER:return!(B=2);case COVER:case FARTHEST_CORNER:return!(B=3)}else if(isLength(e)||isLengthPercentage(e))return(B=!Array.isArray(B)?[]:B).push(e),!1;return A},t)),t&&(A=parseColorStop(r,A),o.push(A))}),{size:B,shape:n,stops:o,position:s,type:2}},isLinearGradient=function(A){return 1===A.type},isRadialGradient=function(A){return 2===A.type},image={name:"image",parse:function(A,e){if(22===e.type){var t={url:e.value,type:0};return A.cache.addImage(e.value),t}if(18!==e.type)throw new Error("Unsupported image type "+e.type);t=SUPPORTED_IMAGE_FUNCTIONS[e.name];if(void 0===t)throw new Error('Attempting to parse an unsupported image function "'+e.name+'"');return t(A,e.values)}};function isSupportedImage(A){return!(20===A.type&&"none"===A.value||18===A.type&&!SUPPORTED_IMAGE_FUNCTIONS[A.name])}var BACKGROUND_SIZE,SUPPORTED_IMAGE_FUNCTIONS={"linear-gradient":linearGradient,"-moz-linear-gradient":prefixLinearGradient,"-ms-linear-gradient":prefixLinearGradient,"-o-linear-gradient":prefixLinearGradient,"-webkit-linear-gradient":prefixLinearGradient,"radial-gradient":radialGradient,"-moz-radial-gradient":prefixRadialGradient,"-ms-radial-gradient":prefixRadialGradient,"-o-radial-gradient":prefixRadialGradient,"-webkit-radial-gradient":prefixRadialGradient,"-webkit-gradient":webkitGradient},backgroundImage={name:"background-image",initialValue:"none",type:1,prefix:!1,parse:function(e,A){if(0===A.length)return[];var t=A[0];return 20===t.type&&"none"===t.value?[]:A.filter(function(A){return nonFunctionArgSeparator(A)&&isSupportedImage(A)}).map(function(A){return image.parse(e,A)})}},backgroundOrigin={name:"background-origin",initialValue:"border-box",prefix:!1,type:1,parse:function(A,e){return e.map(function(A){if(isIdentToken(A))switch(A.value){case"padding-box":return 1;case"content-box":return 2}return 0})}},backgroundPosition={name:"background-position",initialValue:"0% 0%",type:1,prefix:!1,parse:function(A,e){return parseFunctionArgs(e).map(function(A){return A.filter(isLengthPercentage)}).map(parseLengthPercentageTuple)}},backgroundRepeat={name:"background-repeat",initialValue:"repeat",prefix:!1,type:1,parse:function(A,e){return parseFunctionArgs(e).map(function(A){return A.filter(isIdentToken).map(function(A){return A.value}).join(" ")}).map(parseBackgroundRepeat)}},parseBackgroundRepeat=function(A){switch(A){case"no-repeat":return 1;case"repeat-x":case"repeat no-repeat":return 2;case"repeat-y":case"no-repeat repeat":return 3;default:return 0}};!function(A){A.AUTO="auto",A.CONTAIN="contain",A.COVER="cover"}(BACKGROUND_SIZE=BACKGROUND_SIZE||{});var LINE_BREAK,backgroundSize={name:"background-size",initialValue:"0",prefix:!1,type:1,parse:function(A,e){return parseFunctionArgs(e).map(function(A){return A.filter(isBackgroundSizeInfoToken)})}},isBackgroundSizeInfoToken=function(A){return isIdentToken(A)||isLengthPercentage(A)},borderColorForSide=function(A){return{name:"border-"+A+"-color",initialValue:"transparent",prefix:!1,type:3,format:"color"}},borderTopColor=borderColorForSide("top"),borderRightColor=borderColorForSide("right"),borderBottomColor=borderColorForSide("bottom"),borderLeftColor=borderColorForSide("left"),borderRadiusForSide=function(A){return{name:"border-radius-"+A,initialValue:"0 0",prefix:!1,type:1,parse:function(A,e){return parseLengthPercentageTuple(e.filter(isLengthPercentage))}}},borderTopLeftRadius=borderRadiusForSide("top-left"),borderTopRightRadius=borderRadiusForSide("top-right"),borderBottomRightRadius=borderRadiusForSide("bottom-right"),borderBottomLeftRadius=borderRadiusForSide("bottom-left"),borderStyleForSide=function(A){return{name:"border-"+A+"-style",initialValue:"solid",prefix:!1,type:2,parse:function(A,e){switch(e){case"none":return 0;case"dashed":return 2;case"dotted":return 3;case"double":return 4}return 1}}},borderTopStyle=borderStyleForSide("top"),borderRightStyle=borderStyleForSide("right"),borderBottomStyle=borderStyleForSide("bottom"),borderLeftStyle=borderStyleForSide("left"),borderWidthForSide=function(A){return{name:"border-"+A+"-width",initialValue:"0",type:0,prefix:!1,parse:function(A,e){return isDimensionToken(e)?e.number:0}}},borderTopWidth=borderWidthForSide("top"),borderRightWidth=borderWidthForSide("right"),borderBottomWidth=borderWidthForSide("bottom"),borderLeftWidth=borderWidthForSide("left"),color={name:"color",initialValue:"transparent",prefix:!1,type:3,format:"color"},direction={name:"direction",initialValue:"ltr",prefix:!1,type:2,parse:function(A,e){return"rtl"!==e?0:1}},display={name:"display",initialValue:"inline-block",prefix:!1,type:1,parse:function(A,e){return e.filter(isIdentToken).reduce(function(A,e){return A|parseDisplayValue(e.value)},0)}},parseDisplayValue=function(A){switch(A){case"block":case"-webkit-box":return 2;case"inline":return 4;case"run-in":return 8;case"flow":return 16;case"flow-root":return 32;case"table":return 64;case"flex":case"-webkit-flex":return 128;case"grid":case"-ms-grid":return 256;case"ruby":return 512;case"subgrid":return 1024;case"list-item":return 2048;case"table-row-group":return 4096;case"table-header-group":return 8192;case"table-footer-group":return 16384;case"table-row":return 32768;case"table-cell":return 65536;case"table-column-group":return 131072;case"table-column":return 262144;case"table-caption":return 524288;case"ruby-base":return 1048576;case"ruby-text":return 2097152;case"ruby-base-container":return 4194304;case"ruby-text-container":return 8388608;case"contents":return 16777216;case"inline-block":return 33554432;case"inline-list-item":return 67108864;case"inline-table":return 134217728;case"inline-flex":return 268435456;case"inline-grid":return 536870912}return 0},float={name:"float",initialValue:"none",prefix:!1,type:2,parse:function(A,e){switch(e){case"left":return 1;case"right":return 2;case"inline-start":return 3;case"inline-end":return 4}return 0}},letterSpacing={name:"letter-spacing",initialValue:"0",prefix:!1,type:0,parse:function(A,e){return!(20===e.type&&"normal"===e.value||17!==e.type&&15!==e.type)?e.number:0}};!function(A){A.NORMAL="normal",A.STRICT="strict"}(LINE_BREAK=LINE_BREAK||{});var WORD_BREAK,lineBreak={name:"line-break",initialValue:"normal",prefix:!1,type:2,parse:function(A,e){return"strict"!==e?LINE_BREAK.NORMAL:LINE_BREAK.STRICT}},lineHeight={name:"line-height",initialValue:"normal",prefix:!1,type:4},computeLineHeight=function(A,e){return isIdentToken(A)&&"normal"===A.value?1.2*e:17===A.type?e*A.number:isLengthPercentage(A)?getAbsoluteValue(A,e):e},listStyleImage={name:"list-style-image",initialValue:"none",type:0,prefix:!1,parse:function(A,e){return 20===e.type&&"none"===e.value?null:image.parse(A,e)}},listStylePosition={name:"list-style-position",initialValue:"outside",prefix:!1,type:2,parse:function(A,e){return"inside"!==e?1:0}},listStyleType={name:"list-style-type",initialValue:"none",prefix:!1,type:2,parse:function(A,e){switch(e){case"disc":return 0;case"circle":return 1;case"square":return 2;case"decimal":return 3;case"cjk-decimal":return 4;case"decimal-leading-zero":return 5;case"lower-roman":return 6;case"upper-roman":return 7;case"lower-greek":return 8;case"lower-alpha":return 9;case"upper-alpha":return 10;case"arabic-indic":return 11;case"armenian":return 12;case"bengali":return 13;case"cambodian":return 14;case"cjk-earthly-branch":return 15;case"cjk-heavenly-stem":return 16;case"cjk-ideographic":return 17;case"devanagari":return 18;case"ethiopic-numeric":return 19;case"georgian":return 20;case"gujarati":return 21;case"gurmukhi":case"hebrew":return 22;case"hiragana":return 23;case"hiragana-iroha":return 24;case"japanese-formal":return 25;case"japanese-informal":return 26;case"kannada":return 27;case"katakana":return 28;case"katakana-iroha":return 29;case"khmer":return 30;case"korean-hangul-formal":return 31;case"korean-hanja-formal":return 32;case"korean-hanja-informal":return 33;case"lao":return 34;case"lower-armenian":return 35;case"malayalam":return 36;case"mongolian":return 37;case"myanmar":return 38;case"oriya":return 39;case"persian":return 40;case"simp-chinese-formal":return 41;case"simp-chinese-informal":return 42;case"tamil":return 43;case"telugu":return 44;case"thai":return 45;case"tibetan":return 46;case"trad-chinese-formal":return 47;case"trad-chinese-informal":return 48;case"upper-armenian":return 49;case"disclosure-open":return 50;case"disclosure-closed":return 51;default:return-1}}},marginForSide=function(A){return{name:"margin-"+A,initialValue:"0",prefix:!1,type:4}},marginTop=marginForSide("top"),marginRight=marginForSide("right"),marginBottom=marginForSide("bottom"),marginLeft=marginForSide("left"),overflow={name:"overflow",initialValue:"visible",prefix:!1,type:1,parse:function(A,e){return e.filter(isIdentToken).map(function(A){switch(A.value){case"hidden":return 1;case"scroll":return 2;case"clip":return 3;case"auto":return 4;default:return 0}})}},overflowWrap={name:"overflow-wrap",initialValue:"normal",prefix:!1,type:2,parse:function(A,e){return"break-word"!==e?"normal":"break-word"}},paddingForSide=function(A){return{name:"padding-"+A,initialValue:"0",prefix:!1,type:3,format:"length-percentage"}},paddingTop=paddingForSide("top"),paddingRight=paddingForSide("right"),paddingBottom=paddingForSide("bottom"),paddingLeft=paddingForSide("left"),textAlign={name:"text-align",initialValue:"left",prefix:!1,type:2,parse:function(A,e){switch(e){case"right":return 2;case"center":case"justify":return 1;default:return 0}}},position={name:"position",initialValue:"static",prefix:!1,type:2,parse:function(A,e){switch(e){case"relative":return 1;case"absolute":return 2;case"fixed":return 3;case"sticky":return 4}return 0}},textShadow={name:"text-shadow",initialValue:"none",type:1,prefix:!1,parse:function(B,A){return 1===A.length&&isIdentWithValue(A[0],"none")?[]:parseFunctionArgs(A).map(function(A){for(var e={color:COLORS.TRANSPARENT,offsetX:ZERO_LENGTH,offsetY:ZERO_LENGTH,blur:ZERO_LENGTH},t=0,r=0;r<A.length;r++){var n=A[r];isLength(n)?(0===t?e.offsetX=n:1===t?e.offsetY=n:e.blur=n,t++):e.color=color$1.parse(B,n)}return e})}},textTransform={name:"text-transform",initialValue:"none",prefix:!1,type:2,parse:function(A,e){switch(e){case"uppercase":return 2;case"lowercase":return 1;case"capitalize":return 3}return 0}},transform$1={name:"transform",initialValue:"none",prefix:!0,type:0,parse:function(A,e){if(20===e.type&&"none"===e.value)return null;if(18!==e.type)return null;var t=SUPPORTED_TRANSFORM_FUNCTIONS[e.name];if(void 0===t)throw new Error('Attempting to parse an unsupported transform function "'+e.name+'"');return t(e.values)}},matrix=function(A){A=A.filter(function(A){return 17===A.type}).map(function(A){return A.number});return 6===A.length?A:null},matrix3d=function(A){var e=A.filter(function(A){return 17===A.type}).map(function(A){return A.number}),t=e[0],r=e[1];e[2],e[3];var n=e[4],B=e[5];e[6],e[7],e[8],e[9],e[10],e[11];var o=e[12],A=e[13];return e[14],e[15],16===e.length?[t,r,n,B,o,A]:null},SUPPORTED_TRANSFORM_FUNCTIONS={matrix:matrix,matrix3d:matrix3d},DEFAULT_VALUE={type:16,number:50,flags:FLAG_INTEGER},DEFAULT=[DEFAULT_VALUE,DEFAULT_VALUE],transformOrigin={name:"transform-origin",initialValue:"50% 50%",prefix:!0,type:1,parse:function(A,e){e=e.filter(isLengthPercentage);return 2!==e.length?DEFAULT:[e[0],e[1]]}},visibility={name:"visible",initialValue:"none",prefix:!1,type:2,parse:function(A,e){switch(e){case"hidden":return 1;case"collapse":return 2;default:return 0}}};!function(A){A.NORMAL="normal",A.BREAK_ALL="break-all",A.KEEP_ALL="keep-all"}(WORD_BREAK=WORD_BREAK||{});for(var wordBreak={name:"word-break",initialValue:"normal",prefix:!1,type:2,parse:function(A,e){switch(e){case"break-all":return WORD_BREAK.BREAK_ALL;case"keep-all":return WORD_BREAK.KEEP_ALL;default:return WORD_BREAK.NORMAL}}},zIndex={name:"z-index",initialValue:"auto",prefix:!1,type:0,parse:function(A,e){if(20===e.type)return{auto:!0,order:0};if(isNumberToken(e))return{auto:!1,order:e.number};throw new Error("Invalid z-index number parsed")}},time={name:"time",parse:function(A,e){if(15===e.type)switch(e.unit.toLowerCase()){case"s":return 1e3*e.number;case"ms":return e.number}throw new Error("Unsupported time type")}},opacity={name:"opacity",initialValue:"1",type:0,prefix:!1,parse:function(A,e){return isNumberToken(e)?e.number:1}},textDecorationColor={name:"text-decoration-color",initialValue:"transparent",prefix:!1,type:3,format:"color"},textDecorationLine={name:"text-decoration-line",initialValue:"none",prefix:!1,type:1,parse:function(A,e){return e.filter(isIdentToken).map(function(A){switch(A.value){case"underline":return 1;case"overline":return 2;case"line-through":return 3;case"none":return 4}return 0}).filter(function(A){return 0!==A})}},fontFamily={name:"font-family",initialValue:"",prefix:!1,type:1,parse:function(A,e){var t=[],r=[];return e.forEach(function(A){switch(A.type){case 20:case 0:t.push(A.value);break;case 17:t.push(A.number.toString());break;case 4:r.push(t.join(" ")),t.length=0}}),t.length&&r.push(t.join(" ")),r.map(function(A){return-1===A.indexOf(" ")?A:"'"+A+"'"})}},fontSize={name:"font-size",initialValue:"0",prefix:!1,type:3,format:"length"},fontWeight={name:"font-weight",initialValue:"normal",type:0,prefix:!1,parse:function(A,e){return isNumberToken(e)?e.number:!isIdentToken(e)||"bold"!==e.value?400:700}},fontVariant={name:"font-variant",initialValue:"none",type:1,prefix:!1,parse:function(A,e){return e.filter(isIdentToken).map(function(A){return A.value})}},fontStyle={name:"font-style",initialValue:"normal",prefix:!1,type:2,parse:function(A,e){switch(e){case"oblique":return"oblique";case"italic":return"italic";default:return"normal"}}},contains=function(A,e){return 0!=(A&e)},content={name:"content",initialValue:"none",type:1,prefix:!1,parse:function(A,e){if(0===e.length)return[];var t=e[0];return 20===t.type&&"none"===t.value?[]:e}},counterIncrement={name:"counter-increment",initialValue:"none",prefix:!0,type:1,parse:function(A,e){if(0===e.length)return null;var t=e[0];if(20===t.type&&"none"===t.value)return null;for(var r=[],n=e.filter(nonWhiteSpace),B=0;B<n.length;B++){var o=n[B],s=n[B+1];20===o.type&&(s=s&&isNumberToken(s)?s.number:1,r.push({counter:o.value,increment:s}))}return r}},counterReset={name:"counter-reset",initialValue:"none",prefix:!0,type:1,parse:function(A,e){if(0===e.length)return[];for(var t=[],r=e.filter(nonWhiteSpace),n=0;n<r.length;n++){var B=r[n],o=r[n+1];isIdentToken(B)&&"none"!==B.value&&(o=o&&isNumberToken(o)?o.number:0,t.push({counter:B.value,reset:o}))}return t}},duration={name:"duration",initialValue:"0s",prefix:!1,type:1,parse:function(e,A){return A.filter(isDimensionToken).map(function(A){return time.parse(e,A)})}},quotes={name:"quotes",initialValue:"none",prefix:!0,type:1,parse:function(A,e){if(0===e.length)return null;var t=e[0];if(20===t.type&&"none"===t.value)return null;var r=[],n=e.filter(isStringToken);if(n.length%2!=0)return null;for(var B=0;B<n.length;B+=2){var o=n[B].value,s=n[B+1].value;r.push({open:o,close:s})}return r}},getQuote=function(A,e,t){if(!A)return"";A=A[Math.min(e,A.length-1)];return A?t?A.open:A.close:""},boxShadow={name:"box-shadow",initialValue:"none",type:1,prefix:!1,parse:function(B,A){return 1===A.length&&isIdentWithValue(A[0],"none")?[]:parseFunctionArgs(A).map(function(A){for(var e={color:255,offsetX:ZERO_LENGTH,offsetY:ZERO_LENGTH,blur:ZERO_LENGTH,spread:ZERO_LENGTH,inset:!1},t=0,r=0;r<A.length;r++){var n=A[r];isIdentWithValue(n,"inset")?e.inset=!0:isLength(n)?(0===t?e.offsetX=n:1===t?e.offsetY=n:2===t?e.blur=n:e.spread=n,t++):e.color=color$1.parse(B,n)}return e})}},paintOrder={name:"paint-order",initialValue:"normal",prefix:!1,type:1,parse:function(A,e){var t=[];return e.filter(isIdentToken).forEach(function(A){switch(A.value){case"stroke":t.push(1);break;case"fill":t.push(0);break;case"markers":t.push(2)}}),[0,1,2].forEach(function(A){-1===t.indexOf(A)&&t.push(A)}),t}},webkitTextStrokeColor={name:"-webkit-text-stroke-color",initialValue:"currentcolor",prefix:!1,type:3,format:"color"},webkitTextStrokeWidth={name:"-webkit-text-stroke-width",initialValue:"0",type:0,prefix:!1,parse:function(A,e){return isDimensionToken(e)?e.number:0}},CSSParsedDeclaration=function(){function A(A,e){this.animationDuration=parse(A,duration,e.animationDuration),this.backgroundClip=parse(A,backgroundClip,e.backgroundClip),this.backgroundColor=parse(A,backgroundColor,e.backgroundColor),this.backgroundImage=parse(A,backgroundImage,e.backgroundImage),this.backgroundOrigin=parse(A,backgroundOrigin,e.backgroundOrigin),this.backgroundPosition=parse(A,backgroundPosition,e.backgroundPosition),this.backgroundRepeat=parse(A,backgroundRepeat,e.backgroundRepeat),this.backgroundSize=parse(A,backgroundSize,e.backgroundSize),this.borderTopColor=parse(A,borderTopColor,e.borderTopColor),this.borderRightColor=parse(A,borderRightColor,e.borderRightColor),this.borderBottomColor=parse(A,borderBottomColor,e.borderBottomColor),this.borderLeftColor=parse(A,borderLeftColor,e.borderLeftColor),this.borderTopLeftRadius=parse(A,borderTopLeftRadius,e.borderTopLeftRadius),this.borderTopRightRadius=parse(A,borderTopRightRadius,e.borderTopRightRadius),this.borderBottomRightRadius=parse(A,borderBottomRightRadius,e.borderBottomRightRadius),this.borderBottomLeftRadius=parse(A,borderBottomLeftRadius,e.borderBottomLeftRadius),this.borderTopStyle=parse(A,borderTopStyle,e.borderTopStyle),this.borderRightStyle=parse(A,borderRightStyle,e.borderRightStyle),this.borderBottomStyle=parse(A,borderBottomStyle,e.borderBottomStyle),this.borderLeftStyle=parse(A,borderLeftStyle,e.borderLeftStyle),this.borderTopWidth=parse(A,borderTopWidth,e.borderTopWidth),this.borderRightWidth=parse(A,borderRightWidth,e.borderRightWidth),this.borderBottomWidth=parse(A,borderBottomWidth,e.borderBottomWidth),this.borderLeftWidth=parse(A,borderLeftWidth,e.borderLeftWidth),this.boxShadow=parse(A,boxShadow,e.boxShadow),this.color=parse(A,color,e.color),this.direction=parse(A,direction,e.direction),this.display=parse(A,display,e.display),this.float=parse(A,float,e.cssFloat),this.fontFamily=parse(A,fontFamily,e.fontFamily),this.fontSize=parse(A,fontSize,e.fontSize),this.fontStyle=parse(A,fontStyle,e.fontStyle),this.fontVariant=parse(A,fontVariant,e.fontVariant),this.fontWeight=parse(A,fontWeight,e.fontWeight),this.letterSpacing=parse(A,letterSpacing,e.letterSpacing),this.lineBreak=parse(A,lineBreak,e.lineBreak),this.lineHeight=parse(A,lineHeight,e.lineHeight),this.listStyleImage=parse(A,listStyleImage,e.listStyleImage),this.listStylePosition=parse(A,listStylePosition,e.listStylePosition),this.listStyleType=parse(A,listStyleType,e.listStyleType),this.marginTop=parse(A,marginTop,e.marginTop),this.marginRight=parse(A,marginRight,e.marginRight),this.marginBottom=parse(A,marginBottom,e.marginBottom),this.marginLeft=parse(A,marginLeft,e.marginLeft),this.opacity=parse(A,opacity,e.opacity);var t=parse(A,overflow,e.overflow);this.overflowX=t[0],this.overflowY=t[1<t.length?1:0],this.overflowWrap=parse(A,overflowWrap,e.overflowWrap),this.paddingTop=parse(A,paddingTop,e.paddingTop),this.paddingRight=parse(A,paddingRight,e.paddingRight),this.paddingBottom=parse(A,paddingBottom,e.paddingBottom),this.paddingLeft=parse(A,paddingLeft,e.paddingLeft),this.paintOrder=parse(A,paintOrder,e.paintOrder),this.position=parse(A,position,e.position),this.textAlign=parse(A,textAlign,e.textAlign),this.textDecorationColor=parse(A,textDecorationColor,null!==(t=e.textDecorationColor)&&void 0!==t?t:e.color),this.textDecorationLine=parse(A,textDecorationLine,null!==(t=e.textDecorationLine)&&void 0!==t?t:e.textDecoration),this.textShadow=parse(A,textShadow,e.textShadow),this.textTransform=parse(A,textTransform,e.textTransform),this.transform=parse(A,transform$1,e.transform),this.transformOrigin=parse(A,transformOrigin,e.transformOrigin),this.visibility=parse(A,visibility,e.visibility),this.webkitTextStrokeColor=parse(A,webkitTextStrokeColor,e.webkitTextStrokeColor),this.webkitTextStrokeWidth=parse(A,webkitTextStrokeWidth,e.webkitTextStrokeWidth),this.wordBreak=parse(A,wordBreak,e.wordBreak),this.zIndex=parse(A,zIndex,e.zIndex)}return A.prototype.isVisible=function(){return 0<this.display&&0<this.opacity&&0===this.visibility},A.prototype.isTransparent=function(){return isTransparent(this.backgroundColor)},A.prototype.isTransformed=function(){return null!==this.transform},A.prototype.isPositioned=function(){return 0!==this.position},A.prototype.isPositionedWithZIndex=function(){return this.isPositioned()&&!this.zIndex.auto},A.prototype.isFloating=function(){return 0!==this.float},A.prototype.isInlineLevel=function(){return contains(this.display,4)||contains(this.display,33554432)||contains(this.display,268435456)||contains(this.display,536870912)||contains(this.display,67108864)||contains(this.display,134217728)},A}(),CSSParsedPseudoDeclaration=function(A,e){this.content=parse(A,content,e.content),this.quotes=parse(A,quotes,e.quotes)},CSSParsedCounterDeclaration=function(A,e){this.counterIncrement=parse(A,counterIncrement,e.counterIncrement),this.counterReset=parse(A,counterReset,e.counterReset)},parse=function(A,e,t){var r=new Tokenizer,t=null!=t?t.toString():e.initialValue;r.write(t);var n=new Parser(r.read());switch(e.type){case 2:var B=n.parseComponentValue();return e.parse(A,isIdentToken(B)?B.value:e.initialValue);case 0:return e.parse(A,n.parseComponentValue());case 1:return e.parse(A,n.parseComponentValues());case 4:return n.parseComponentValue();case 3:switch(e.format){case"angle":return angle.parse(A,n.parseComponentValue());case"color":return color$1.parse(A,n.parseComponentValue());case"image":return image.parse(A,n.parseComponentValue());case"length":var o=n.parseComponentValue();return isLength(o)?o:ZERO_LENGTH;case"length-percentage":o=n.parseComponentValue();return isLengthPercentage(o)?o:ZERO_LENGTH;case"time":return time.parse(A,n.parseComponentValue())}}},elementDebuggerAttribute="data-html2canvas-debug",getElementDebugType=function(A){switch(A.getAttribute(elementDebuggerAttribute)){case"all":return 1;case"clone":return 2;case"parse":return 3;case"render":return 4;default:return 0}},isDebugging=function(A,e){A=getElementDebugType(A);return 1===A||e===A},ElementContainer=function(A,e){this.context=A,this.textNodes=[],this.elements=[],this.flags=0,isDebugging(e,3),this.styles=new CSSParsedDeclaration(A,window.getComputedStyle(e,null)),isHTMLElementNode(e)&&(this.styles.animationDuration.some(function(A){return 0<A})&&(e.style.animationDuration="0s"),null!==this.styles.transform&&(e.style.transform="none")),this.bounds=parseBounds(this.context,e),isDebugging(e,4)&&(this.flags|=16)},base64="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",chars$1="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",lookup$1="undefined"==typeof Uint8Array?[]:new Uint8Array(256),i$1=0;i$1<chars$1.length;i$1++)lookup$1[chars$1.charCodeAt(i$1)]=i$1;for(var decode=function(A){var e,t,r,n,B=.75*A.length,o=A.length,s=0;"="===A[A.length-1]&&(B--,"="===A[A.length-2]&&B--);for(var B=new("undefined"!=typeof ArrayBuffer&&"undefined"!=typeof Uint8Array&&void 0!==Uint8Array.prototype.slice?ArrayBuffer:Array)(B),i=Array.isArray(B)?B:new Uint8Array(B),a=0;a<o;a+=4)e=lookup$1[A.charCodeAt(a)],t=lookup$1[A.charCodeAt(a+1)],r=lookup$1[A.charCodeAt(a+2)],n=lookup$1[A.charCodeAt(a+3)],i[s++]=e<<2|t>>4,i[s++]=(15&t)<<4|r>>2,i[s++]=(3&r)<<6|63&n;return B},polyUint16Array=function(A){for(var e=A.length,t=[],r=0;r<e;r+=2)t.push(A[r+1]<<8|A[r]);return t},polyUint32Array=function(A){for(var e=A.length,t=[],r=0;r<e;r+=4)t.push(A[r+3]<<24|A[r+2]<<16|A[r+1]<<8|A[r]);return t},UTRIE2_SHIFT_2=5,UTRIE2_SHIFT_1=11,UTRIE2_INDEX_SHIFT=2,UTRIE2_SHIFT_1_2=UTRIE2_SHIFT_1-UTRIE2_SHIFT_2,UTRIE2_LSCP_INDEX_2_OFFSET=65536>>UTRIE2_SHIFT_2,UTRIE2_DATA_BLOCK_LENGTH=1<<UTRIE2_SHIFT_2,UTRIE2_DATA_MASK=UTRIE2_DATA_BLOCK_LENGTH-1,UTRIE2_LSCP_INDEX_2_LENGTH=1024>>UTRIE2_SHIFT_2,UTRIE2_INDEX_2_BMP_LENGTH=UTRIE2_LSCP_INDEX_2_OFFSET+UTRIE2_LSCP_INDEX_2_LENGTH,UTRIE2_UTF8_2B_INDEX_2_OFFSET=UTRIE2_INDEX_2_BMP_LENGTH,UTRIE2_UTF8_2B_INDEX_2_LENGTH=32,UTRIE2_INDEX_1_OFFSET=UTRIE2_INDEX_2_BMP_LENGTH+UTRIE2_UTF8_2B_INDEX_2_LENGTH,UTRIE2_OMITTED_BMP_INDEX_1_LENGTH=65536>>UTRIE2_SHIFT_1,UTRIE2_INDEX_2_BLOCK_LENGTH=1<<UTRIE2_SHIFT_1_2,UTRIE2_INDEX_2_MASK=UTRIE2_INDEX_2_BLOCK_LENGTH-1,slice16=function(A,e,t){return A.slice?A.slice(e,t):new Uint16Array(Array.prototype.slice.call(A,e,t))},slice32=function(A,e,t){return A.slice?A.slice(e,t):new Uint32Array(Array.prototype.slice.call(A,e,t))},createTrieFromBase64=function(A,e){var t=decode(A),r=Array.isArray(t)?polyUint32Array(t):new Uint32Array(t),A=Array.isArray(t)?polyUint16Array(t):new Uint16Array(t),t=slice16(A,12,r[4]/2),A=2===r[5]?slice16(A,(24+r[4])/2):slice32(r,Math.ceil((24+r[4])/4));return new Trie(r[0],r[1],r[2],r[3],t,A)},Trie=function(){function A(A,e,t,r,n,B){this.initialValue=A,this.errorValue=e,this.highStart=t,this.highValueIndex=r,this.index=n,this.data=B}return A.prototype.get=function(A){var e;if(0<=A){if(A<55296||56319<A&&A<=65535)return e=this.index[A>>UTRIE2_SHIFT_2],this.data[e=(e<<UTRIE2_INDEX_SHIFT)+(A&UTRIE2_DATA_MASK)];if(A<=65535)return e=this.index[UTRIE2_LSCP_INDEX_2_OFFSET+(A-55296>>UTRIE2_SHIFT_2)],this.data[e=(e<<UTRIE2_INDEX_SHIFT)+(A&UTRIE2_DATA_MASK)];if(A<this.highStart)return e=this.index[e=UTRIE2_INDEX_1_OFFSET-UTRIE2_OMITTED_BMP_INDEX_1_LENGTH+(A>>UTRIE2_SHIFT_1)],e=this.index[e+=A>>UTRIE2_SHIFT_2&UTRIE2_INDEX_2_MASK],this.data[e=(e<<UTRIE2_INDEX_SHIFT)+(A&UTRIE2_DATA_MASK)];if(A<=1114111)return this.data[this.highValueIndex]}return this.errorValue},A}(),chars="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",lookup="undefined"==typeof Uint8Array?[]:new Uint8Array(256),i=0;i<chars.length;i++)lookup[chars.charCodeAt(i)]=i;var PseudoElementType,Prepend=1,CR=2,LF=3,Control=4,Extend=5,SpacingMark=7,L=8,V=9,T=10,LV=11,LVT=12,ZWJ=13,Extended_Pictographic=14,RI=15,toCodePoints=function(A){for(var e=[],t=0,r=A.length;t<r;){var n,B=A.charCodeAt(t++);55296<=B&&B<=56319&&t<r?56320==(64512&(n=A.charCodeAt(t++)))?e.push(((1023&B)<<10)+(1023&n)+65536):(e.push(B),t--):e.push(B)}return e},fromCodePoint=function(){for(var A=[],e=0;e<arguments.length;e++)A[e]=arguments[e];if(String.fromCodePoint)return String.fromCodePoint.apply(String,A);var t=A.length;if(!t)return"";for(var r=[],n=-1,B="";++n<t;){var o=A[n];o<=65535?r.push(o):(o-=65536,r.push(55296+(o>>10),o%1024+56320)),(n+1===t||16384<r.length)&&(B+=String.fromCharCode.apply(String,r),r.length=0)}return B},UnicodeTrie=createTrieFromBase64(base64),BREAK_NOT_ALLOWED="×",BREAK_ALLOWED="÷",codePointToClass=function(A){return UnicodeTrie.get(A)},_graphemeBreakAtIndex=function(A,e,t){var r=t-2,n=e[r],B=e[t-1],t=e[t];if(B===CR&&t===LF)return BREAK_NOT_ALLOWED;if(B===CR||B===LF||B===Control)return BREAK_ALLOWED;if(t===CR||t===LF||t===Control)return BREAK_ALLOWED;if(B===L&&-1!==[L,V,LV,LVT].indexOf(t))return BREAK_NOT_ALLOWED;if(!(B!==LV&&B!==V||t!==V&&t!==T))return BREAK_NOT_ALLOWED;if((B===LVT||B===T)&&t===T)return BREAK_NOT_ALLOWED;if(t===ZWJ||t===Extend)return BREAK_NOT_ALLOWED;if(t===SpacingMark)return BREAK_NOT_ALLOWED;if(B===Prepend)return BREAK_NOT_ALLOWED;if(B===ZWJ&&t===Extended_Pictographic){for(;n===Extend;)n=e[--r];if(n===Extended_Pictographic)return BREAK_NOT_ALLOWED}if(B===RI&&t===RI){for(var o=0;n===RI;)o++,n=e[--r];if(o%2==0)return BREAK_NOT_ALLOWED}return BREAK_ALLOWED},GraphemeBreaker=function(A){var t=toCodePoints(A),r=t.length,n=0,B=0,o=t.map(codePointToClass);return{next:function(){if(r<=n)return{done:!0,value:null};for(var A=BREAK_NOT_ALLOWED;n<r&&(A=_graphemeBreakAtIndex(t,o,++n))===BREAK_NOT_ALLOWED;);if(A===BREAK_NOT_ALLOWED&&n!==r)return{done:!0,value:null};var e=fromCodePoint.apply(null,t.slice(B,n));return B=n,{value:e,done:!1}}}},splitGraphemes=function(A){for(var e,t=GraphemeBreaker(A),r=[];!(e=t.next()).done;)e.value&&r.push(e.value.slice());return r},testRangeBounds=function(A){if(A.createRange){var e=A.createRange();if(e.getBoundingClientRect){var t=A.createElement("boundtest");t.style.height="123px",t.style.display="block",A.body.appendChild(t),e.selectNode(t);e=e.getBoundingClientRect(),e=Math.round(e.height);if(A.body.removeChild(t),123===e)return!0}}return!1},testIOSLineBreak=function(A){var e=A.createElement("boundtest");e.style.width="50px",e.style.display="block",e.style.fontSize="12px",e.style.letterSpacing="0px",e.style.wordSpacing="0px",A.body.appendChild(e);var r=A.createRange();e.innerHTML="function"==typeof"".repeat?"&#128104;".repeat(10):"";var n=e.firstChild,t=toCodePoints$1(n.data).map(function(A){return fromCodePoint$1(A)}),B=0,o={},t=t.every(function(A,e){r.setStart(n,B),r.setEnd(n,B+A.length);var t=r.getBoundingClientRect();B+=A.length;A=t.x>o.x||t.y>o.y;return o=t,0===e||A});return A.body.removeChild(e),t},testCORS=function(){return void 0!==(new Image).crossOrigin},testResponseType=function(){return"string"==typeof(new XMLHttpRequest).responseType},testSVG=function(A){var e=new Image,t=A.createElement("canvas"),A=t.getContext("2d");if(!A)return!1;e.src="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg'></svg>";try{A.drawImage(e,0,0),t.toDataURL()}catch(A){return!1}return!0},isGreenPixel=function(A){return 0===A[0]&&255===A[1]&&0===A[2]&&255===A[3]},testForeignObject=function(t){var A=t.createElement("canvas"),r=100;A.width=r,A.height=r;var n=A.getContext("2d");if(!n)return Promise.reject(!1);n.fillStyle="rgb(0, 255, 0)",n.fillRect(0,0,r,r);var e=new Image,B=A.toDataURL();e.src=B;e=createForeignObjectSVG(r,r,0,0,e);return n.fillStyle="red",n.fillRect(0,0,r,r),loadSerializedSVG$1(e).then(function(A){n.drawImage(A,0,0);var e=n.getImageData(0,0,r,r).data;n.fillStyle="red",n.fillRect(0,0,r,r);A=t.createElement("div");return A.style.backgroundImage="url("+B+")",A.style.height="100px",isGreenPixel(e)?loadSerializedSVG$1(createForeignObjectSVG(r,r,0,0,A)):Promise.reject(!1)}).then(function(A){return n.drawImage(A,0,0),isGreenPixel(n.getImageData(0,0,r,r).data)}).catch(function(){return!1})},createForeignObjectSVG=function(A,e,t,r,n){var B="http://www.w3.org/2000/svg",o=document.createElementNS(B,"svg"),B=document.createElementNS(B,"foreignObject");return o.setAttributeNS(null,"width",A.toString()),o.setAttributeNS(null,"height",e.toString()),B.setAttributeNS(null,"width","100%"),B.setAttributeNS(null,"height","100%"),B.setAttributeNS(null,"x",t.toString()),B.setAttributeNS(null,"y",r.toString()),B.setAttributeNS(null,"externalResourcesRequired","true"),o.appendChild(B),B.appendChild(n),o},loadSerializedSVG$1=function(r){return new Promise(function(A,e){var t=new Image;t.onload=function(){return A(t)},t.onerror=e,t.src="data:image/svg+xml;charset=utf-8,"+encodeURIComponent((new XMLSerializer).serializeToString(r))})},FEATURES={get SUPPORT_RANGE_BOUNDS(){var A=testRangeBounds(document);return Object.defineProperty(FEATURES,"SUPPORT_RANGE_BOUNDS",{value:A}),A},get SUPPORT_WORD_BREAKING(){var A=FEATURES.SUPPORT_RANGE_BOUNDS&&testIOSLineBreak(document);return Object.defineProperty(FEATURES,"SUPPORT_WORD_BREAKING",{value:A}),A},get SUPPORT_SVG_DRAWING(){var A=testSVG(document);return Object.defineProperty(FEATURES,"SUPPORT_SVG_DRAWING",{value:A}),A},get SUPPORT_FOREIGNOBJECT_DRAWING(){var A="function"==typeof Array.from&&"function"==typeof window.fetch?testForeignObject(document):Promise.resolve(!1);return Object.defineProperty(FEATURES,"SUPPORT_FOREIGNOBJECT_DRAWING",{value:A}),A},get SUPPORT_CORS_IMAGES(){var A=testCORS();return Object.defineProperty(FEATURES,"SUPPORT_CORS_IMAGES",{value:A}),A},get SUPPORT_RESPONSE_TYPE(){var A=testResponseType();return Object.defineProperty(FEATURES,"SUPPORT_RESPONSE_TYPE",{value:A}),A},get SUPPORT_CORS_XHR(){var A="withCredentials"in new XMLHttpRequest;return Object.defineProperty(FEATURES,"SUPPORT_CORS_XHR",{value:A}),A},get SUPPORT_NATIVE_TEXT_SEGMENTATION(){var A=!("undefined"==typeof Intl||!Intl.Segmenter);return Object.defineProperty(FEATURES,"SUPPORT_NATIVE_TEXT_SEGMENTATION",{value:A}),A}},TextBounds=function(A,e){this.text=A,this.bounds=e},parseTextBounds=function(n,A,B,o){var A=breakText(A,B),s=[],i=0;return A.forEach(function(A){var e,t,r;B.textDecorationLine.length||0<A.trim().length?FEATURES.SUPPORT_RANGE_BOUNDS?1<(r=createRange(o,i,A.length).getClientRects()).length?(e=segmentGraphemes(A),t=0,e.forEach(function(A){s.push(new TextBounds(A,Bounds.fromDOMRectList(n,createRange(o,t+i,A.length).getClientRects()))),t+=A.length})):s.push(new TextBounds(A,Bounds.fromDOMRectList(n,r))):(r=o.splitText(A.length),s.push(new TextBounds(A,getWrapperBounds(n,o))),o=r):FEATURES.SUPPORT_RANGE_BOUNDS||(o=o.splitText(A.length)),i+=A.length}),s},getWrapperBounds=function(A,e){var t=e.ownerDocument;if(t){var r=t.createElement("html2canvaswrapper");r.appendChild(e.cloneNode(!0));t=e.parentNode;if(t){t.replaceChild(r,e);A=parseBounds(A,r);return r.firstChild&&t.replaceChild(r.firstChild,r),A}}return Bounds.EMPTY},createRange=function(A,e,t){var r=A.ownerDocument;if(!r)throw new Error("Node has no owner document");r=r.createRange();return r.setStart(A,e),r.setEnd(A,e+t),r},segmentGraphemes=function(A){if(FEATURES.SUPPORT_NATIVE_TEXT_SEGMENTATION){var e=new Intl.Segmenter(void 0,{granularity:"grapheme"});return Array.from(e.segment(A)).map(function(A){return A.segment})}return splitGraphemes(A)},segmentWords=function(A,e){if(FEATURES.SUPPORT_NATIVE_TEXT_SEGMENTATION){var t=new Intl.Segmenter(void 0,{granularity:"word"});return Array.from(t.segment(A)).map(function(A){return A.segment})}return breakWords(A,e)},breakText=function(A,e){return 0!==e.letterSpacing?segmentGraphemes(A):segmentWords(A,e)},wordSeparators=[32,160,4961,65792,65793,4153,4241],breakWords=function(A,e){for(var t,r=LineBreaker(A,{lineBreak:e.lineBreak,wordBreak:"break-word"===e.overflowWrap?"break-word":e.wordBreak}),n=[];!(t=r.next()).done;)!function(){var A,e;t.value&&(A=t.value.slice(),A=toCodePoints$1(A),e="",A.forEach(function(A){-1===wordSeparators.indexOf(A)?e+=fromCodePoint$1(A):(e.length&&n.push(e),n.push(fromCodePoint$1(A)),e="")}),e.length&&n.push(e))}();return n},TextContainer=function(A,e,t){this.text=transform(e.data,t.textTransform),this.textBounds=parseTextBounds(A,this.text,t,e)},transform=function(A,e){switch(e){case 1:return A.toLowerCase();case 3:return A.replace(CAPITALIZE,capitalize);case 2:return A.toUpperCase();default:return A}},CAPITALIZE=/(^|\s|:|-|\(|\))([a-z])/g,capitalize=function(A,e,t){return 0<A.length?e+t.toUpperCase():A},ImageElementContainer=function(t){function A(A,e){A=t.call(this,A,e)||this;return A.src=e.currentSrc||e.src,A.intrinsicWidth=e.naturalWidth,A.intrinsicHeight=e.naturalHeight,A.context.cache.addImage(A.src),A}return __extends(A,t),A}(ElementContainer),CanvasElementContainer=function(t){function A(A,e){A=t.call(this,A,e)||this;return A.canvas=e,A.intrinsicWidth=e.width,A.intrinsicHeight=e.height,A}return __extends(A,t),A}(ElementContainer),SVGElementContainer=function(n){function A(A,e){var t=n.call(this,A,e)||this,r=new XMLSerializer,A=parseBounds(A,e);return e.setAttribute("width",A.width+"px"),e.setAttribute("height",A.height+"px"),t.svg="data:image/svg+xml,"+encodeURIComponent(r.serializeToString(e)),t.intrinsicWidth=e.width.baseVal.value,t.intrinsicHeight=e.height.baseVal.value,t.context.cache.addImage(t.svg),t}return __extends(A,n),A}(ElementContainer),LIElementContainer=function(t){function A(A,e){A=t.call(this,A,e)||this;return A.value=e.value,A}return __extends(A,t),A}(ElementContainer),OLElementContainer=function(t){function A(A,e){A=t.call(this,A,e)||this;return A.start=e.start,A.reversed="boolean"==typeof e.reversed&&!0===e.reversed,A}return __extends(A,t),A}(ElementContainer),CHECKBOX_BORDER_RADIUS=[{type:15,flags:0,unit:"px",number:3}],RADIO_BORDER_RADIUS=[{type:16,flags:0,number:50}],reformatInputBounds=function(A){return A.width>A.height?new Bounds(A.left+(A.width-A.height)/2,A.top,A.height,A.height):A.width<A.height?new Bounds(A.left,A.top+(A.height-A.width)/2,A.width,A.width):A},getInputValue=function(A){var e=A.type===PASSWORD?new Array(A.value.length+1).join("•"):A.value;return 0===e.length?A.placeholder||"":e},CHECKBOX="checkbox",RADIO="radio",PASSWORD="password",INPUT_COLOR=707406591,InputElementContainer=function(r){function A(A,e){var t=r.call(this,A,e)||this;switch(t.type=e.type.toLowerCase(),t.checked=e.checked,t.value=getInputValue(e),t.type!==CHECKBOX&&t.type!==RADIO||(t.styles.backgroundColor=3739148031,t.styles.borderTopColor=t.styles.borderRightColor=t.styles.borderBottomColor=t.styles.borderLeftColor=2779096575,t.styles.borderTopWidth=t.styles.borderRightWidth=t.styles.borderBottomWidth=t.styles.borderLeftWidth=1,t.styles.borderTopStyle=t.styles.borderRightStyle=t.styles.borderBottomStyle=t.styles.borderLeftStyle=1,t.styles.backgroundClip=[0],t.styles.backgroundOrigin=[0],t.bounds=reformatInputBounds(t.bounds)),t.type){case CHECKBOX:t.styles.borderTopRightRadius=t.styles.borderTopLeftRadius=t.styles.borderBottomRightRadius=t.styles.borderBottomLeftRadius=CHECKBOX_BORDER_RADIUS;break;case RADIO:t.styles.borderTopRightRadius=t.styles.borderTopLeftRadius=t.styles.borderBottomRightRadius=t.styles.borderBottomLeftRadius=RADIO_BORDER_RADIUS}return t}return __extends(A,r),A}(ElementContainer),SelectElementContainer=function(t){function A(A,e){A=t.call(this,A,e)||this,e=e.options[e.selectedIndex||0];return A.value=e&&e.text||"",A}return __extends(A,t),A}(ElementContainer),TextareaElementContainer=function(t){function A(A,e){A=t.call(this,A,e)||this;return A.value=e.value,A}return __extends(A,t),A}(ElementContainer),IFrameElementContainer=function(B){function A(A,e){var t,r,n=B.call(this,A,e)||this;n.src=e.src,n.width=parseInt(e.width,10)||0,n.height=parseInt(e.height,10)||0,n.backgroundColor=n.styles.backgroundColor;try{e.contentWindow&&e.contentWindow.document&&e.contentWindow.document.documentElement&&(n.tree=parseTree(A,e.contentWindow.document.documentElement),t=e.contentWindow.document.documentElement?parseColor(A,getComputedStyle(e.contentWindow.document.documentElement).backgroundColor):COLORS.TRANSPARENT,r=e.contentWindow.document.body?parseColor(A,getComputedStyle(e.contentWindow.document.body).backgroundColor):COLORS.TRANSPARENT,n.backgroundColor=isTransparent(t)?isTransparent(r)?n.styles.backgroundColor:r:t)}catch(A){}return n}return __extends(A,B),A}(ElementContainer),LIST_OWNERS=["OL","UL","MENU"],parseNodeTree=function(e,A,t,r){for(var n=A.firstChild;n;n=o){var B,o=n.nextSibling;isTextNode(n)&&0<n.data.trim().length?t.textNodes.push(new TextContainer(e,n,t.styles)):isElementNode(n)&&(isSlotElement(n)&&n.assignedNodes?n.assignedNodes().forEach(function(A){return parseNodeTree(e,A,t,r)}):(B=createContainer(e,n)).styles.isVisible()&&(createsRealStackingContext(n,B,r)?B.flags|=4:createsStackingContext(B.styles)&&(B.flags|=2),-1!==LIST_OWNERS.indexOf(n.tagName)&&(B.flags|=8),t.elements.push(B),n.slot,n.shadowRoot?parseNodeTree(e,n.shadowRoot,B,r):isTextareaElement(n)||isSVGElement(n)||isSelectElement(n)||parseNodeTree(e,n,B,r)))}},createContainer=function(A,e){return new(isImageElement(e)?ImageElementContainer:isCanvasElement(e)?CanvasElementContainer:isSVGElement(e)?SVGElementContainer:isLIElement(e)?LIElementContainer:isOLElement(e)?OLElementContainer:isInputElement(e)?InputElementContainer:isSelectElement(e)?SelectElementContainer:isTextareaElement(e)?TextareaElementContainer:isIFrameElement(e)?IFrameElementContainer:ElementContainer)(A,e)},parseTree=function(A,e){var t=createContainer(A,e);return t.flags|=4,parseNodeTree(A,e,t,t),t},createsRealStackingContext=function(A,e,t){return e.styles.isPositionedWithZIndex()||e.styles.opacity<1||e.styles.isTransformed()||isBodyElement(A)&&t.styles.isTransparent()},createsStackingContext=function(A){return A.isPositioned()||A.isFloating()},isTextNode=function(A){return A.nodeType===Node.TEXT_NODE},isElementNode=function(A){return A.nodeType===Node.ELEMENT_NODE},isHTMLElementNode=function(A){return isElementNode(A)&&void 0!==A.style&&!isSVGElementNode(A)},isSVGElementNode=function(A){return"object"==typeof A.className},isLIElement=function(A){return"LI"===A.tagName},isOLElement=function(A){return"OL"===A.tagName},isInputElement=function(A){return"INPUT"===A.tagName},isHTMLElement=function(A){return"HTML"===A.tagName},isSVGElement=function(A){return"svg"===A.tagName},isBodyElement=function(A){return"BODY"===A.tagName},isCanvasElement=function(A){return"CANVAS"===A.tagName},isVideoElement=function(A){return"VIDEO"===A.tagName},isImageElement=function(A){return"IMG"===A.tagName},isIFrameElement=function(A){return"IFRAME"===A.tagName},isStyleElement=function(A){return"STYLE"===A.tagName},isScriptElement=function(A){return"SCRIPT"===A.tagName},isTextareaElement=function(A){return"TEXTAREA"===A.tagName},isSelectElement=function(A){return"SELECT"===A.tagName},isSlotElement=function(A){return"SLOT"===A.tagName},isCustomElement=function(A){return 0<A.tagName.indexOf("-")},CounterState=function(){function A(){this.counters={}}return A.prototype.getCounterValue=function(A){A=this.counters[A];return A&&A.length?A[A.length-1]:1},A.prototype.getCounterValues=function(A){A=this.counters[A];return A||[]},A.prototype.pop=function(A){var e=this;A.forEach(function(A){return e.counters[A].pop()})},A.prototype.parse=function(A){var t=this,e=A.counterIncrement,A=A.counterReset,r=!0;null!==e&&e.forEach(function(A){var e=t.counters[A.counter];e&&0!==A.increment&&(r=!1,e.length||e.push(1),e[Math.max(0,e.length-1)]+=A.increment)});var n=[];return r&&A.forEach(function(A){var e=t.counters[A.counter];n.push(A.counter),(e=e||(t.counters[A.counter]=[])).push(A.reset)}),n},A}(),ROMAN_UPPER={integers:[1e3,900,500,400,100,90,50,40,10,9,5,4,1],values:["M","CM","D","CD","C","XC","L","XL","X","IX","V","IV","I"]},ARMENIAN={integers:[9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,900,800,700,600,500,400,300,200,100,90,80,70,60,50,40,30,20,10,9,8,7,6,5,4,3,2,1],values:["Ք","Փ","Ւ","Ց","Ր","Տ","Վ","Ս","Ռ","Ջ","Պ","Չ","Ո","Շ","Ն","Յ","Մ","Ճ","Ղ","Ձ","Հ","Կ","Ծ","Խ","Լ","Ի","Ժ","Թ","Ը","Է","Զ","Ե","Դ","Գ","Բ","Ա"]},HEBREW={integers:[1e4,9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,400,300,200,100,90,80,70,60,50,40,30,20,19,18,17,16,15,10,9,8,7,6,5,4,3,2,1],values:["י׳","ט׳","ח׳","ז׳","ו׳","ה׳","ד׳","ג׳","ב׳","א׳","ת","ש","ר","ק","צ","פ","ע","ס","נ","מ","ל","כ","יט","יח","יז","טז","טו","י","ט","ח","ז","ו","ה","ד","ג","ב","א"]},GEORGIAN={integers:[1e4,9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,900,800,700,600,500,400,300,200,100,90,80,70,60,50,40,30,20,10,9,8,7,6,5,4,3,2,1],values:["ჵ","ჰ","ჯ","ჴ","ხ","ჭ","წ","ძ","ც","ჩ","შ","ყ","ღ","ქ","ფ","ჳ","ტ","ს","რ","ჟ","პ","ო","ჲ","ნ","მ","ლ","კ","ი","თ","ჱ","ზ","ვ","ე","დ","გ","ბ","ა"]},createAdditiveCounter=function(r,A,e,n,t,B){return r<A||e<r?createCounterText(r,t,0<B.length):n.integers.reduce(function(A,e,t){for(;e<=r;)r-=e,A+=n.values[t];return A},"")+B},createCounterStyleWithSymbolResolver=function(A,e,t,r){for(var n="";t||A--,n=r(A)+n,e<=(A/=e)*e;);return n},createCounterStyleFromRange=function(A,e,t,r,n){var B=t-e+1;return(A<0?"-":"")+(createCounterStyleWithSymbolResolver(Math.abs(A),B,r,function(A){return fromCodePoint$1(Math.floor(A%B)+e)})+n)},createCounterStyleFromSymbols=function(A,e,t){void 0===t&&(t=". ");var r=e.length;return createCounterStyleWithSymbolResolver(Math.abs(A),r,!1,function(A){return e[Math.floor(A%r)]})+t},CJK_ZEROS=1,CJK_TEN_COEFFICIENTS=2,CJK_TEN_HIGH_COEFFICIENTS=4,CJK_HUNDRED_COEFFICIENTS=8,createCJKCounter=function(A,e,t,r,n,B){if(A<-9999||9999<A)return createCounterText(A,4,0<n.length);var o=Math.abs(A),s=n;if(0===o)return e[0]+s;for(var i=0;0<o&&i<=4;i++){var a=o%10;0==a&&contains(B,CJK_ZEROS)&&""!==s?s=e[a]+s:1<a||1==a&&0===i||1==a&&1===i&&contains(B,CJK_TEN_COEFFICIENTS)||1==a&&1===i&&contains(B,CJK_TEN_HIGH_COEFFICIENTS)&&100<A||1==a&&1<i&&contains(B,CJK_HUNDRED_COEFFICIENTS)?s=e[a]+(0<i?t[i-1]:"")+s:1==a&&0<i&&(s=t[i-1]+s),o=Math.floor(o/10)}return(A<0?r:"")+s},CHINESE_INFORMAL_MULTIPLIERS="十百千萬",CHINESE_FORMAL_MULTIPLIERS="拾佰仟萬",JAPANESE_NEGATIVE="マイナス",KOREAN_NEGATIVE="마이너스",createCounterText=function(A,e,t){var r=t?". ":"",n=t?"、":"",B=t?", ":"",o=t?" ":"";switch(e){case 0:return"•"+o;case 1:return"◦"+o;case 2:return"◾"+o;case 5:var s=createCounterStyleFromRange(A,48,57,!0,r);return s.length<4?"0"+s:s;case 4:return createCounterStyleFromSymbols(A,"〇一二三四五六七八九",n);case 6:return createAdditiveCounter(A,1,3999,ROMAN_UPPER,3,r).toLowerCase();case 7:return createAdditiveCounter(A,1,3999,ROMAN_UPPER,3,r);case 8:return createCounterStyleFromRange(A,945,969,!1,r);case 9:return createCounterStyleFromRange(A,97,122,!1,r);case 10:return createCounterStyleFromRange(A,65,90,!1,r);case 11:return createCounterStyleFromRange(A,1632,1641,!0,r);case 12:case 49:return createAdditiveCounter(A,1,9999,ARMENIAN,3,r);case 35:return createAdditiveCounter(A,1,9999,ARMENIAN,3,r).toLowerCase();case 13:return createCounterStyleFromRange(A,2534,2543,!0,r);case 14:case 30:return createCounterStyleFromRange(A,6112,6121,!0,r);case 15:return createCounterStyleFromSymbols(A,"子丑寅卯辰巳午未申酉戌亥",n);case 16:return createCounterStyleFromSymbols(A,"甲乙丙丁戊己庚辛壬癸",n);case 17:case 48:return createCJKCounter(A,"零一二三四五六七八九",CHINESE_INFORMAL_MULTIPLIERS,"負",n,CJK_TEN_COEFFICIENTS|CJK_TEN_HIGH_COEFFICIENTS|CJK_HUNDRED_COEFFICIENTS);case 47:return createCJKCounter(A,"零壹貳參肆伍陸柒捌玖",CHINESE_FORMAL_MULTIPLIERS,"負",n,CJK_ZEROS|CJK_TEN_COEFFICIENTS|CJK_TEN_HIGH_COEFFICIENTS|CJK_HUNDRED_COEFFICIENTS);case 42:return createCJKCounter(A,"零一二三四五六七八九",CHINESE_INFORMAL_MULTIPLIERS,"负",n,CJK_TEN_COEFFICIENTS|CJK_TEN_HIGH_COEFFICIENTS|CJK_HUNDRED_COEFFICIENTS);case 41:return createCJKCounter(A,"零壹贰叁肆伍陆柒捌玖",CHINESE_FORMAL_MULTIPLIERS,"负",n,CJK_ZEROS|CJK_TEN_COEFFICIENTS|CJK_TEN_HIGH_COEFFICIENTS|CJK_HUNDRED_COEFFICIENTS);case 26:return createCJKCounter(A,"〇一二三四五六七八九","十百千万",JAPANESE_NEGATIVE,n,0);case 25:return createCJKCounter(A,"零壱弐参四伍六七八九","拾百千万",JAPANESE_NEGATIVE,n,CJK_ZEROS|CJK_TEN_COEFFICIENTS|CJK_TEN_HIGH_COEFFICIENTS);case 31:return createCJKCounter(A,"영일이삼사오육칠팔구","십백천만",KOREAN_NEGATIVE,B,CJK_ZEROS|CJK_TEN_COEFFICIENTS|CJK_TEN_HIGH_COEFFICIENTS);case 33:return createCJKCounter(A,"零一二三四五六七八九","十百千萬",KOREAN_NEGATIVE,B,0);case 32:return createCJKCounter(A,"零壹貳參四五六七八九","拾百千",KOREAN_NEGATIVE,B,CJK_ZEROS|CJK_TEN_COEFFICIENTS|CJK_TEN_HIGH_COEFFICIENTS);case 18:return createCounterStyleFromRange(A,2406,2415,!0,r);case 20:return createAdditiveCounter(A,1,19999,GEORGIAN,3,r);case 21:return createCounterStyleFromRange(A,2790,2799,!0,r);case 22:return createCounterStyleFromRange(A,2662,2671,!0,r);case 22:return createAdditiveCounter(A,1,10999,HEBREW,3,r);case 23:return createCounterStyleFromSymbols(A,"あいうえおかきくけこさしすせそたちつてとなにぬねのはひふへほまみむめもやゆよらりるれろわゐゑをん");case 24:return createCounterStyleFromSymbols(A,"いろはにほへとちりぬるをわかよたれそつねならむうゐのおくやまけふこえてあさきゆめみしゑひもせす");case 27:return createCounterStyleFromRange(A,3302,3311,!0,r);case 28:return createCounterStyleFromSymbols(A,"アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヰヱヲン",n);case 29:return createCounterStyleFromSymbols(A,"イロハニホヘトチリヌルヲワカヨタレソツネナラムウヰノオクヤマケフコエテアサキユメミシヱヒモセス",n);case 34:return createCounterStyleFromRange(A,3792,3801,!0,r);case 37:return createCounterStyleFromRange(A,6160,6169,!0,r);case 38:return createCounterStyleFromRange(A,4160,4169,!0,r);case 39:return createCounterStyleFromRange(A,2918,2927,!0,r);case 40:return createCounterStyleFromRange(A,1776,1785,!0,r);case 43:return createCounterStyleFromRange(A,3046,3055,!0,r);case 44:return createCounterStyleFromRange(A,3174,3183,!0,r);case 45:return createCounterStyleFromRange(A,3664,3673,!0,r);case 46:return createCounterStyleFromRange(A,3872,3881,!0,r);default:return createCounterStyleFromRange(A,48,57,!0,r)}},IGNORE_ATTRIBUTE="data-html2canvas-ignore",DocumentCloner=function(){function A(A,e,t){if(this.context=A,this.options=t,this.scrolledElements=[],this.referenceElement=e,this.counters=new CounterState,this.quoteDepth=0,!e.ownerDocument)throw new Error("Cloned element does not have an owner document");this.documentElement=this.cloneNode(e.ownerDocument.documentElement,!1)}return A.prototype.toIFrame=function(A,r){var e=this,n=createIFrameContainer(A,r);if(!n.contentWindow)return Promise.reject("Unable to find iframe window");var t=A.defaultView.pageXOffset,B=A.defaultView.pageYOffset,o=n.contentWindow,s=o.document,A=iframeLoader(n).then(function(){return __awaiter(e,void 0,void 0,function(){var e,t;return __generator(this,function(A){switch(A.label){case 0:return this.scrolledElements.forEach(restoreNodeScroll),o&&(o.scrollTo(r.left,r.top),!/(iPad|iPhone|iPod)/g.test(navigator.userAgent)||o.scrollY===r.top&&o.scrollX===r.left||(this.context.logger.warn("Unable to restore scroll position for cloned document"),this.context.windowBounds=this.context.windowBounds.add(o.scrollX-r.left,o.scrollY-r.top,0,0))),e=this.options.onclone,void 0===(t=this.clonedReferenceElement)?[2,Promise.reject("Error finding the "+this.referenceElement.nodeName+" in the cloned document")]:s.fonts&&s.fonts.ready?[4,s.fonts.ready]:[3,2];case 1:A.sent(),A.label=2;case 2:return/(AppleWebKit)/g.test(navigator.userAgent)?[4,imagesReady(s)]:[3,4];case 3:A.sent(),A.label=4;case 4:return"function"==typeof e?[2,Promise.resolve().then(function(){return e(s,t)}).then(function(){return n})]:[2,n]}})})});return s.open(),s.write(serializeDoctype(document.doctype)+"<html></html>"),restoreOwnerScroll(this.referenceElement.ownerDocument,t,B),s.replaceChild(s.adoptNode(this.documentElement),s.documentElement),s.close(),A},A.prototype.createElementClone=function(A){if(isDebugging(A,2),isCanvasElement(A))return this.createCanvasClone(A);if(isVideoElement(A))return this.createVideoClone(A);if(isStyleElement(A))return this.createStyleClone(A);var e=A.cloneNode(!1);return isImageElement(e)&&(isImageElement(A)&&A.currentSrc&&A.currentSrc!==A.src&&(e.src=A.currentSrc,e.srcset=""),"lazy"===e.loading&&(e.loading="eager")),isCustomElement(e)?this.createCustomElementClone(e):e},A.prototype.createCustomElementClone=function(A){var e=document.createElement("html2canvascustomelement");return copyCSSStyles(A.style,e),e},A.prototype.createStyleClone=function(A){try{var e=A.sheet;if(e&&e.cssRules){var t=[].slice.call(e.cssRules,0).reduce(function(A,e){return e&&"string"==typeof e.cssText?A+e.cssText:A},""),r=A.cloneNode(!1);return r.textContent=t,r}}catch(A){if(this.context.logger.error("Unable to access cssRules property",A),"SecurityError"!==A.name)throw A}return A.cloneNode(!1)},A.prototype.createCanvasClone=function(e){var A;if(this.options.inlineImages&&e.ownerDocument){var t=e.ownerDocument.createElement("img");try{return t.src=e.toDataURL(),t}catch(A){this.context.logger.info("Unable to inline canvas contents, canvas is tainted",e)}}t=e.cloneNode(!1);try{t.width=e.width,t.height=e.height;var r,n,B=e.getContext("2d"),o=t.getContext("2d");return o&&(!this.options.allowTaint&&B?o.putImageData(B.getImageData(0,0,e.width,e.height),0,0):(!(r=null!==(A=e.getContext("webgl2"))&&void 0!==A?A:e.getContext("webgl"))||!1===(null==(n=r.getContextAttributes())?void 0:n.preserveDrawingBuffer)&&this.context.logger.warn("Unable to clone WebGL context as it has preserveDrawingBuffer=false",e),o.drawImage(e,0,0))),t}catch(A){this.context.logger.info("Unable to clone canvas as it is tainted",e)}return t},A.prototype.createVideoClone=function(e){var A=e.ownerDocument.createElement("canvas");A.width=e.offsetWidth,A.height=e.offsetHeight;var t=A.getContext("2d");try{return t&&(t.drawImage(e,0,0,A.width,A.height),this.options.allowTaint||t.getImageData(0,0,A.width,A.height)),A}catch(A){this.context.logger.info("Unable to clone video as it is tainted",e)}A=e.ownerDocument.createElement("canvas");return A.width=e.offsetWidth,A.height=e.offsetHeight,A},A.prototype.appendChildNode=function(A,e,t){isElementNode(e)&&(isScriptElement(e)||e.hasAttribute(IGNORE_ATTRIBUTE)||"function"==typeof this.options.ignoreElements&&this.options.ignoreElements(e))||this.options.copyStyles&&isElementNode(e)&&isStyleElement(e)||A.appendChild(this.cloneNode(e,t))},A.prototype.cloneChildNodes=function(A,e,t){for(var r,n=this,B=(A.shadowRoot||A).firstChild;B;B=B.nextSibling)isElementNode(B)&&isSlotElement(B)&&"function"==typeof B.assignedNodes?(r=B.assignedNodes()).length&&r.forEach(function(A){return n.appendChildNode(e,A,t)}):this.appendChildNode(e,B,t)},A.prototype.cloneNode=function(A,e){if(isTextNode(A))return document.createTextNode(A.data);if(!A.ownerDocument)return A.cloneNode(!1);var t=A.ownerDocument.defaultView;if(t&&isElementNode(A)&&(isHTMLElementNode(A)||isSVGElementNode(A))){var r=this.createElementClone(A);r.style.transitionProperty="none";var n=t.getComputedStyle(A),B=t.getComputedStyle(A,":before"),o=t.getComputedStyle(A,":after");this.referenceElement===A&&isHTMLElementNode(r)&&(this.clonedReferenceElement=r),isBodyElement(r)&&createPseudoHideStyles(r);t=this.counters.parse(new CSSParsedCounterDeclaration(this.context,n)),B=this.resolvePseudoContent(A,r,B,PseudoElementType.BEFORE);isCustomElement(A)&&(e=!0),isVideoElement(A)||this.cloneChildNodes(A,r,e),B&&r.insertBefore(B,r.firstChild);o=this.resolvePseudoContent(A,r,o,PseudoElementType.AFTER);return o&&r.appendChild(o),this.counters.pop(t),(n&&(this.options.copyStyles||isSVGElementNode(A))&&!isIFrameElement(A)||e)&&copyCSSStyles(n,r),0===A.scrollTop&&0===A.scrollLeft||this.scrolledElements.push([r,A.scrollLeft,A.scrollTop]),(isTextareaElement(A)||isSelectElement(A))&&(isTextareaElement(r)||isSelectElement(r))&&(r.value=A.value),r}return A.cloneNode(!1)},A.prototype.resolvePseudoContent=function(s,A,e,t){var i=this;if(e){var r=e.content,a=A.ownerDocument;if(a&&r&&"none"!==r&&"-moz-alt-content"!==r&&"none"!==e.display){this.counters.parse(new CSSParsedCounterDeclaration(this.context,e));var c=new CSSParsedPseudoDeclaration(this.context,e),g=a.createElement("html2canvaspseudoelement");copyCSSStyles(e,g),c.content.forEach(function(A){if(0===A.type)g.appendChild(a.createTextNode(A.value));else if(22===A.type){var e=a.createElement("img");e.src=A.value,e.style.opacity="1",g.appendChild(e)}else if(18===A.type){var t,r,n,B,o;"attr"===A.name?(e=A.values.filter(isIdentToken)).length&&g.appendChild(a.createTextNode(s.getAttribute(e[0].value)||"")):"counter"===A.name?(n=(r=A.values.filter(nonFunctionArgSeparator))[0],r=r[1],n&&isIdentToken(n)&&(t=i.counters.getCounterValue(n.value),o=r&&isIdentToken(r)?listStyleType.parse(i.context,r.value):3,g.appendChild(a.createTextNode(createCounterText(t,o,!1))))):"counters"===A.name&&(n=(t=A.values.filter(nonFunctionArgSeparator))[0],o=t[1],r=t[2],n&&isIdentToken(n)&&(n=i.counters.getCounterValues(n.value),B=r&&isIdentToken(r)?listStyleType.parse(i.context,r.value):3,o=o&&0===o.type?o.value:"",o=n.map(function(A){return createCounterText(A,B,!1)}).join(o),g.appendChild(a.createTextNode(o))))}else if(20===A.type)switch(A.value){case"open-quote":g.appendChild(a.createTextNode(getQuote(c.quotes,i.quoteDepth++,!0)));break;case"close-quote":g.appendChild(a.createTextNode(getQuote(c.quotes,--i.quoteDepth,!1)));break;default:g.appendChild(a.createTextNode(A.value))}}),g.className=PSEUDO_HIDE_ELEMENT_CLASS_BEFORE+" "+PSEUDO_HIDE_ELEMENT_CLASS_AFTER;t=t===PseudoElementType.BEFORE?" "+PSEUDO_HIDE_ELEMENT_CLASS_BEFORE:" "+PSEUDO_HIDE_ELEMENT_CLASS_AFTER;return isSVGElementNode(A)?A.className.baseValue+=t:A.className+=t,g}}},A.destroy=function(A){return!!A.parentNode&&(A.parentNode.removeChild(A),!0)},A}();!function(A){A[A.BEFORE=0]="BEFORE",A[A.AFTER=1]="AFTER"}(PseudoElementType=PseudoElementType||{});var CORNER,createIFrameContainer=function(A,e){var t=A.createElement("iframe");return t.className="html2canvas-container",t.style.visibility="hidden",t.style.position="fixed",t.style.left="-10000px",t.style.top="0px",t.style.border="0",t.width=e.width.toString(),t.height=e.height.toString(),t.scrolling="no",t.setAttribute(IGNORE_ATTRIBUTE,"true"),A.body.appendChild(t),t},imageReady=function(e){return new Promise(function(A){!e.complete&&e.src?(e.onload=A,e.onerror=A):A()})},imagesReady=function(A){return Promise.all([].slice.call(A.images,0).map(imageReady))},iframeLoader=function(n){return new Promise(function(e,A){var t=n.contentWindow;if(!t)return A("No window assigned for iframe");var r=t.document;t.onload=n.onload=function(){t.onload=n.onload=null;var A=setInterval(function(){0<r.body.childNodes.length&&"complete"===r.readyState&&(clearInterval(A),e(n))},50)}})},ignoredStyleProperties=["all","d","content"],copyCSSStyles=function(A,e){for(var t=A.length-1;0<=t;t--){var r=A.item(t);-1===ignoredStyleProperties.indexOf(r)&&e.style.setProperty(r,A.getPropertyValue(r))}return e},serializeDoctype=function(A){var e="";return A&&(e+="<!DOCTYPE ",A.name&&(e+=A.name),A.internalSubset&&(e+=A.internalSubset),A.publicId&&(e+='"'+A.publicId+'"'),A.systemId&&(e+='"'+A.systemId+'"'),e+=">"),e},restoreOwnerScroll=function(A,e,t){A&&A.defaultView&&(e!==A.defaultView.pageXOffset||t!==A.defaultView.pageYOffset)&&A.defaultView.scrollTo(e,t)},restoreNodeScroll=function(A){var e=A[0],t=A[1],A=A[2];e.scrollLeft=t,e.scrollTop=A},PSEUDO_BEFORE=":before",PSEUDO_AFTER=":after",PSEUDO_HIDE_ELEMENT_CLASS_BEFORE="___html2canvas___pseudoelement_before",PSEUDO_HIDE_ELEMENT_CLASS_AFTER="___html2canvas___pseudoelement_after",PSEUDO_HIDE_ELEMENT_STYLE='{\n    content: "" !important;\n    display: none !important;\n}',createPseudoHideStyles=function(A){createStyles(A,"."+PSEUDO_HIDE_ELEMENT_CLASS_BEFORE+PSEUDO_BEFORE+PSEUDO_HIDE_ELEMENT_STYLE+"\n         ."+PSEUDO_HIDE_ELEMENT_CLASS_AFTER+PSEUDO_AFTER+PSEUDO_HIDE_ELEMENT_STYLE)},createStyles=function(A,e){var t=A.ownerDocument;t&&((t=t.createElement("style")).textContent=e,A.appendChild(t))},CacheStorage=function(){function t(){}return t.getOrigin=function(A){var e=t._link;return e?(e.href=A,e.href=e.href,e.protocol+e.hostname+e.port):"about:blank"},t.isSameOrigin=function(A){return t.getOrigin(A)===t._origin},t.setContext=function(A){t._link=A.document.createElement("a"),t._origin=t.getOrigin(A.location.href)},t._origin="about:blank",t}(),Cache=function(){function A(A,e){this.context=A,this._options=e,this._cache={}}return A.prototype.addImage=function(A){var e=Promise.resolve();return this.has(A)||(isBlobImage(A)||isRenderable(A))&&(this._cache[A]=this.loadImage(A)).catch(function(){}),e},A.prototype.match=function(A){return this._cache[A]},A.prototype.loadImage=function(o){return __awaiter(this,void 0,void 0,function(){var e,r,t,n,B=this;return __generator(this,function(A){switch(A.label){case 0:return(e=CacheStorage.isSameOrigin(o),r=!isInlineImage(o)&&!0===this._options.useCORS&&FEATURES.SUPPORT_CORS_IMAGES&&!e,t=!isInlineImage(o)&&!e&&!isBlobImage(o)&&"string"==typeof this._options.proxy&&FEATURES.SUPPORT_CORS_XHR&&!r,e||!1!==this._options.allowTaint||isInlineImage(o)||isBlobImage(o)||t||r)?(n=o,t?[4,this.proxy(n)]:[3,2]):[2];case 1:n=A.sent(),A.label=2;case 2:return this.context.logger.debug("Added image "+o.substring(0,256)),[4,new Promise(function(A,e){var t=new Image;t.onload=function(){return A(t)},t.onerror=e,(isInlineBase64Image(n)||r)&&(t.crossOrigin="anonymous"),t.src=n,!0===t.complete&&setTimeout(function(){return A(t)},500),0<B._options.imageTimeout&&setTimeout(function(){return e("Timed out ("+B._options.imageTimeout+"ms) loading image")},B._options.imageTimeout)})];case 3:return[2,A.sent()]}})})},A.prototype.has=function(A){return void 0!==this._cache[A]},A.prototype.keys=function(){return Promise.resolve(Object.keys(this._cache))},A.prototype.proxy=function(o){var s=this,i=this._options.proxy;if(!i)throw new Error("No proxy defined");var a=o.substring(0,256);return new Promise(function(e,t){var r=FEATURES.SUPPORT_RESPONSE_TYPE?"blob":"text",n=new XMLHttpRequest;n.onload=function(){var A;200===n.status?"text"==r?e(n.response):((A=new FileReader).addEventListener("load",function(){return e(A.result)},!1),A.addEventListener("error",function(A){return t(A)},!1),A.readAsDataURL(n.response)):t("Failed to proxy resource "+a+" with status code "+n.status)},n.onerror=t;var A,B=-1<i.indexOf("?")?"&":"?";n.open("GET",i+B+"url="+encodeURIComponent(o)+"&responseType="+r),"text"!=r&&n instanceof XMLHttpRequest&&(n.responseType=r),s._options.imageTimeout&&(A=s._options.imageTimeout,n.timeout=A,n.ontimeout=function(){return t("Timed out ("+A+"ms) proxying "+a)}),n.send()})},A}(),INLINE_SVG=/^data:image\/svg\+xml/i,INLINE_BASE64=/^data:image\/.*;base64,/i,INLINE_IMG=/^data:image\/.*/i,isRenderable=function(A){return FEATURES.SUPPORT_SVG_DRAWING||!isSVG(A)},isInlineImage=function(A){return INLINE_IMG.test(A)},isInlineBase64Image=function(A){return INLINE_BASE64.test(A)},isBlobImage=function(A){return"blob"===A.substr(0,4)},isSVG=function(A){return"svg"===A.substr(-3).toLowerCase()||INLINE_SVG.test(A)},Vector=function(){function t(A,e){this.type=0,this.x=A,this.y=e}return t.prototype.add=function(A,e){return new t(this.x+A,this.y+e)},t}(),lerp=function(A,e,t){return new Vector(A.x+(e.x-A.x)*t,A.y+(e.y-A.y)*t)},BezierCurve=function(){function o(A,e,t,r){this.type=1,this.start=A,this.startControl=e,this.endControl=t,this.end=r}return o.prototype.subdivide=function(A,e){var t=lerp(this.start,this.startControl,A),r=lerp(this.startControl,this.endControl,A),n=lerp(this.endControl,this.end,A),B=lerp(t,r,A),r=lerp(r,n,A),A=lerp(B,r,A);return e?new o(this.start,t,B,A):new o(A,r,n,this.end)},o.prototype.add=function(A,e){return new o(this.start.add(A,e),this.startControl.add(A,e),this.endControl.add(A,e),this.end.add(A,e))},o.prototype.reverse=function(){return new o(this.end,this.endControl,this.startControl,this.start)},o}(),isBezierCurve=function(A){return 1===A.type},BoundCurves=function(A){var e=A.styles,t=A.bounds,r=(w=getAbsoluteValueForTuple(e.borderTopLeftRadius,t.width,t.height))[0],n=w[1],B=(U=getAbsoluteValueForTuple(e.borderTopRightRadius,t.width,t.height))[0],o=U[1],s=(F=getAbsoluteValueForTuple(e.borderBottomRightRadius,t.width,t.height))[0],i=F[1],a=(E=getAbsoluteValueForTuple(e.borderBottomLeftRadius,t.width,t.height))[0],c=E[1];(d=[]).push((r+B)/t.width),d.push((a+s)/t.width),d.push((n+c)/t.height),d.push((o+i)/t.height),1<(h=Math.max.apply(Math,d))&&(r/=h,n/=h,B/=h,o/=h,s/=h,i/=h,a/=h,c/=h);var g=t.width-B,Q=t.height-i,C=t.width-s,l=t.height-c,u=e.borderTopWidth,w=e.borderRightWidth,U=e.borderBottomWidth,F=e.borderLeftWidth,E=getAbsoluteValue(e.paddingTop,A.bounds.width),d=getAbsoluteValue(e.paddingRight,A.bounds.width),h=getAbsoluteValue(e.paddingBottom,A.bounds.width),A=getAbsoluteValue(e.paddingLeft,A.bounds.width);this.topLeftBorderDoubleOuterBox=0<r||0<n?getCurvePoints(t.left+F/3,t.top+u/3,r-F/3,n-u/3,CORNER.TOP_LEFT):new Vector(t.left+F/3,t.top+u/3),this.topRightBorderDoubleOuterBox=0<r||0<n?getCurvePoints(t.left+g,t.top+u/3,B-w/3,o-u/3,CORNER.TOP_RIGHT):new Vector(t.left+t.width-w/3,t.top+u/3),this.bottomRightBorderDoubleOuterBox=0<s||0<i?getCurvePoints(t.left+C,t.top+Q,s-w/3,i-U/3,CORNER.BOTTOM_RIGHT):new Vector(t.left+t.width-w/3,t.top+t.height-U/3),this.bottomLeftBorderDoubleOuterBox=0<a||0<c?getCurvePoints(t.left+F/3,t.top+l,a-F/3,c-U/3,CORNER.BOTTOM_LEFT):new Vector(t.left+F/3,t.top+t.height-U/3),this.topLeftBorderDoubleInnerBox=0<r||0<n?getCurvePoints(t.left+2*F/3,t.top+2*u/3,r-2*F/3,n-2*u/3,CORNER.TOP_LEFT):new Vector(t.left+2*F/3,t.top+2*u/3),this.topRightBorderDoubleInnerBox=0<r||0<n?getCurvePoints(t.left+g,t.top+2*u/3,B-2*w/3,o-2*u/3,CORNER.TOP_RIGHT):new Vector(t.left+t.width-2*w/3,t.top+2*u/3),this.bottomRightBorderDoubleInnerBox=0<s||0<i?getCurvePoints(t.left+C,t.top+Q,s-2*w/3,i-2*U/3,CORNER.BOTTOM_RIGHT):new Vector(t.left+t.width-2*w/3,t.top+t.height-2*U/3),this.bottomLeftBorderDoubleInnerBox=0<a||0<c?getCurvePoints(t.left+2*F/3,t.top+l,a-2*F/3,c-2*U/3,CORNER.BOTTOM_LEFT):new Vector(t.left+2*F/3,t.top+t.height-2*U/3),this.topLeftBorderStroke=0<r||0<n?getCurvePoints(t.left+F/2,t.top+u/2,r-F/2,n-u/2,CORNER.TOP_LEFT):new Vector(t.left+F/2,t.top+u/2),this.topRightBorderStroke=0<r||0<n?getCurvePoints(t.left+g,t.top+u/2,B-w/2,o-u/2,CORNER.TOP_RIGHT):new Vector(t.left+t.width-w/2,t.top+u/2),this.bottomRightBorderStroke=0<s||0<i?getCurvePoints(t.left+C,t.top+Q,s-w/2,i-U/2,CORNER.BOTTOM_RIGHT):new Vector(t.left+t.width-w/2,t.top+t.height-U/2),this.bottomLeftBorderStroke=0<a||0<c?getCurvePoints(t.left+F/2,t.top+l,a-F/2,c-U/2,CORNER.BOTTOM_LEFT):new Vector(t.left+F/2,t.top+t.height-U/2),this.topLeftBorderBox=0<r||0<n?getCurvePoints(t.left,t.top,r,n,CORNER.TOP_LEFT):new Vector(t.left,t.top),this.topRightBorderBox=0<B||0<o?getCurvePoints(t.left+g,t.top,B,o,CORNER.TOP_RIGHT):new Vector(t.left+t.width,t.top),this.bottomRightBorderBox=0<s||0<i?getCurvePoints(t.left+C,t.top+Q,s,i,CORNER.BOTTOM_RIGHT):new Vector(t.left+t.width,t.top+t.height),this.bottomLeftBorderBox=0<a||0<c?getCurvePoints(t.left,t.top+l,a,c,CORNER.BOTTOM_LEFT):new Vector(t.left,t.top+t.height),this.topLeftPaddingBox=0<r||0<n?getCurvePoints(t.left+F,t.top+u,Math.max(0,r-F),Math.max(0,n-u),CORNER.TOP_LEFT):new Vector(t.left+F,t.top+u),this.topRightPaddingBox=0<B||0<o?getCurvePoints(t.left+Math.min(g,t.width-w),t.top+u,g>t.width+w?0:Math.max(0,B-w),Math.max(0,o-u),CORNER.TOP_RIGHT):new Vector(t.left+t.width-w,t.top+u),this.bottomRightPaddingBox=0<s||0<i?getCurvePoints(t.left+Math.min(C,t.width-F),t.top+Math.min(Q,t.height-U),Math.max(0,s-w),Math.max(0,i-U),CORNER.BOTTOM_RIGHT):new Vector(t.left+t.width-w,t.top+t.height-U),this.bottomLeftPaddingBox=0<a||0<c?getCurvePoints(t.left+F,t.top+Math.min(l,t.height-U),Math.max(0,a-F),Math.max(0,c-U),CORNER.BOTTOM_LEFT):new Vector(t.left+F,t.top+t.height-U),this.topLeftContentBox=0<r||0<n?getCurvePoints(t.left+F+A,t.top+u+E,Math.max(0,r-(F+A)),Math.max(0,n-(u+E)),CORNER.TOP_LEFT):new Vector(t.left+F+A,t.top+u+E),this.topRightContentBox=0<B||0<o?getCurvePoints(t.left+Math.min(g,t.width+F+A),t.top+u+E,g>t.width+F+A?0:B-F+A,o-(u+E),CORNER.TOP_RIGHT):new Vector(t.left+t.width-(w+d),t.top+u+E),this.bottomRightContentBox=0<s||0<i?getCurvePoints(t.left+Math.min(C,t.width-(F+A)),t.top+Math.min(Q,t.height+u+E),Math.max(0,s-(w+d)),i-(U+h),CORNER.BOTTOM_RIGHT):new Vector(t.left+t.width-(w+d),t.top+t.height-(U+h)),this.bottomLeftContentBox=0<a||0<c?getCurvePoints(t.left+F+A,t.top+l,Math.max(0,a-(F+A)),c-(U+h),CORNER.BOTTOM_LEFT):new Vector(t.left+F+A,t.top+t.height-(U+h))};!function(A){A[A.TOP_LEFT=0]="TOP_LEFT",A[A.TOP_RIGHT=1]="TOP_RIGHT",A[A.BOTTOM_RIGHT=2]="BOTTOM_RIGHT",A[A.BOTTOM_LEFT=3]="BOTTOM_LEFT"}(CORNER=CORNER||{});var getCurvePoints=function(A,e,t,r,n){var B=(Math.sqrt(2)-1)/3*4,o=t*B,s=r*B,i=A+t,a=e+r;switch(n){case CORNER.TOP_LEFT:return new BezierCurve(new Vector(A,a),new Vector(A,a-s),new Vector(i-o,e),new Vector(i,e));case CORNER.TOP_RIGHT:return new BezierCurve(new Vector(A,e),new Vector(A+o,e),new Vector(i,a-s),new Vector(i,a));case CORNER.BOTTOM_RIGHT:return new BezierCurve(new Vector(i,e),new Vector(i,e+s),new Vector(A+o,a),new Vector(A,a));default:CORNER.BOTTOM_LEFT;return new BezierCurve(new Vector(i,a),new Vector(i-o,a),new Vector(A,e+s),new Vector(A,e))}},calculateBorderBoxPath=function(A){return[A.topLeftBorderBox,A.topRightBorderBox,A.bottomRightBorderBox,A.bottomLeftBorderBox]},calculateContentBoxPath=function(A){return[A.topLeftContentBox,A.topRightContentBox,A.bottomRightContentBox,A.bottomLeftContentBox]},calculatePaddingBoxPath=function(A){return[A.topLeftPaddingBox,A.topRightPaddingBox,A.bottomRightPaddingBox,A.bottomLeftPaddingBox]},TransformEffect=function(A,e,t){this.offsetX=A,this.offsetY=e,this.matrix=t,this.type=0,this.target=6},ClipEffect=function(A,e){this.path=A,this.target=e,this.type=1},OpacityEffect=function(A){this.opacity=A,this.type=2,this.target=6},isTransformEffect=function(A){return 0===A.type},isClipEffect=function(A){return 1===A.type},isOpacityEffect=function(A){return 2===A.type},equalPath=function(A,t){return A.length===t.length&&A.some(function(A,e){return A===t[e]})},transformPath=function(A,t,r,n,B){return A.map(function(A,e){switch(e){case 0:return A.add(t,r);case 1:return A.add(t+n,r);case 2:return A.add(t+n,r+B);case 3:return A.add(t,r+B)}return A})},StackingContext=function(A){this.element=A,this.inlineLevel=[],this.nonInlineLevel=[],this.negativeZIndex=[],this.zeroOrAutoZIndexOrTransformedOrOpacity=[],this.positiveZIndex=[],this.nonPositionedFloats=[],this.nonPositionedInlineLevel=[]},ElementPaint=function(){function A(A,e){var t,r;this.container=A,this.parent=e,this.effects=[],this.curves=new BoundCurves(this.container),this.container.styles.opacity<1&&this.effects.push(new OpacityEffect(this.container.styles.opacity)),null!==this.container.styles.transform&&(e=this.container.bounds.left+this.container.styles.transformOrigin[0].number,t=this.container.bounds.top+this.container.styles.transformOrigin[1].number,r=this.container.styles.transform,this.effects.push(new TransformEffect(e,t,r))),0!==this.container.styles.overflowX&&(t=calculateBorderBoxPath(this.curves),r=calculatePaddingBoxPath(this.curves),equalPath(t,r)?this.effects.push(new ClipEffect(t,6)):(this.effects.push(new ClipEffect(t,2)),this.effects.push(new ClipEffect(r,4))))}return A.prototype.getEffects=function(e){for(var A=-1===[2,3].indexOf(this.container.styles.position),t=this.parent,r=this.effects.slice(0);t;){var n,B,o=t.effects.filter(function(A){return!isClipEffect(A)});A||0!==t.container.styles.position||!t.parent?(r.unshift.apply(r,o),A=-1===[2,3].indexOf(t.container.styles.position),0!==t.container.styles.overflowX&&(n=calculateBorderBoxPath(t.curves),B=calculatePaddingBoxPath(t.curves),equalPath(n,B)||r.unshift(new ClipEffect(B,6)))):r.unshift.apply(r,o),t=t.parent}return r.filter(function(A){return contains(A.target,e)})},A}(),parseStackTree=function(a,c,g,Q){a.container.elements.forEach(function(A){var e=contains(A.flags,4),t=contains(A.flags,2),r=new ElementPaint(A,a);contains(A.styles.display,2048)&&Q.push(r);var n,B,o,s,i=contains(A.flags,8)?[]:Q;e||t?(n=e||A.styles.isPositioned()?g:c,t=new StackingContext(r),A.styles.isPositioned()||A.styles.opacity<1||A.styles.isTransformed()?(B=A.styles.zIndex.order)<0?(o=0,n.negativeZIndex.some(function(A,e){return B>A.element.container.styles.zIndex.order?(o=e,!1):0<o}),n.negativeZIndex.splice(o,0,t)):0<B?(s=0,n.positiveZIndex.some(function(A,e){return B>=A.element.container.styles.zIndex.order?(s=e+1,!1):0<s}),n.positiveZIndex.splice(s,0,t)):n.zeroOrAutoZIndexOrTransformedOrOpacity.push(t):(A.styles.isFloating()?n.nonPositionedFloats:n.nonPositionedInlineLevel).push(t),parseStackTree(r,t,e?t:g,i)):((A.styles.isInlineLevel()?c.inlineLevel:c.nonInlineLevel).push(r),parseStackTree(r,c,g,i)),contains(A.flags,8)&&processListItems(A,i)})},processListItems=function(A,e){for(var t=A instanceof OLElementContainer?A.start:1,r=A instanceof OLElementContainer&&A.reversed,n=0;n<e.length;n++){var B=e[n];B.container instanceof LIElementContainer&&"number"==typeof B.container.value&&0!==B.container.value&&(t=B.container.value),B.listValue=createCounterText(t,B.container.styles.listStyleType,!0),t+=r?-1:1}},parseStackingContexts=function(A){var e=new ElementPaint(A,null),t=new StackingContext(e),A=[];return parseStackTree(e,t,t,A),processListItems(e.container,A),t},parsePathForBorder=function(A,e){switch(e){case 0:return createPathFromCurves(A.topLeftBorderBox,A.topLeftPaddingBox,A.topRightBorderBox,A.topRightPaddingBox);case 1:return createPathFromCurves(A.topRightBorderBox,A.topRightPaddingBox,A.bottomRightBorderBox,A.bottomRightPaddingBox);case 2:return createPathFromCurves(A.bottomRightBorderBox,A.bottomRightPaddingBox,A.bottomLeftBorderBox,A.bottomLeftPaddingBox);default:return createPathFromCurves(A.bottomLeftBorderBox,A.bottomLeftPaddingBox,A.topLeftBorderBox,A.topLeftPaddingBox)}},parsePathForBorderDoubleOuter=function(A,e){switch(e){case 0:return createPathFromCurves(A.topLeftBorderBox,A.topLeftBorderDoubleOuterBox,A.topRightBorderBox,A.topRightBorderDoubleOuterBox);case 1:return createPathFromCurves(A.topRightBorderBox,A.topRightBorderDoubleOuterBox,A.bottomRightBorderBox,A.bottomRightBorderDoubleOuterBox);case 2:return createPathFromCurves(A.bottomRightBorderBox,A.bottomRightBorderDoubleOuterBox,A.bottomLeftBorderBox,A.bottomLeftBorderDoubleOuterBox);default:return createPathFromCurves(A.bottomLeftBorderBox,A.bottomLeftBorderDoubleOuterBox,A.topLeftBorderBox,A.topLeftBorderDoubleOuterBox)}},parsePathForBorderDoubleInner=function(A,e){switch(e){case 0:return createPathFromCurves(A.topLeftBorderDoubleInnerBox,A.topLeftPaddingBox,A.topRightBorderDoubleInnerBox,A.topRightPaddingBox);case 1:return createPathFromCurves(A.topRightBorderDoubleInnerBox,A.topRightPaddingBox,A.bottomRightBorderDoubleInnerBox,A.bottomRightPaddingBox);case 2:return createPathFromCurves(A.bottomRightBorderDoubleInnerBox,A.bottomRightPaddingBox,A.bottomLeftBorderDoubleInnerBox,A.bottomLeftPaddingBox);default:return createPathFromCurves(A.bottomLeftBorderDoubleInnerBox,A.bottomLeftPaddingBox,A.topLeftBorderDoubleInnerBox,A.topLeftPaddingBox)}},parsePathForBorderStroke=function(A,e){switch(e){case 0:return createStrokePathFromCurves(A.topLeftBorderStroke,A.topRightBorderStroke);case 1:return createStrokePathFromCurves(A.topRightBorderStroke,A.bottomRightBorderStroke);case 2:return createStrokePathFromCurves(A.bottomRightBorderStroke,A.bottomLeftBorderStroke);default:return createStrokePathFromCurves(A.bottomLeftBorderStroke,A.topLeftBorderStroke)}},createStrokePathFromCurves=function(A,e){var t=[];return isBezierCurve(A)?t.push(A.subdivide(.5,!1)):t.push(A),isBezierCurve(e)?t.push(e.subdivide(.5,!0)):t.push(e),t},createPathFromCurves=function(A,e,t,r){var n=[];return isBezierCurve(A)?n.push(A.subdivide(.5,!1)):n.push(A),isBezierCurve(t)?n.push(t.subdivide(.5,!0)):n.push(t),isBezierCurve(r)?n.push(r.subdivide(.5,!0).reverse()):n.push(r),isBezierCurve(e)?n.push(e.subdivide(.5,!1).reverse()):n.push(e),n},paddingBox=function(A){var e=A.bounds,A=A.styles;return e.add(A.borderLeftWidth,A.borderTopWidth,-(A.borderRightWidth+A.borderLeftWidth),-(A.borderTopWidth+A.borderBottomWidth))},contentBox=function(A){var e=A.styles,t=A.bounds,r=getAbsoluteValue(e.paddingLeft,t.width),n=getAbsoluteValue(e.paddingRight,t.width),B=getAbsoluteValue(e.paddingTop,t.width),A=getAbsoluteValue(e.paddingBottom,t.width);return t.add(r+e.borderLeftWidth,B+e.borderTopWidth,-(e.borderRightWidth+e.borderLeftWidth+r+n),-(e.borderTopWidth+e.borderBottomWidth+B+A))},calculateBackgroundPositioningArea=function(A,e){return 0===A?e.bounds:(2===A?contentBox:paddingBox)(e)},calculateBackgroundPaintingArea=function(A,e){return 0===A?e.bounds:(2===A?contentBox:paddingBox)(e)},calculateBackgroundRendering=function(A,e,t){var r=calculateBackgroundPositioningArea(getBackgroundValueForIndex(A.styles.backgroundOrigin,e),A),n=calculateBackgroundPaintingArea(getBackgroundValueForIndex(A.styles.backgroundClip,e),A),B=calculateBackgroundSize(getBackgroundValueForIndex(A.styles.backgroundSize,e),t,r),o=B[0],s=B[1],t=getAbsoluteValueForTuple(getBackgroundValueForIndex(A.styles.backgroundPosition,e),r.width-o,r.height-s);return[calculateBackgroundRepeatPath(getBackgroundValueForIndex(A.styles.backgroundRepeat,e),t,B,r,n),Math.round(r.left+t[0]),Math.round(r.top+t[1]),o,s]},isAuto=function(A){return isIdentToken(A)&&A.value===BACKGROUND_SIZE.AUTO},hasIntrinsicValue=function(A){return"number"==typeof A},calculateBackgroundSize=function(A,e,t){var r=e[0],n=e[1],B=e[2],o=A[0],s=A[1];if(!o)return[0,0];if(isLengthPercentage(o)&&s&&isLengthPercentage(s))return[getAbsoluteValue(o,t.width),getAbsoluteValue(s,t.height)];var i=hasIntrinsicValue(B);if(isIdentToken(o)&&(o.value===BACKGROUND_SIZE.CONTAIN||o.value===BACKGROUND_SIZE.COVER))return hasIntrinsicValue(B)?t.width/t.height<B!=(o.value===BACKGROUND_SIZE.COVER)?[t.width,t.width/B]:[t.height*B,t.height]:[t.width,t.height];var a=hasIntrinsicValue(r),e=hasIntrinsicValue(n),A=a||e;if(isAuto(o)&&(!s||isAuto(s)))return a&&e?[r,n]:i||A?A&&i?[a?r:n*B,e?n:r/B]:[a?r:t.width,e?n:t.height]:[t.width,t.height];if(i){var c=0,g=0;return isLengthPercentage(o)?c=getAbsoluteValue(o,t.width):isLengthPercentage(s)&&(g=getAbsoluteValue(s,t.height)),isAuto(o)?c=g*B:s&&!isAuto(s)||(g=c/B),[c,g]}c=null,g=null;if(isLengthPercentage(o)?c=getAbsoluteValue(o,t.width):s&&isLengthPercentage(s)&&(g=getAbsoluteValue(s,t.height)),null!==(c=null!==(g=null!==c&&(!s||isAuto(s))?a&&e?c/r*n:t.height:g)&&isAuto(o)?a&&e?g/n*r:t.width:c)&&null!==g)return[c,g];throw new Error("Unable to calculate background-size for element")},getBackgroundValueForIndex=function(A,e){e=A[e];return void 0===e?A[0]:e},calculateBackgroundRepeatPath=function(A,e,t,r,n){var B=e[0],o=e[1],s=t[0],i=t[1];switch(A){case 2:return[new Vector(Math.round(r.left),Math.round(r.top+o)),new Vector(Math.round(r.left+r.width),Math.round(r.top+o)),new Vector(Math.round(r.left+r.width),Math.round(i+r.top+o)),new Vector(Math.round(r.left),Math.round(i+r.top+o))];case 3:return[new Vector(Math.round(r.left+B),Math.round(r.top)),new Vector(Math.round(r.left+B+s),Math.round(r.top)),new Vector(Math.round(r.left+B+s),Math.round(r.height+r.top)),new Vector(Math.round(r.left+B),Math.round(r.height+r.top))];case 1:return[new Vector(Math.round(r.left+B),Math.round(r.top+o)),new Vector(Math.round(r.left+B+s),Math.round(r.top+o)),new Vector(Math.round(r.left+B+s),Math.round(r.top+o+i)),new Vector(Math.round(r.left+B),Math.round(r.top+o+i))];default:return[new Vector(Math.round(n.left),Math.round(n.top)),new Vector(Math.round(n.left+n.width),Math.round(n.top)),new Vector(Math.round(n.left+n.width),Math.round(n.height+n.top)),new Vector(Math.round(n.left),Math.round(n.height+n.top))]}},SMALL_IMAGE="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",SAMPLE_TEXT="Hidden Text",FontMetrics=function(){function A(A){this._data={},this._document=A}return A.prototype.parseMetrics=function(A,e){var t=this._document.createElement("div"),r=this._document.createElement("img"),n=this._document.createElement("span"),B=this._document.body;t.style.visibility="hidden",t.style.fontFamily=A,t.style.fontSize=e,t.style.margin="0",t.style.padding="0",t.style.whiteSpace="nowrap",B.appendChild(t),r.src=SMALL_IMAGE,r.width=1,r.height=1,r.style.margin="0",r.style.padding="0",r.style.verticalAlign="baseline",n.style.fontFamily=A,n.style.fontSize=e,n.style.margin="0",n.style.padding="0",n.appendChild(this._document.createTextNode(SAMPLE_TEXT)),t.appendChild(n),t.appendChild(r);e=r.offsetTop-n.offsetTop+2;t.removeChild(n),t.appendChild(this._document.createTextNode(SAMPLE_TEXT)),t.style.lineHeight="normal",r.style.verticalAlign="super";r=r.offsetTop-t.offsetTop+2;return B.removeChild(t),{baseline:e,middle:r}},A.prototype.getMetrics=function(A,e){var t=A+" "+e;return void 0===this._data[t]&&(this._data[t]=this.parseMetrics(A,e)),this._data[t]},A}(),Renderer=function(A,e){this.context=A,this.options=e},MASK_OFFSET=1e4,CanvasRenderer=function(t){function l(A,e){A=t.call(this,A,e)||this;return A._activeEffects=[],A.canvas=e.canvas||document.createElement("canvas"),A.ctx=A.canvas.getContext("2d"),e.canvas||(A.canvas.width=Math.floor(e.width*e.scale),A.canvas.height=Math.floor(e.height*e.scale),A.canvas.style.width=e.width+"px",A.canvas.style.height=e.height+"px"),A.fontMetrics=new FontMetrics(document),A.ctx.scale(A.options.scale,A.options.scale),A.ctx.translate(-e.x,-e.y),A.ctx.textBaseline="bottom",A._activeEffects=[],A.context.logger.debug("Canvas renderer initialized ("+e.width+"x"+e.height+") with scale "+e.scale),A}return __extends(l,t),l.prototype.applyEffects=function(A){for(var e=this;this._activeEffects.length;)this.popEffect();A.forEach(function(A){return e.applyEffect(A)})},l.prototype.applyEffect=function(A){this.ctx.save(),isOpacityEffect(A)&&(this.ctx.globalAlpha=A.opacity),isTransformEffect(A)&&(this.ctx.translate(A.offsetX,A.offsetY),this.ctx.transform(A.matrix[0],A.matrix[1],A.matrix[2],A.matrix[3],A.matrix[4],A.matrix[5]),this.ctx.translate(-A.offsetX,-A.offsetY)),isClipEffect(A)&&(this.path(A.path),this.ctx.clip()),this._activeEffects.push(A)},l.prototype.popEffect=function(){this._activeEffects.pop(),this.ctx.restore()},l.prototype.renderStack=function(e){return __awaiter(this,void 0,void 0,function(){return __generator(this,function(A){switch(A.label){case 0:return e.element.container.styles.isVisible()?[4,this.renderStackContent(e)]:[3,2];case 1:A.sent(),A.label=2;case 2:return[2]}})})},l.prototype.renderNode=function(e){return __awaiter(this,void 0,void 0,function(){return __generator(this,function(A){switch(A.label){case 0:return contains(e.container.flags,16),e.container.styles.isVisible()?[4,this.renderNodeBackgroundAndBorders(e)]:[3,3];case 1:return A.sent(),[4,this.renderNodeContent(e)];case 2:A.sent(),A.label=3;case 3:return[2]}})})},l.prototype.renderTextWithLetterSpacing=function(t,A,r){var n=this;0===A?this.ctx.fillText(t.text,t.bounds.left,t.bounds.top+r):segmentGraphemes(t.text).reduce(function(A,e){return n.ctx.fillText(e,A,t.bounds.top+r),A+n.ctx.measureText(e).width},t.bounds.left)},l.prototype.createFontStyle=function(A){var e=A.fontVariant.filter(function(A){return"normal"===A||"small-caps"===A}).join(""),t=fixIOSSystemFonts(A.fontFamily).join(", "),r=isDimensionToken(A.fontSize)?""+A.fontSize.number+A.fontSize.unit:A.fontSize.number+"px";return[[A.fontStyle,e,A.fontWeight,r,t].join(" "),t,r]},l.prototype.renderTextNode=function(i,a){return __awaiter(this,void 0,void 0,function(){var e,t,r,n,B,o,s=this;return __generator(this,function(A){return r=this.createFontStyle(a),e=r[0],t=r[1],r=r[2],this.ctx.font=e,this.ctx.direction=1===a.direction?"rtl":"ltr",this.ctx.textAlign="left",this.ctx.textBaseline="alphabetic",r=this.fontMetrics.getMetrics(t,r),n=r.baseline,B=r.middle,o=a.paintOrder,i.textBounds.forEach(function(t){o.forEach(function(A){switch(A){case 0:s.ctx.fillStyle=asString(a.color),s.renderTextWithLetterSpacing(t,a.letterSpacing,n);var e=a.textShadow;e.length&&t.text.trim().length&&(e.slice(0).reverse().forEach(function(A){s.ctx.shadowColor=asString(A.color),s.ctx.shadowOffsetX=A.offsetX.number*s.options.scale,s.ctx.shadowOffsetY=A.offsetY.number*s.options.scale,s.ctx.shadowBlur=A.blur.number,s.renderTextWithLetterSpacing(t,a.letterSpacing,n)}),s.ctx.shadowColor="",s.ctx.shadowOffsetX=0,s.ctx.shadowOffsetY=0,s.ctx.shadowBlur=0),a.textDecorationLine.length&&(s.ctx.fillStyle=asString(a.textDecorationColor||a.color),a.textDecorationLine.forEach(function(A){switch(A){case 1:s.ctx.fillRect(t.bounds.left,Math.round(t.bounds.top+n),t.bounds.width,1);break;case 2:s.ctx.fillRect(t.bounds.left,Math.round(t.bounds.top),t.bounds.width,1);break;case 3:s.ctx.fillRect(t.bounds.left,Math.ceil(t.bounds.top+B),t.bounds.width,1)}}));break;case 1:a.webkitTextStrokeWidth&&t.text.trim().length&&(s.ctx.strokeStyle=asString(a.webkitTextStrokeColor),s.ctx.lineWidth=a.webkitTextStrokeWidth,s.ctx.lineJoin=window.chrome?"miter":"round",s.ctx.strokeText(t.text,t.bounds.left,t.bounds.top+n)),s.ctx.strokeStyle="",s.ctx.lineWidth=0,s.ctx.lineJoin="miter"}})}),[2]})})},l.prototype.renderReplacedElement=function(A,e,t){var r;t&&0<A.intrinsicWidth&&0<A.intrinsicHeight&&(r=contentBox(A),e=calculatePaddingBoxPath(e),this.path(e),this.ctx.save(),this.ctx.clip(),this.ctx.drawImage(t,0,0,A.intrinsicWidth,A.intrinsicHeight,r.left,r.top,r.width,r.height),this.ctx.restore())},l.prototype.renderNodeContent=function(C){return __awaiter(this,void 0,void 0,function(){var e,t,r,n,B,o,s,i,a,c,g,Q;return __generator(this,function(A){switch(A.label){case 0:this.applyEffects(C.getEffects(4)),e=C.container,t=C.curves,r=e.styles,n=0,B=e.textNodes,A.label=1;case 1:return n<B.length?(o=B[n],[4,this.renderTextNode(o,r)]):[3,4];case 2:A.sent(),A.label=3;case 3:return n++,[3,1];case 4:if(!(e instanceof ImageElementContainer))return[3,8];A.label=5;case 5:return A.trys.push([5,7,,8]),[4,this.context.cache.match(e.src)];case 6:return a=A.sent(),this.renderReplacedElement(e,t,a),[3,8];case 7:return A.sent(),this.context.logger.error("Error loading image "+e.src),[3,8];case 8:if(e instanceof CanvasElementContainer&&this.renderReplacedElement(e,t,e.canvas),!(e instanceof SVGElementContainer))return[3,12];A.label=9;case 9:return A.trys.push([9,11,,12]),[4,this.context.cache.match(e.svg)];case 10:return a=A.sent(),this.renderReplacedElement(e,t,a),[3,12];case 11:return A.sent(),this.context.logger.error("Error loading svg "+e.svg.substring(0,255)),[3,12];case 12:return e instanceof IFrameElementContainer&&e.tree?[4,new l(this.context,{scale:this.options.scale,backgroundColor:e.backgroundColor,x:0,y:0,width:e.width,height:e.height}).render(e.tree)]:[3,14];case 13:o=A.sent(),e.width&&e.height&&this.ctx.drawImage(o,0,0,e.width,e.height,e.bounds.left,e.bounds.top,e.bounds.width,e.bounds.height),A.label=14;case 14:if(e instanceof InputElementContainer&&(i=Math.min(e.bounds.width,e.bounds.height),e.type===CHECKBOX?e.checked&&(this.ctx.save(),this.path([new Vector(e.bounds.left+.39363*i,e.bounds.top+.79*i),new Vector(e.bounds.left+.16*i,e.bounds.top+.5549*i),new Vector(e.bounds.left+.27347*i,e.bounds.top+.44071*i),new Vector(e.bounds.left+.39694*i,e.bounds.top+.5649*i),new Vector(e.bounds.left+.72983*i,e.bounds.top+.23*i),new Vector(e.bounds.left+.84*i,e.bounds.top+.34085*i),new Vector(e.bounds.left+.39363*i,e.bounds.top+.79*i)]),this.ctx.fillStyle=asString(INPUT_COLOR),this.ctx.fill(),this.ctx.restore()):e.type===RADIO&&e.checked&&(this.ctx.save(),this.ctx.beginPath(),this.ctx.arc(e.bounds.left+i/2,e.bounds.top+i/2,i/4,0,2*Math.PI,!0),this.ctx.fillStyle=asString(INPUT_COLOR),this.ctx.fill(),this.ctx.restore())),isTextInputElement(e)&&e.value.length){switch(c=this.createFontStyle(r),g=c[0],i=c[1],c=this.fontMetrics.getMetrics(g,i).baseline,this.ctx.font=g,this.ctx.fillStyle=asString(r.color),this.ctx.textBaseline="alphabetic",this.ctx.textAlign=canvasTextAlign(e.styles.textAlign),Q=contentBox(e),s=0,e.styles.textAlign){case 1:s+=Q.width/2;break;case 2:s+=Q.width}i=Q.add(s,0,0,-Q.height/2+1),this.ctx.save(),this.path([new Vector(Q.left,Q.top),new Vector(Q.left+Q.width,Q.top),new Vector(Q.left+Q.width,Q.top+Q.height),new Vector(Q.left,Q.top+Q.height)]),this.ctx.clip(),this.renderTextWithLetterSpacing(new TextBounds(e.value,i),r.letterSpacing,c),this.ctx.restore(),this.ctx.textBaseline="alphabetic",this.ctx.textAlign="left"}if(!contains(e.styles.display,2048))return[3,20];if(null===e.styles.listStyleImage)return[3,19];if(0!==(c=e.styles.listStyleImage).type)return[3,18];a=void 0,c=c.url,A.label=15;case 15:return A.trys.push([15,17,,18]),[4,this.context.cache.match(c)];case 16:return a=A.sent(),this.ctx.drawImage(a,e.bounds.left-(a.width+10),e.bounds.top),[3,18];case 17:return A.sent(),this.context.logger.error("Error loading list-style-image "+c),[3,18];case 18:return[3,20];case 19:C.listValue&&-1!==e.styles.listStyleType&&(g=this.createFontStyle(r)[0],this.ctx.font=g,this.ctx.fillStyle=asString(r.color),this.ctx.textBaseline="middle",this.ctx.textAlign="right",Q=new Bounds(e.bounds.left,e.bounds.top+getAbsoluteValue(e.styles.paddingTop,e.bounds.width),e.bounds.width,computeLineHeight(r.lineHeight,r.fontSize.number)/2+1),this.renderTextWithLetterSpacing(new TextBounds(C.listValue,Q),r.letterSpacing,computeLineHeight(r.lineHeight,r.fontSize.number)/2+2),this.ctx.textBaseline="bottom",this.ctx.textAlign="left"),A.label=20;case 20:return[2]}})})},l.prototype.renderStackContent=function(w){return __awaiter(this,void 0,void 0,function(){var e,t,r,n,B,o,s,i,a,c,g,Q,C,l,u;return __generator(this,function(A){switch(A.label){case 0:return contains(w.element.container.flags,16),[4,this.renderNodeBackgroundAndBorders(w.element)];case 1:A.sent(),e=0,t=w.negativeZIndex,A.label=2;case 2:return e<t.length?(u=t[e],[4,this.renderStack(u)]):[3,5];case 3:A.sent(),A.label=4;case 4:return e++,[3,2];case 5:return[4,this.renderNodeContent(w.element)];case 6:A.sent(),r=0,n=w.nonInlineLevel,A.label=7;case 7:return r<n.length?(u=n[r],[4,this.renderNode(u)]):[3,10];case 8:A.sent(),A.label=9;case 9:return r++,[3,7];case 10:B=0,o=w.nonPositionedFloats,A.label=11;case 11:return B<o.length?(u=o[B],[4,this.renderStack(u)]):[3,14];case 12:A.sent(),A.label=13;case 13:return B++,[3,11];case 14:s=0,i=w.nonPositionedInlineLevel,A.label=15;case 15:return s<i.length?(u=i[s],[4,this.renderStack(u)]):[3,18];case 16:A.sent(),A.label=17;case 17:return s++,[3,15];case 18:a=0,c=w.inlineLevel,A.label=19;case 19:return a<c.length?(u=c[a],[4,this.renderNode(u)]):[3,22];case 20:A.sent(),A.label=21;case 21:return a++,[3,19];case 22:g=0,Q=w.zeroOrAutoZIndexOrTransformedOrOpacity,A.label=23;case 23:return g<Q.length?(u=Q[g],[4,this.renderStack(u)]):[3,26];case 24:A.sent(),A.label=25;case 25:return g++,[3,23];case 26:C=0,l=w.positiveZIndex,A.label=27;case 27:return C<l.length?(u=l[C],[4,this.renderStack(u)]):[3,30];case 28:A.sent(),A.label=29;case 29:return C++,[3,27];case 30:return[2]}})})},l.prototype.mask=function(A){this.ctx.beginPath(),this.ctx.moveTo(0,0),this.ctx.lineTo(this.canvas.width,0),this.ctx.lineTo(this.canvas.width,this.canvas.height),this.ctx.lineTo(0,this.canvas.height),this.ctx.lineTo(0,0),this.formatPath(A.slice(0).reverse()),this.ctx.closePath()},l.prototype.path=function(A){this.ctx.beginPath(),this.formatPath(A),this.ctx.closePath()},l.prototype.formatPath=function(A){var r=this;A.forEach(function(A,e){var t=isBezierCurve(A)?A.start:A;0===e?r.ctx.moveTo(t.x,t.y):r.ctx.lineTo(t.x,t.y),isBezierCurve(A)&&r.ctx.bezierCurveTo(A.startControl.x,A.startControl.y,A.endControl.x,A.endControl.y,A.end.x,A.end.y)})},l.prototype.renderRepeat=function(A,e,t,r){this.path(A),this.ctx.fillStyle=e,this.ctx.translate(t,r),this.ctx.fill(),this.ctx.translate(-t,-r)},l.prototype.resizeImage=function(A,e,t){if(A.width===e&&A.height===t)return A;var r=(null!==(r=this.canvas.ownerDocument)&&void 0!==r?r:document).createElement("canvas");return r.width=Math.max(1,e),r.height=Math.max(1,t),r.getContext("2d").drawImage(A,0,0,A.width,A.height,0,0,e,t),r},l.prototype.renderBackgroundImage=function(h){return __awaiter(this,void 0,void 0,function(){var E,e,d,t,r,n;return __generator(this,function(A){switch(A.label){case 0:E=h.styles.backgroundImage.length-1,e=function(e){var t,r,n,B,o,s,i,a,c,g,Q,C,l,u,w,U,F;return __generator(this,function(A){switch(A.label){case 0:if(0!==e.type)return[3,5];t=void 0,r=e.url,A.label=1;case 1:return A.trys.push([1,3,,4]),[4,d.context.cache.match(r)];case 2:return t=A.sent(),[3,4];case 3:return A.sent(),d.context.logger.error("Error loading background-image "+r),[3,4];case 4:return t&&(n=calculateBackgroundRendering(h,E,[t.width,t.height,t.width/t.height]),s=n[0],Q=n[1],C=n[2],c=n[3],g=n[4],o=d.ctx.createPattern(d.resizeImage(t,c,g),"repeat"),d.renderRepeat(s,o,Q,C)),[3,6];case 5:isLinearGradient(e)?(F=calculateBackgroundRendering(h,E,[null,null,null]),s=F[0],Q=F[1],C=F[2],c=F[3],g=F[4],w=calculateGradientDirection(e.angle,c,g),u=w[0],n=w[1],i=w[2],U=w[3],a=w[4],(F=document.createElement("canvas")).width=c,F.height=g,w=F.getContext("2d"),B=w.createLinearGradient(n,U,i,a),processColorStops(e.stops,u).forEach(function(A){return B.addColorStop(A.stop,asString(A.color))}),w.fillStyle=B,w.fillRect(0,0,c,g),0<c&&0<g&&(o=d.ctx.createPattern(F,"repeat"),d.renderRepeat(s,o,Q,C))):isRadialGradient(e)&&(U=calculateBackgroundRendering(h,E,[null,null,null]),s=U[0],i=U[1],a=U[2],c=U[3],g=U[4],u=0===e.position.length?[FIFTY_PERCENT]:e.position,Q=getAbsoluteValue(u[0],c),C=getAbsoluteValue(u[u.length-1],g),w=calculateRadius(e,Q,C,c,g),F=w[0],U=w[1],0<F&&0<U&&(l=d.ctx.createRadialGradient(i+Q,a+C,0,i+Q,a+C,F),processColorStops(e.stops,2*F).forEach(function(A){return l.addColorStop(A.stop,asString(A.color))}),d.path(s),d.ctx.fillStyle=l,F!==U?(u=h.bounds.left+.5*h.bounds.width,w=h.bounds.top+.5*h.bounds.height,F=1/(U=U/F),d.ctx.save(),d.ctx.translate(u,w),d.ctx.transform(1,0,0,U,0,0),d.ctx.translate(-u,-w),d.ctx.fillRect(i,F*(a-w)+w,c,g*F),d.ctx.restore()):d.ctx.fill())),A.label=6;case 6:return E--,[2]}})},d=this,t=0,r=h.styles.backgroundImage.slice(0).reverse(),A.label=1;case 1:return t<r.length?(n=r[t],[5,e(n)]):[3,4];case 2:A.sent(),A.label=3;case 3:return t++,[3,1];case 4:return[2]}})})},l.prototype.renderSolidBorder=function(e,t,r){return __awaiter(this,void 0,void 0,function(){return __generator(this,function(A){return this.path(parsePathForBorder(r,t)),this.ctx.fillStyle=asString(e),this.ctx.fill(),[2]})})},l.prototype.renderDoubleBorder=function(t,r,n,B){return __awaiter(this,void 0,void 0,function(){var e;return __generator(this,function(A){switch(A.label){case 0:return r<3?[4,this.renderSolidBorder(t,n,B)]:[3,2];case 1:return A.sent(),[2];case 2:return e=parsePathForBorderDoubleOuter(B,n),this.path(e),this.ctx.fillStyle=asString(t),this.ctx.fill(),e=parsePathForBorderDoubleInner(B,n),this.path(e),this.ctx.fill(),[2]}})})},l.prototype.renderNodeBackgroundAndBorders=function(c){return __awaiter(this,void 0,void 0,function(){var e,t,r,n,B,o,s,i,a=this;return __generator(this,function(A){switch(A.label){case 0:return(this.applyEffects(c.getEffects(2)),e=c.container.styles,t=!isTransparent(e.backgroundColor)||e.backgroundImage.length,r=[{style:e.borderTopStyle,color:e.borderTopColor,width:e.borderTopWidth},{style:e.borderRightStyle,color:e.borderRightColor,width:e.borderRightWidth},{style:e.borderBottomStyle,color:e.borderBottomColor,width:e.borderBottomWidth},{style:e.borderLeftStyle,color:e.borderLeftColor,width:e.borderLeftWidth}],n=calculateBackgroundCurvedPaintingArea(getBackgroundValueForIndex(e.backgroundClip,0),c.curves),t||e.boxShadow.length)?(this.ctx.save(),this.path(n),this.ctx.clip(),isTransparent(e.backgroundColor)||(this.ctx.fillStyle=asString(e.backgroundColor),this.ctx.fill()),[4,this.renderBackgroundImage(c.container)]):[3,2];case 1:A.sent(),this.ctx.restore(),e.boxShadow.slice(0).reverse().forEach(function(A){a.ctx.save();var e=calculateBorderBoxPath(c.curves),t=A.inset?0:MASK_OFFSET,r=transformPath(e,-t+(A.inset?1:-1)*A.spread.number,(A.inset?1:-1)*A.spread.number,A.spread.number*(A.inset?-2:2),A.spread.number*(A.inset?-2:2));A.inset?(a.path(e),a.ctx.clip(),a.mask(r)):(a.mask(e),a.ctx.clip(),a.path(r)),a.ctx.shadowOffsetX=A.offsetX.number+t,a.ctx.shadowOffsetY=A.offsetY.number,a.ctx.shadowColor=asString(A.color),a.ctx.shadowBlur=A.blur.number,a.ctx.fillStyle=A.inset?asString(A.color):"rgba(0,0,0,1)",a.ctx.fill(),a.ctx.restore()}),A.label=2;case 2:o=B=0,s=r,A.label=3;case 3:return o<s.length?0!==(i=s[o]).style&&!isTransparent(i.color)&&0<i.width?2!==i.style?[3,5]:[4,this.renderDashedDottedBorder(i.color,i.width,B,c.curves,2)]:[3,11]:[3,13];case 4:return A.sent(),[3,11];case 5:return 3!==i.style?[3,7]:[4,this.renderDashedDottedBorder(i.color,i.width,B,c.curves,3)];case 6:return A.sent(),[3,11];case 7:return 4!==i.style?[3,9]:[4,this.renderDoubleBorder(i.color,i.width,B,c.curves)];case 8:return A.sent(),[3,11];case 9:return[4,this.renderSolidBorder(i.color,B,c.curves)];case 10:A.sent(),A.label=11;case 11:B++,A.label=12;case 12:return o++,[3,3];case 13:return[2]}})})},l.prototype.renderDashedDottedBorder=function(Q,C,l,u,w){return __awaiter(this,void 0,void 0,function(){var e,t,r,n,B,o,s,i,a,c,g;return __generator(this,function(A){return this.ctx.save(),a=parsePathForBorderStroke(u,l),e=parsePathForBorder(u,l),2===w&&(this.path(e),this.ctx.clip()),o=isBezierCurve(e[0])?(t=e[0].start.x,e[0].start.y):(t=e[0].x,e[0].y),s=isBezierCurve(e[1])?(r=e[1].end.x,e[1].end.y):(r=e[1].x,e[1].y),n=0===l||2===l?Math.abs(t-r):Math.abs(o-s),this.ctx.beginPath(),3===w?this.formatPath(a):this.formatPath(e.slice(0,2)),B=C<3?3*C:2*C,o=C<3?2*C:C,3===w&&(o=B=C),s=!0,n<=2*B?s=!1:n<=2*B+o?(B*=i=n/(2*B+o),o*=i):(a=Math.floor((n+o)/(B+o)),i=(n-a*B)/(a-1),o=(a=(n-(a+1)*B)/a)<=0||Math.abs(o-i)<Math.abs(o-a)?i:a),s&&(3===w?this.ctx.setLineDash([0,B+o]):this.ctx.setLineDash([B,o])),3===w?(this.ctx.lineCap="round",this.ctx.lineWidth=C):this.ctx.lineWidth=2*C+1.1,this.ctx.strokeStyle=asString(Q),this.ctx.stroke(),this.ctx.setLineDash([]),2===w&&(isBezierCurve(e[0])&&(c=e[3],g=e[0],this.ctx.beginPath(),this.formatPath([new Vector(c.end.x,c.end.y),new Vector(g.start.x,g.start.y)]),this.ctx.stroke()),isBezierCurve(e[1])&&(c=e[1],g=e[2],this.ctx.beginPath(),this.formatPath([new Vector(c.end.x,c.end.y),new Vector(g.start.x,g.start.y)]),this.ctx.stroke())),this.ctx.restore(),[2]})})},l.prototype.render=function(t){return __awaiter(this,void 0,void 0,function(){var e;return __generator(this,function(A){switch(A.label){case 0:return this.options.backgroundColor&&(this.ctx.fillStyle=asString(this.options.backgroundColor),this.ctx.fillRect(this.options.x,this.options.y,this.options.width,this.options.height)),e=parseStackingContexts(t),[4,this.renderStack(e)];case 1:return A.sent(),this.applyEffects([]),[2,this.canvas]}})})},l}(Renderer),isTextInputElement=function(A){return A instanceof TextareaElementContainer||(A instanceof SelectElementContainer||A instanceof InputElementContainer&&A.type!==RADIO&&A.type!==CHECKBOX)},calculateBackgroundCurvedPaintingArea=function(A,e){switch(A){case 0:return calculateBorderBoxPath(e);case 2:return calculateContentBoxPath(e);default:return calculatePaddingBoxPath(e)}},canvasTextAlign=function(A){switch(A){case 1:return"center";case 2:return"right";default:return"left"}},iOSBrokenFonts=["-apple-system","system-ui"],fixIOSSystemFonts=function(A){return/iPhone OS 15_(0|1)/.test(window.navigator.userAgent)?A.filter(function(A){return-1===iOSBrokenFonts.indexOf(A)}):A},ForeignObjectRenderer=function(t){function A(A,e){A=t.call(this,A,e)||this;return A.canvas=e.canvas||document.createElement("canvas"),A.ctx=A.canvas.getContext("2d"),A.options=e,A.canvas.width=Math.floor(e.width*e.scale),A.canvas.height=Math.floor(e.height*e.scale),A.canvas.style.width=e.width+"px",A.canvas.style.height=e.height+"px",A.ctx.scale(A.options.scale,A.options.scale),A.ctx.translate(-e.x,-e.y),A.context.logger.debug("EXPERIMENTAL ForeignObject renderer initialized ("+e.width+"x"+e.height+" at "+e.x+","+e.y+") with scale "+e.scale),A}return __extends(A,t),A.prototype.render=function(t){return __awaiter(this,void 0,void 0,function(){var e;return __generator(this,function(A){switch(A.label){case 0:return e=createForeignObjectSVG(this.options.width*this.options.scale,this.options.height*this.options.scale,this.options.scale,this.options.scale,t),[4,loadSerializedSVG(e)];case 1:return e=A.sent(),this.options.backgroundColor&&(this.ctx.fillStyle=asString(this.options.backgroundColor),this.ctx.fillRect(0,0,this.options.width*this.options.scale,this.options.height*this.options.scale)),this.ctx.drawImage(e,-this.options.x*this.options.scale,-this.options.y*this.options.scale),[2,this.canvas]}})})},A}(Renderer),loadSerializedSVG=function(r){return new Promise(function(A,e){var t=new Image;t.onload=function(){A(t)},t.onerror=e,t.src="data:image/svg+xml;charset=utf-8,"+encodeURIComponent((new XMLSerializer).serializeToString(r))})},Logger=function(){function A(A){var e=A.id,A=A.enabled;this.id=e,this.enabled=A,this.start=Date.now()}return A.prototype.debug=function(){for(var A=[],e=0;e<arguments.length;e++)A[e]=arguments[e];this.enabled&&("undefined"!=typeof window&&window.console&&"function"==typeof console.debug?console.debug.apply(console,__spreadArray([this.id,this.getTime()+"ms"],A)):this.info.apply(this,A))},A.prototype.getTime=function(){return Date.now()-this.start},A.prototype.info=function(){for(var A=[],e=0;e<arguments.length;e++)A[e]=arguments[e];this.enabled&&"undefined"!=typeof window&&window.console&&"function"==typeof console.info&&console.info.apply(console,__spreadArray([this.id,this.getTime()+"ms"],A))},A.prototype.warn=function(){for(var A=[],e=0;e<arguments.length;e++)A[e]=arguments[e];this.enabled&&("undefined"!=typeof window&&window.console&&"function"==typeof console.warn?console.warn.apply(console,__spreadArray([this.id,this.getTime()+"ms"],A)):this.info.apply(this,A))},A.prototype.error=function(){for(var A=[],e=0;e<arguments.length;e++)A[e]=arguments[e];this.enabled&&("undefined"!=typeof window&&window.console&&"function"==typeof console.error?console.error.apply(console,__spreadArray([this.id,this.getTime()+"ms"],A)):this.info.apply(this,A))},A.instances={},A}(),Context=function(){function t(A,e){this.windowBounds=e,this.instanceName="#"+t.instanceCount++,this.logger=new Logger({id:this.instanceName,enabled:A.logging}),this.cache=null!==(e=A.cache)&&void 0!==e?e:new Cache(this,A)}return t.instanceCount=1,t}(),html2canvas=function(A,e){return renderElement(A,e=void 0===e?{}:e)};"undefined"!=typeof window&&CacheStorage.setContext(window);var renderElement=function(U,F){return __awaiter(void 0,void 0,void 0,function(){var e,t,r,n,B,o,s,i,a,c,g,Q,C,l,u,w;return __generator(this,function(A){switch(A.label){case 0:if(!U||"object"!=typeof U)return[2,Promise.reject("Invalid element provided as first argument")];if(!(e=U.ownerDocument))throw new Error("Element is not attached to a Document");if(!(t=e.defaultView))throw new Error("Document is not attached to a Window");return C={allowTaint:null!==(l=F.allowTaint)&&void 0!==l&&l,imageTimeout:null!==(c=F.imageTimeout)&&void 0!==c?c:15e3,proxy:F.proxy,useCORS:null!==(g=F.useCORS)&&void 0!==g&&g},l=__assign({logging:null===(Q=F.logging)||void 0===Q||Q,cache:F.cache},C),c={windowWidth:null!==(c=F.windowWidth)&&void 0!==c?c:t.innerWidth,windowHeight:null!==(g=F.windowHeight)&&void 0!==g?g:t.innerHeight,scrollX:null!==(Q=F.scrollX)&&void 0!==Q?Q:t.pageXOffset,scrollY:null!==(C=F.scrollY)&&void 0!==C?C:t.pageYOffset},g=new Bounds(c.scrollX,c.scrollY,c.windowWidth,c.windowHeight),Q=new Context(l,g),c=null!==(C=F.foreignObjectRendering)&&void 0!==C&&C,C={allowTaint:null!==(l=F.allowTaint)&&void 0!==l&&l,onclone:F.onclone,ignoreElements:F.ignoreElements,inlineImages:c,copyStyles:c},Q.logger.debug("Starting document clone with size "+g.width+"x"+g.height+" scrolled to "+-g.left+","+-g.top),l=new DocumentCloner(Q,U,C),(C=l.clonedReferenceElement)?[4,l.toIFrame(e,g)]:[2,Promise.reject("Unable to find element in cloned iframe")];case 1:return(r=A.sent(),u=isBodyElement(C)||isHTMLElement(C)?parseDocumentSize(C.ownerDocument):parseBounds(Q,C),n=u.width,B=u.height,o=u.left,s=u.top,i=parseBackgroundColor(Q,C,F.backgroundColor),u={canvas:F.canvas,backgroundColor:i,scale:null!==(u=null!==(u=F.scale)&&void 0!==u?u:t.devicePixelRatio)&&void 0!==u?u:1,x:(null!==(u=F.x)&&void 0!==u?u:0)+o,y:(null!==(u=F.y)&&void 0!==u?u:0)+s,width:null!==(u=F.width)&&void 0!==u?u:Math.ceil(n),height:null!==(u=F.height)&&void 0!==u?u:Math.ceil(B)},c)?(Q.logger.debug("Document cloned, using foreign object rendering"),[4,new ForeignObjectRenderer(Q,u).render(C)]):[3,3];case 2:return a=A.sent(),[3,5];case 3:return Q.logger.debug("Document cloned, element located at "+o+","+s+" with size "+n+"x"+B+" using computed rendering"),Q.logger.debug("Starting DOM parsing"),w=parseTree(Q,C),i===w.styles.backgroundColor&&(w.styles.backgroundColor=COLORS.TRANSPARENT),Q.logger.debug("Starting renderer for element at "+u.x+","+u.y+" with size "+u.width+"x"+u.height),[4,new CanvasRenderer(Q,u).render(w)];case 4:a=A.sent(),A.label=5;case 5:return null!==(w=F.removeContainer)&&void 0!==w&&!w||DocumentCloner.destroy(r)||Q.logger.error("Cannot detach cloned iframe as it is not in the DOM anymore"),Q.logger.debug("Finished rendering"),[2,a]}})})},parseBackgroundColor=function(A,e,t){var r=e.ownerDocument,n=r.documentElement?parseColor(A,getComputedStyle(r.documentElement).backgroundColor):COLORS.TRANSPARENT,B=r.body?parseColor(A,getComputedStyle(r.body).backgroundColor):COLORS.TRANSPARENT,t="string"==typeof t?parseColor(A,t):null===t?COLORS.TRANSPARENT:4294967295;return e===r.documentElement?isTransparent(n)?isTransparent(B)?t:B:n:t};export default html2canvas;