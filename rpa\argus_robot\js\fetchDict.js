const distListFNM = [
  {fieldDesc: '报告类型',fieldId: "DLIST_CSM_RPT_TYPE_ID", fieldName: 'TXT_report_type'},
  {fieldDesc: '自定义分类',fieldId: "DLIST_CSCCL_CLASSIFICATION_ID", fieldName: 'TXT_Class_Table_{index}_class_id'},
  {fieldDesc: '临床试验分期',fieldId: "DLIST_CSS_DEV_PHASE_ID", fieldName: 'TXT_dev_phase_id'},
  {fieldDesc: '职业',fieldId: "DLIST_CSRP_OCCUPATION_ID", fieldName: 'TXT_rep_occupation_id'},
  {fieldDesc: '报告类型',fieldId: "DLIST_CSRP_REPORTER_TYPE", fieldName: 'TXT_rep_type'},
  {fieldDesc: '单位',fieldId: "DLIST_CSPI_AGE_UNIT_ID", fieldName: 'TXT_pat_age_unit'},
  {fieldDesc: '年龄组',fieldId: "DLIST_CSPI_AGE_GROUP_ID", fieldName: 'TXT_pat_age_group'},
  {fieldDesc: '出生时性别',fieldId: "DLIST_CSPI_GENDER_ID", fieldName: 'TXT_pat_gender'},
  {fieldDesc: '现在性别',fieldId: "DLIST_CSPI_GENDER_CURRENT_ID", fieldName: 'TXT_pat_gender_current'},
  {fieldDesc: '职业',fieldId: "DLIST_CSPI_OCCUPATION_ID", fieldName: 'TXT_pat_occupation'},
  {fieldDesc: '单位（接种疫苗的年龄）',fieldId: "DLIST_CSPI_AGE_UNIT_ID_AT_VACC", fieldName: 'TXT_pat_age_unit_at_vacc'},
  {fieldDesc: '种族',fieldId: "DLIST_CSPI_ETHNIC_GROUP_ID", fieldName: 'TXT_pat_ethnic_group_id'},
  {fieldDesc: '兵役状态',fieldId: "DLIST_CSPI_MILITARY_STATUS_ID", fieldName: 'TXT_pat_military_status_id'},
  {fieldDesc: '人种信息',fieldId: "DLIST_CPR_ETHNICITY_ID", fieldName: 'TXT_CPR_{index}_ethnicity_id'},
  {fieldDesc: '病史-信息分类',fieldId: "DLIST_CSPH_CONDITION_TYPE_ID", fieldName: 'TXT_Rel_Hist_Table_pat_hist_type'},
  {fieldDesc: '实验室检查-单位',fieldId: "DLIST_CSLD_UNIT_ID", fieldName: 'TXT_labunit_0'},
  {fieldDesc: '实验室检查-检查结果评估',fieldId: "DLIST_CSLD_ASSESSMENT", fieldName: 'TXT_labassess'},
  {fieldDesc: '产品标识类型',fieldId: "DLIST_CSPD_IDENTIFIER_TYPE_ID", fieldName: 'TXT_identifier_type_id'},
  {fieldDesc: '生产商',fieldId: "DLIST_CSPD_MANUFACTURER_ID", fieldName: 'TXT_manufacturer_id'},
  {fieldDesc: '剂型',fieldId: "DLIST_CSPDG_FORMULATION_ID", fieldName: 'TXT_formulation_id'},
  {fieldDesc: '上市许可持有人',fieldId: "DLIST_CSPD_MKT_AUTH_HOLDER", fieldName: 'TXT_mkt_auth_holder_id'},
  {fieldDesc: '含量单位',fieldId: "DLIST_CSPDG_CONC_UNITS_ID", fieldName: 'TXT_conc_units_id'},
  {fieldDesc: '有效成分名称',fieldId: "DLIST_CSPRI_INGREDIENT_ID", fieldName: 'TXT_IngredientTable_{index}_ingredient_id'},
  {fieldDesc: '成分信息-单位',fieldId: "DLIST_CSPRI_CONC_UNIT_ID", fieldName: 'TXT_IngredientTable_{index}_conc_unit_id'},
  {fieldDesc: '产品名称关联信息-关联类型',fieldId: "DLIST_CSPNP_NAME_PART_TYPE", fieldName: 'TXT_PartNameTable_{index}_name_part_type'},
  {fieldDesc: '给药方案-单次用药单位',fieldId: "DLIST_CSDR_DOSE_UNIT_ID", fieldName: 'TXT_cdr_dose_unit_id'},
  {fieldDesc: '给药方案-频率',fieldId: "DLIST_CSDR_FREQ_ID", fieldName: 'TXT_freq_id'},
  {fieldDesc: '给药方案-单日用药单位',fieldId: "DLIST_CSDR_DAILY_DOSE_UNIT_ID", fieldName: 'TXT_daily_dose_unit_id'},
  {fieldDesc: '给药方案-总剂量单位',fieldId: "DLIST_CSDR_TOT_REG_DOSE_UNIT_ID", fieldName: 'TXT_tot_reg_dose_unit_id'},
  {fieldDesc: '给药方案-患者给药途径',fieldId: "DLIST_CSDR_ADMIN_ROUTE_ID", fieldName: 'TXT_cdr_admin_route_id'},
  {fieldDesc: '给药方案-给药途径（父母）',fieldId: "DLIST_CSDR_ADMIN_ROUTE_ID", fieldName: 'TXT_par_admin_route'},
  {fieldDesc: '用药方案-意外暴露类别',fieldId: "DLIST_CSDR_ACCIDENTAL_EXPOSURE", fieldName: 'TXT_accidental_exposure'},
  {fieldDesc: '产品使用详细信息-单位',fieldId: "DLIST_CSPDG_TOT_DOSE_UNIT_ID", fieldName: 'TXT_tot_dose_unit_id'},
  {fieldDesc: '产品使用详细信息-单位',fieldId: "DLIST_CSPDG_CUMULATIVE_DOSE_UNIT_ID", fieldName: 'TXT_cumulative_dose_unit_id'},
  {fieldDesc: '产品使用详细信息-单位',fieldId: "DLIST_CSPDG_EXPOSURE_UNIT_ID", fieldName: 'TXT_exposure_unit_id'},
  {fieldDesc: '对药物采取措施',fieldId: "DLIST_CSPDG_ACT_TAKEN_ID", fieldName: 'TXT_act_taken_id'},
  {fieldDesc: '专门产品类别',fieldId: "DLIST_CSPDG_FDA_SPC_ID", fieldName: 'TXT_fda_spc_id'},
  {fieldDesc: '强度（轻重度）',fieldId: "DLIST_CSE_EVT_INTENSITY_ID", fieldName: 'TXT_evt_intensity_id'},
  {fieldDesc: '频率',fieldId: "DLIST_CSE_EVT_FREQ_ID", fieldName: 'TXT_evt_freq_id'},
  {fieldDesc: '事件转因',fieldId: "DLIST_CSE_EVT_OUTCOME_ID", fieldName: 'TXT_evt_outcome_id'},
  {fieldDesc: '报告来源的因果关系',fieldId: "DLIST_CSEA_PRT_SOURCE_ID", fieldName: 'assessmentSource'},
  {fieldDesc: '方法',fieldId: "DLIST_CSEA_PRT_METHOD_ID", fieldName: 'method_id'},
  {fieldDesc: '结果',fieldId: "DLIST_CSEA_PRT_CAUSALITY_ID", fieldName: 'assessmentResult'},
  {fieldDesc: '采取的措施',fieldId: "DLIST_CSED_ACT_TAKEN_ID", fieldName: 'TXT_act_taken_id'},
  {fieldDesc: '单位',fieldId: "DLIST_CSED_TOTAL_DOSE_UNIT_ID", fieldName: 'TXT_total_dose_unit_id'},
  {fieldDesc: '工作分类',fieldId: "DLIST_CSCLG_CODE", fieldName: 'TXT_TheContactLog_{index}_contact_code'},
  {fieldDesc: '负责组',fieldId: "DLIST_CSCLG_GROUP_ID", fieldName: 'TXT_TheContactLog_{index}_contact_group_id'},
  {fieldDesc: '负责人',fieldId: "DLIST_CSCLG_USER_ID", fieldName: 'TXT_TheContactLog_{index}_contact_user_id'},
  {fieldDesc: '附件-信息分类',fieldId: "DLIST_CSNAT_CLASSIFICATION", fieldName: 'TXT_TableNotesAttach_{index}_classification'},
  {fieldDesc: '参考信息-信息分类',fieldId: "DLIST_CSR_REF_TYPE_ID", fieldName: 'TXT_TableReference_{index}_ref_type_id'},
  {fieldDesc: 'meddra版本',fieldId: "MEDDRA_VERSION", fieldName: 'meddra_version'},
];
const origin = window.location.origin;
// 基础URL
const baseUrl = origin + "/Controls/Utilities/AjaxTypeAheadFindValues.asp";

// 解析XML响应，提取OPTION元素的id和值
function parseXMLResponse(xmlString, dsplyLng) {
  const parser = new DOMParser();
  const xmlDoc = parser.parseFromString(xmlString, "text/xml");
  const options = xmlDoc.getElementsByTagName("OPTION");

  const result = [];
  for (let i = 0; i < options.length; i++) {
    const option = options[i];
    result.push({
      standardCode: option.getAttribute("id"),
      standardNameCn: dsplyLng == 1 ? option.textContent : '',
      standardNameEn: dsplyLng == 0 ? option.textContent : '',
    });
  }

  return result;
}

// 请求单个FNM的数据
async function fetchFNMData(fnm, dsplyLng, OCATOK, MedDRAData) {
  // DSPLYLNG 1 中文 0 英文 
  const url = `${baseUrl}?FNM=${fnm.fieldId}&FV=&DSPLYLNG=${dsplyLng}&DBCLICK=1&OCATOK=${OCATOK}`;
  let options = [];
  try {
    if(fnm.fieldId === 'MEDDRA_VERSION'){
      options = MedDRAData
    }else {
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const xmlString = await response.text();
      options = parseXMLResponse(xmlString, dsplyLng);
    }
    return {
      "fieldName": fnm.fieldName,
      "fieldId": fnm.fieldId,
      "fieldDesc": fnm.fieldDesc,
      "standardItems": options,
    };
    
  } catch (error) {
    console.error(`Error fetching data for: ${fnm.fieldId}`, error);
  }
}

function mergeCnEnArrays(chineseArray, englishArray) {
    const result = [];
    
    // 确保两个数组长度相同
    if (chineseArray.length !== englishArray.length) {
      console.error('中英文数组长度不一致!');
      return [];
    }
    
    // 遍历每个字段组
    for (let i = 0; i < chineseArray.length; i++) {
      const chineseObj = chineseArray[i];
      const englishObj = englishArray.find(item => item.fieldId === chineseObj.fieldId);
    //   const mergedGroup = [];
      
      // 确保每个组中只有一个对象
      if (!chineseObj || !englishObj) {
        console.error(`对象不存在`);
        continue;
      }
      
      // 创建新对象，保留中文字段信息
      const mergedObj = {
        fieldName: chineseObj.fieldName,
        fieldId: chineseObj.fieldId,
        fieldDesc: chineseObj.fieldDesc,
        standardItems: []
      };
      
      // 创建一个映射来快速查找英文项目
      const englishMap = {};
      englishObj.standardItems.forEach(item => {
        englishMap[item.standardCode] = item.standardNameEn;
      });
      
      // 合并standardItems
      chineseObj.standardItems.forEach(chItem => {
        mergedObj.standardItems.push({
          standardCode: chItem.standardCode,
          standardNameCn: chItem.standardNameCn,
          standardNameEn: englishMap[chItem.standardCode] || ''
        });
      });
      
    //   mergedGroup.push(mergedObj);
      result.push(mergedObj);
    }
    
    return result;
  }

// 主函数：获取所有FNM的数据
async function fetchAllFNMData(MedDRAData, OCATOK) {
  // 使用Promise.all并行请求所有数据
  // 获取中文的所有字典
  const resultsCn = await Promise.all(
    distListFNM.map((fnm) => fetchFNMData(fnm, 1, OCATOK, MedDRAData))
  );
  // 获取英文的所有字典
  const resultsEn = await Promise.all(
    distListFNM.map((fnm) => fetchFNMData(fnm, 0, OCATOK, MedDRAData))
  );
  // 合并中文和英文的字典
  const resultDist = mergeCnEnArrays(resultsCn.filter(item => item), resultsEn.filter(item => item))

  return resultDist;
}

window.addEventListener("message", async function (event) {
  if (event.data.type === "fetchDictExec") {    
    console.log('字典采集');

    let data = null;
    if(event.data.env === 'uat'){
      const agForm = document.getElementById('fm_TopHidden')?.contentDocument?.getElementById('agForm');
      const actionData = agForm?.getAttribute('action')
      const OCATOK = actionData?.split('OCATOK=')?.[1] || '';
      console.log(OCATOK);
      data = await fetchAllFNMData(event.data.MedDRAData, OCATOK)
    }else {
      data = await fetchAllFNMData(event.data.MedDRAData)
    }
    
    // 传递保存保存结果
    window.postMessage(
      { type: "fetchDictData", data: data },
      window.location.origin
    );
  }
});
