// 显示账号密码列表
function displayCredentials(credentials) {
  // console.log("credentials", credentials);

  const credentialsList = document.getElementById("credentials-list");
  credentialsList.innerHTML = "";
  // const moreOptions = document.getElementById("more-options");

  credentialsList.style.display = "block";
  // moreOptions.style.display = "block";
  // if (credentials.length < 3) {
  //   moreOptions.style.display = "none";
  // }
  credentials.forEach((credential, index) => {
    const item = document.createElement("div");
    item.id = `credential-item-${index}`;
    item.classList.add("credential-item");
    item.innerHTML = `
            <div class="icon-lock">
                <svg t="1739256358995" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4188" width="15" height="15"><path d="M910.222222 1024 113.777778 1024C50.915556 1024 0 973.084444 0 910.222222L0 455.111111C0 392.248889 50.915556 341.333333 113.777778 341.333333L230.229333 341.333333C227.214222 323.128889 227.555556 303.445333 227.555556 284.444444 227.555556 127.374222 354.929778 0 512 0 669.070222 0 796.444444 127.374222 796.444444 284.444444L739.555556 284.444444C739.555556 158.776889 637.667556 56.888889 512 56.888889 386.332444 56.888889 284.444444 158.776889 284.444444 284.444444 284.444444 304.184889 287.573333 323.072 292.352 341.333333L910.222222 341.333333C973.084444 341.333333 1024 392.248889 1024 455.111111L1024 910.222222C1024 973.084444 973.084444 1024 910.222222 1024ZM967.111111 455.111111C967.111111 423.708444 941.624889 398.222222 910.222222 398.222222L113.777778 398.222222C82.375111 398.222222 56.888889 423.708444 56.888889 455.111111L56.888889 910.222222C56.888889 941.624889 82.375111 967.111111 113.777778 967.111111L910.222222 967.111111C941.624889 967.111111 967.111111 941.624889 967.111111 910.222222L967.111111 455.111111ZM512 739.555556C480.597333 739.555556 455.111111 714.069333 455.111111 682.666667 455.111111 651.264 480.597333 625.777778 512 625.777778 543.402667 625.777778 568.888889 651.264 568.888889 682.666667 568.888889 714.069333 543.402667 739.555556 512 739.555556Z" fill="#13227a" p-id="4189"></path></svg>
            </div>
            <div class="username-item">
                <span class="name">${credential.name}</span>
                <span class="username">${credential.username}</span>
            </div>
            <div class="icon-edit" id="edit-button-${index}">
                <svg t="1739256424750" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5320" width="15" height="15"><path d="M512.7 908.2c0 11-9 20-20 20H117.8c-11 0-20-9-20-20v-7.1c0-11 9-20 20-20h374.9c11 0 20 9 20 20v7.1zM382.4 773.7c-7.8 7.8-20.5 7.8-28.3 0l-148-148c-7.8-7.8-7.8-20.5 0-28.3l5.1-5.1c7.8-7.8 20.5-7.8 28.3 0l148 148c7.8 7.8 7.8 20.5 0 28.3l-5.1 5.1z" fill="#13227a" p-id="5321"></path><path d="M527.1 264.6c-7.7-7.8-20.5-7.9-28.3-0.1L183.3 577.3c-7.8 7.7-16.3 22.8-18.9 33.5l-59.5 245c-2.6 10.7 4.1 17.5 14.8 15.1l248.5-55c10.7-2.4 25.9-10.7 33.7-18.4l314.6-313c7.8-7.8 7.8-20.5 0.1-28.3L527.1 264.6zM369.9 757.1c-7.9 7.7-16.2 14.5-18.4 15.1-2.3 0.6-12.9 3.2-23.6 5.7l-147.4 34c-10.7 2.5-17.3-4.2-14.7-14.9L202 648.5c2.6-10.7 5.2-21.1 5.7-23 0.5-2 7.3-10 15-17.8l273-275.1c7.7-7.8 20.4-7.8 28.1 0L647.7 458c7.7 7.8 7.6 20.5-0.3 28.2L369.9 757.1zM678.9 110.7c-7.8-7.8-20.5-7.8-28.2 0l-107 107.7c-7.8 7.8-7.7 20.6 0.1 28.3l192.1 192.1c7.8 7.8 20.5 7.8 28.2 0L871 331.1c7.8-7.8 7.7-20.6-0.1-28.3l-192-192.1zM605 246.2c-7.8-7.8-7.9-20.6-0.3-28.6l43.4-45.4c7.6-8 20.2-8.1 28-0.4l135.1 134.4c7.8 7.8 7.8 20.5 0.1 28.3l-44.5 44.7c-7.8 7.8-20.5 7.8-28.3 0.1L605 246.2z" fill="#1296db" p-id="5322"></path></svg>
            </div>
            `;
    credentialsList.appendChild(item);

    // 为编辑按钮添加点击事件监听器
    const editButton = document.getElementById(`edit-button-${index}`);
    editButton.addEventListener("click", (e) => {
      e.stopPropagation();
      openEditPopup(credentials, credential, index);
      credentialsList.style.display = "none";
      // moreOptions.style.display = "none";
      window.parent.postMessage({
        action: "setCredentialValue",
        data: "edit",
      }, '*'); 
    });

    // 为编辑按钮添加点击事件监听器
    const credentialItem = document.getElementById(`credential-item-${index}`);
    credentialItem.addEventListener("click", () => {
      // 传递在content.js中，关闭用户名选择框
      window.parent.postMessage({
        action: "setCredentialValue",
        data: credential,
      }, '*');  
    });
  });
}

// 打开编辑弹窗
function openEditPopup(credentials, credential, index) {
  const editCredentialPop = document.getElementById("edit-credential-option");
  editCredentialPop.style.display = "block";
  document.getElementById("url").value = credential.url || "";
  document.getElementById("name").value = credential.name || "";
  document.getElementById("folder").value = credential.folder || "";
  document.getElementById("username").value = credential.username || "";
  document.getElementById("password").value = credential.password || "";
  document.getElementById("notes").value = credential.notes || "";

  // 保存按钮点击事件
  const saveButton = document.getElementById("saveButton");
  saveButton.addEventListener("click", () => {
    const newUsername = document.getElementById("username").value;
    const newPassword = document.getElementById("password").value;
    const newUrl = document.getElementById("url").value;
    const newName = document.getElementById("name").value;
    const newFolder = document.getElementById("folder").value;
    const newNotes = document.getElementById("notes").value;
    const newData = {
      ...credential,
      url: newUrl,
      name: newName,
      folder: newFolder,
      username: newUsername,
      password: newPassword,
      notes: newNotes,
      updateTime: new Date().getTime(),
    };
    credentials[index] = newData;
    
    window.parent.postMessage({
      action: "setCredentialValue",
      data: credentials.sort(
        (a, b) => new Date(b.updateTime) - new Date(a.updateTime)
      ),
    }, '*');   
    restoreCredentialPop(credentials);
  });

  // 取消按钮点击事件
  const cancelButton = document.getElementById("cancelButton");
  cancelButton.addEventListener("click", () =>
    restoreCredentialPop(credentials)
  );
}

function restoreCredentialPop(credentials) {
  const editCredentialPop = document.getElementById("edit-credential-option");
  editCredentialPop.style.display = "none";
  displayCredentials(credentials);
  // 传递在content.js中，关闭用户名选择框
  window.parent.postMessage({
    action: "restoreCredentialPop",
  }, '*');  
}

window.addEventListener("DOMContentLoaded", () => {
  const passwordInput = document.getElementById("password");
  const toggleButton = document.querySelector("#toggle-password");
  passwordInput.addEventListener("input", () => {
    toggleButton.style.visibility = passwordInput.value ? "visible" : "hidden";
  });

  toggleButton.addEventListener("click", () => {
    if (passwordInput.type === "password") {
      passwordInput.type = "text";
      toggleButton.innerHTML = `<svg t="1739345472722" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="10288" width="15" height="15"><path d="M520.3968 241.2544C222.4128 228.1472 4.7104 541.2864 4.7104 541.2864c201.5232 309.8624 480.8704 301.4656 480.8704 301.4656 346.9312 9.4208 524.4928-301.4656 524.4928-301.4656-26.4192-48.3328-191.************-489.6768-300.032z m-12.288 478.0032c-97.4848 0-176.5376-79.0528-176.5376-176.5376s79.0528-176.5376 176.5376-176.5376 176.5376 79.0528 176.5376 176.5376-79.0528 176.5376-176.5376 176.5376z" p-id="10289" fill="#515151"></path><path d="M507.2896 544.768m-100.352 0a100.352 100.352 0 1 0 200.704 0 100.352 100.352 0 1 0-200.704 0Z" p-id="10290" fill="#515151"></path></svg>`;
    } else {
      passwordInput.type = "password";
      toggleButton.innerHTML = `<svg t="1739345513275" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="10489" width="15" height="15"><path d="M512 298.666667c117.76 0 213.333333 95.573333 213.333333 213.333333 0 27.52-5.546667 53.76-15.146666 77.866667l124.8 124.8c64.426667-53.76 115.2-123.306667 146.56-202.666667-74.026667-187.306667-256-320-469.546667-320-59.733333 0-116.906667 10.666667-170.026667 29.866667l92.16 91.946666c24.106667-9.386667 50.346667-15.146667 77.866667-15.146666zM85.333333 182.4l97.28 97.28 19.413334 19.413333C131.626667 354.133333 75.946667 427.306667 42.666667 512c73.813333 187.306667 256 320 469.333333 320 66.133333 0 129.28-12.8 187.093333-36.053333l18.133334 18.133333L841.6 938.666667 896 884.48 139.733333 128 85.333333 182.4zM321.28 418.133333l65.92 65.92c-1.92 9.173333-3.2 18.346667-3.2 27.946667 0 70.613333 57.386667 128 128 128 9.6 0 18.773333-1.28 27.733333-3.2l65.92 65.92C577.28 716.8 545.706667 725.333333 512 725.333333c-117.76 0-213.333333-95.573333-213.333333-213.333333 0-33.706667 8.533333-65.28 22.613333-93.866667z m183.68-33.493333l134.4 134.4L640 512c0-70.613333-57.386667-128-128-128l-7.04 0.64z" fill="#515151" p-id="10490"></path></svg>`;
    }
  });
  // 使用 window.addEventListener('message') 来接收消息
  window.addEventListener("message", function (event) {
    if (event.data.action === "getAllCredentials") {
      const data = event.data.data;
      displayCredentials(data);
    }
  });
});
