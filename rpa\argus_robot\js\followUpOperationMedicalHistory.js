function collectSelectedRelHistData() {
    const mainAppId = 'fm_MainApp';
    const mainFrameId = 'fm_MainFrame';
    // 获取表格元素
    const table = document.getElementById(mainAppId).contentDocument.getElementById(mainFrameId).contentDocument.querySelector("#Rel_Hist_Table");
    if (!table) {
        console.error("无法找到ID为 'Rel_Hist_Table' 的表格元素。");
        return;
    }

    // 获取所有数据行，排除表头
    const rows = table.querySelectorAll("tr[id^='Rel_Hist_Table_']");

    // 初始化一个空数组，用于存储每一行的数据对象
    const selectedData = [];

    // 定义需要采集的字段列表及其对应的ID模板
    const fieldsMapping = {
        "pat_hist_cont": "Rel_Hist_Table_{index}_pat_hist_cont",
        "pat_hist_rptd": "Rel_Hist_Table_{index}_pat_hist_rptd",
        "pat_hist_start": "Rel_Hist_Table_{index}_pat_hist_start",
        "pat_hist_stop": "Rel_Hist_Table_{index}_pat_hist_stop",
        "pat_hist_notes": "Rel_Hist_Table_{index}_pat_hist_notes",
        "pat_hist_type": "TXT_Rel_Hist_Table_{index}_pat_hist_type",
        "pat_hist_llt_code": "Rel_Hist_Table_{index}_pat_hist_llt_code",
        "pat_hist_llt": "Rel_Hist_Table_{index}_pat_hist_llt"
    };

    // 定义需要进行占位符清理的字段
    const fieldsToSanitize = ["pat_hist_start", "pat_hist_stop"];

    // 遍历每一行
    rows.forEach((row, rowIndex) => {
        const rowData = {};

        // 遍历需要采集的字段
        for (const [key, idTemplate] of Object.entries(fieldsMapping)) {
            // 替换索引以构建实际的ID
            const fieldId = idTemplate.replace("{index}", rowIndex);

            // 尝试选择对应的元素（input、select、textarea）
            let element = row.querySelector(`#${fieldId}`);
            if (!element) {
                console.warn(`在第 ${rowIndex + 1} 行中，无法找到ID为 '${fieldId}' 的元素。`);
                rowData[key] = null; // 设置为null以表示缺失
                continue;
            }

            // 根据元素类型提取值
            const tagName = element.tagName.toLowerCase();
            if (tagName === 'input') {
                if (element.type === 'checkbox') {
                    rowData[key] = element.checked;
                } else {
                    rowData[key] = element.value.trim();
                }
            } else if (tagName === 'select') {
                rowData[key] = element.value;
            } else if (tagName === 'textarea') {
                rowData[key] = element.value.trim();
            } else {
                rowData[key] = element.textContent.trim();
            }

            // 如果字段需要清理占位符，则进行处理
            if (fieldsToSanitize.includes(key)) {
                rowData[key] = sanitizeValue(element.value);
            } else {
                rowData[key] = element.value;
            }
        }

        // 处理 "pat_hist_rptd_lltname" 字段
        // 组合 "pat_hist_llt_code" 和 "pat_hist_llt" 为 "pat_hist_rptd_lltname"
        const lltCode = rowData["pat_hist_llt_code"] || "未知编码";
        const lltName = rowData["pat_hist_llt"] || "未知名称";
        const lltNameFormatted = `${lltCode}(${lltName})`;

        // 构建最终的字段名，包括行索引
        const rptdLltNameKey = `Rel_Hist_Table_${rowIndex}_pat_hist_rptd_lltname`;
        rowData[rptdLltNameKey] = lltNameFormatted;

        // 构建最终的对象，只保留所需的字段
        const finalRowData = {
            [rptdLltNameKey]: rowData[rptdLltNameKey],
            [`Rel_Hist_Table_${rowIndex}_pat_hist_cont`]: rowData["pat_hist_cont"],
            [`Rel_Hist_Table_${rowIndex}_pat_hist_rptd`]: rowData["pat_hist_rptd"],
            [`Rel_Hist_Table_${rowIndex}_pat_hist_start`]: rowData["pat_hist_start"],
            [`Rel_Hist_Table_${rowIndex}_pat_hist_stop`]: rowData["pat_hist_stop"],
            [`Rel_Hist_Table_${rowIndex}_pat_hist_notes`]: rowData["pat_hist_notes"],
            [`TXT_Rel_Hist_Table_${rowIndex}_pat_hist_type`]: rowData["pat_hist_type"]
        };

        // 将当前行的数据对象添加到selectedData数组中
        selectedData.push(finalRowData);
    });

    // 输出最终的数据数组
    return selectedData;
}

// 用于清理字段值的函数
function sanitizeValue(value) {
    if (value === 'YYYY/??/??' || value.trim() === '') {
        return ''; // 将默认占位符或空值转换为 null
    }
    return value.trim();
}
window.postMessage({ type: 'followUpOperationMedicalHistory', data: collectSelectedRelHistData() }, window.location.origin);