const { 
  wait,
  moduleLogs,
  getDocument,
  timer,
} = await import(`./rpa-util.js?v=${window.__RPA_VERSION}`);

const {
  rpaClient,getBaseUrl
} = await import(`./rpa-client.js?v=${window.__RPA_VERSION}`);

async function getLabXml() {
  const reb = await window.fm_MainApp.fm_MainFrame.fn_labdata_submit();
  if (reb === -1) {
    throw new Error("实验室检查数据校验失败");
  }
  const form = window.fm_MainApp.fm_MainFrame.form;
  const formData = new FormData(form);
  const xml = formData.get("labdataxml1");
  return xml;
}

export function setMainFrameForm(params) {
  const form = window.fm_MainApp.fm_MainFrame.form;
  const submit = form.submit;

  form.submit = () => {
    const formData = new FormData(form); 
    for (const key in params) {
      if (params[key] !== undefined) {
        formData.set(key, params[key]);
      }
    }
    // formData.set("nexturl", "/CaseForm/Patient/CF_Patient.asp"); 
    const oHtml = form.innerHTML;
    form.innerHTML = "";

    formData.forEach((value, key) => {
      const input = document.createElement("input");
      input.type = "hidden";
      input.name = key;
      input.value = value;
      form.appendChild(input);
    });
    try {
      return submit.call(form);
    } catch (e) {
      form.innerHTML = oHtml;
      console.error(e);
    }
  };
} 

// 更新XML中的CLD_COMMENTS，添加唯一索引（只用于初始建立映射）
function updateXmlWithIndices(xmlData) {
  const parser = new DOMParser();
  const xmlDoc = parser.parseFromString(xmlData, "text/xml");
  // 尝试查找LABTESTS节点，这是根据日志显示的实际节点名
  let labTestNodes = Array.from(xmlDoc.querySelectorAll("LABTESTS"));
  if (labTestNodes.length === 0) {
    // 如果没找到LABTESTS，尝试查找CASE_LAB_DATA
    labTestNodes = Array.from(xmlDoc.querySelectorAll("CASE_LAB_DATA"));
  }

  console.log(
    `找到 ${labTestNodes.length} 个${
      labTestNodes.length > 0
        ? xmlDoc.querySelector("LABTESTS")
          ? "LABTESTS"
          : "CASE_LAB_DATA"
        : ""
    }节点`
  );

  // 为每个节点下的CLD_COMMENTS添加索引
  labTestNodes.forEach((node, index) => {
    const commentsNode = node.querySelector("CLD_COMMENTS");
    if (commentsNode) {
      // 使用索引作为唯一标识
      commentsNode.textContent = index.toString(); 
    } else { 
      // 如果不存在则创建一个
      const newCommentsNode = xmlDoc.createElement("CLD_COMMENTS");
      newCommentsNode.textContent = index.toString();
      node.appendChild(newCommentsNode); 
    }
  });
  // 将更新后的XML转换回字符串
  const serializer = new XMLSerializer();
  return serializer.serializeToString(xmlDoc);
}

// 只更新XML中的序号，不改变其他内容
function updateSequenceNumbers(xmlData, positionToSeqNumMap) {
    const parser = new DOMParser();
    const xmlDoc = parser.parseFromString(xmlData, 'text/xml');
    const seqnumNodes = xmlDoc.querySelectorAll('CLD_SEQNUM');

    // 直接按照位置索引更新序号
    Object.entries(positionToSeqNumMap).forEach(([position, seqnum]) => {
        const pos = parseInt(position);
        if (!isNaN(pos) && pos < seqnumNodes.length) {
            seqnumNodes[pos].textContent = seqnum;
            // console.log(`更新序号: XML位置${pos} -> ${seqnum}`);
        } else {
            console.log(`警告: 位置${pos}超出范围或无效`);
        }
    });

    // 将更新后的XML转换回字符串
    const serializer = new XMLSerializer();
    return serializer.serializeToString(xmlDoc);
}

function removeEmptyLab(xml){
  const parser = new DOMParser();
  const xmlDoc = parser.parseFromString(xml, "text/xml");
  
  const labTestNodes = Array.from(xmlDoc.querySelectorAll("CASE_LAB_DATA"));
  labTestNodes.forEach((node, index) => {
    const CLD = node.querySelector("CLD_PT");
    if (!CLD || !CLD.textContent || !CLD.textContent.trim()) {
      if (node.parentNode) {
        node.parentNode.removeChild(node);
      }
    }  
  });
 
  // 将更新后的XML转换回字符串
  const serializer = new XMLSerializer();
  return serializer.serializeToString(xmlDoc);

}
const LAB_MAX_COUNT = 101
function makeLabsFieldsData(xml){
  const parser = new DOMParser();
  const xmlDoc = parser.parseFromString(xml, "text/xml");  
  const labTestNodes = Array.from(xmlDoc.querySelectorAll("CASE_LAB_DATA"));
  if(labTestNodes.length < LAB_MAX_COUNT){
    return {
      labdataxml1: xml,
    }
  }
  
  const count = Math.ceil((labTestNodes.length + 1)  / LAB_MAX_COUNT)
  const fields = {}
  for(let i = 0; count > i; i++){ 
    const start = i * LAB_MAX_COUNT;
    const end = start + LAB_MAX_COUNT;
    const labs = labTestNodes.slice(start, end);
    const newXml = labs.map(lab => lab.outerHTML).join('')   
    const xml = newXml.replace(/>\s+</g, '><');
    fields[`labdataxml${i+1}`] = i === 0 ? '<TABLE_CASE_LAB_DATA>' + xml : xml
    if(i === count - 1){
      fields[`labdataxml${i+1}`] = (fields[`labdataxml${i+1}`] || '') + '</TABLE_CASE_LAB_DATA>'
    }
  }  
  return fields;
} 

async function fetchSequenceAndComments(lang = "cn", xmlData) {  

  // 使用DOM查询获取所有匹配的序号
  const chkElements = window.fm_MainApp.fm_MainFrame.document.querySelectorAll('[id^="ChkOldValue_"]');
  let sequenceNumbers = Array.from(chkElements).map((element) => {
    return element.id.replace("ChkOldValue_", "");
  });

  console.log(`语言: ${lang}: 找到的序号列表:`, sequenceNumbers); 

  // 检查是否获取到足够的序号且全不为-1
  if (
    sequenceNumbers.length > 0 &&
    !sequenceNumbers.some((num) => num === "-1")
  ) {
    console.log(`语言: ${lang}: 成功获取到序号且全不为-1`);

    // 直接创建位置到序号的映射
    const positionToSeqNumMap = {};
    for (let i = 0; i < sequenceNumbers.length; i++) {
      positionToSeqNumMap[i] = sequenceNumbers[i];
      // console.log(`位置映射: 位置${i} -> 序号${sequenceNumbers[i]}`);
    }

    console.log(`语言: ${lang}: 最终的位置到序号的映射:`, positionToSeqNumMap);

    return {
      sequenceNumbers: sequenceNumbers,
      positionToSeqNumMap: positionToSeqNumMap,
      xmlData: xmlData,
    };
  } else {
    throw new Error(`语言: ${lang}: 未获取到足够的序号或存在-1值`);
  }
}



async function writeLab(lang = 'cn', positionToSeqNumMap = null, xmlData = null, extData = {}) {
  console.log(`开始执行 writeLab (${lang})...`); 
  // 设置DSPLYLNG参数：中文为1，英文为0
  const dsplylngValue = lang === "cn" ? "1" : "0"; 

  // 基础URL
  const baseUrl = '/CaseForm/Patient/CF_Patient.asp';

  const params = new URLSearchParams(window.fm_MainApp.fm_MainFrame.location.search)
  if(params.get("DSPLYLNG") != dsplylngValue) {
    params.set("DSPLYLNG", dsplylngValue); // 根据语言设置DSPLYLNG
    // 构建完整URL
    const url = `${baseUrl}?${params.toString()}`;
    window.fm_MainApp.fm_MainFrame.location.href = url;   
    await wait(200)
  }
  await waitDom('#addlabtest'); 

  // 如果有位置到序号的映射，仅更新序号
  if (positionToSeqNumMap && typeof positionToSeqNumMap === 'object') {
      xmlData = updateSequenceNumbers(xmlData, positionToSeqNumMap);
      console.log(`已用位置映射更新 ${lang} 版本的XML中的序号`);
  }
  await waitDom('#CF_Patient');
  await waitDom('#addlabtest'); 


  console.log('makeLabsFieldsData :>> ', makeLabsFieldsData(xmlData));

  setMainFrameForm({
    // labdataxml1: xmlData, // 设置XML数据
    ...makeLabsFieldsData(xmlData),
    ...extData,
  });  
  await clickDom('#td_CF_Tab_1'); 
  (await waitDom('#td_CF_REP_tag_1')); 
  await clickDom('#td_CF_Tab_2'); 
  await waitDom('#addlabtest'); 
}
 

async function waitDom(selector, t = 30000){
  return new Promise((resolve, reject) => {
    const startTime = Date.now();
    const checkExist = setInterval(() => { 
      let element = null
      try{
        element = window.fm_MainApp.fm_MainFrame.document.querySelector(selector);
      }catch(e){
        console.warn(`查询元素 ${selector} 时发生错误:`, e);
      }
      if (element && element.offsetParent !== null) { 
        clearInterval(checkExist);
        setTimeout(() => {          
          resolve(element);
        }, 500);
      } else if (Date.now() - startTime > t) {
        clearInterval(checkExist);
        reject(new Error(`等待元素 ${selector} 超时`));
      }
    }, 500);
  });
}

async function clickDom(selector, t = 30000){
  const element = await waitDom(selector, t);
  await element.click();
  console.log('click 点击 :>> ', selector);
  await wait(2000);
}

async function waitFrameLoaded(t = 30000){ 
  return new Promise((resolve, reject) => {
    const startTime = Date.now();
    const checkExist = setInterval(() => {  
      try {
        const iframe = window.fm_MainApp.fm_MainFrame;
        const doc = iframe?.document || {};
        
        if (doc.readyState === 'complete') {
          setTimeout(() => {          
            resolve();
          }, 500);
          clearInterval(checkExist);
        } else if (Date.now() - startTime > t) {
          clearInterval(checkExist);
          reject(new Error(`等待iframe 加载超时`));
        }
      } catch (e) {
        clearInterval(checkExist);
        console.log(e.stack);
        reject(new Error('跨域 iframe，无法判断加载状态'));
      }
    }, 500);
  });
}

async function setFieldValue(selector, value = '', t = 30000) {
  const element = await waitDom(selector, t);
  if (element) {
    if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
    } else if (element.tagName === 'SELECT') {
      element.value = value;
      // element.dispatchEvent(new Event('change', { bubbles: true }));
    } else {
      console.warn(`不支持的元素类型: ${element.tagName}`);
    }
  } else {
    throw new Error(`无法找到元素: ${selector}`);
  }
} 

 

// 统一的前置数据验证函数
function validateBeforeFill(methodName, instance) {
  const cnData = instance.newReportDataCn;
  const enData = instance.newReportDataEn;

  switch (methodName) {
    case 'fillBaseInfo': {
      const reportBaseInfo = cnData?.reportBaseInfo || enData?.reportBaseInfo;
      if (!reportBaseInfo?.base_xml) {
        throw new Error('通用无接口录入数据');
      }
      break;
    }

    case 'fillPatient': {
      const reportSubjectInfo = cnData?.reportSubjectInfo || enData?.reportSubjectInfo;
      if (!reportSubjectInfo?.base_xml) {
        throw new Error('患者无接口录入数据');
      }
      break;
    }

    case 'fillLabtest': {
      const refExamineTable = cnData?.reportSubjectInfo?.refExamineTable?.length > 0
        ? cnData?.reportSubjectInfo?.refExamineTable
        : enData?.reportSubjectInfo?.refExamineTable || [];
      if (refExamineTable.length === 0) {
        console.log('实验室检查无数据');
        return { shouldSkip: true };
      }

      const labXmlCn = cnData?.reportSubjectInfo?.refExamineTable_xml;
      const labXmlEn = enData?.reportSubjectInfo?.refExamineTable_xml;
      if ((!labXmlCn && !labXmlEn) || (labXmlCn && labXmlCn === '<TABLE_CASE_LAB_DATA></TABLE_CASE_LAB_DATA>')) {
        throw new Error('实验室检查数据转换失败');
      }
      break;
    }

    case 'fillHist': {
      const rel_hist_add = cnData?.reportSubjectInfo?.rel_hist_add?.length > 0
        ? cnData?.reportSubjectInfo?.rel_hist_add
        : enData?.reportSubjectInfo?.rel_hist_add || [];
      if (rel_hist_add.length === 0) {
        console.log('病史无数据');
        return { shouldSkip: true };
      }

      const hist_cn = cnData?.reportSubjectInfo?.rel_hist_add_xml ?
        JSON.parse(cnData.reportSubjectInfo.rel_hist_add_xml) : null;
      const hist_en = enData?.reportSubjectInfo?.rel_hist_add_xml ?
        JSON.parse(enData.reportSubjectInfo.rel_hist_add_xml) : null;
      if (!hist_cn && !hist_en) {
        throw new Error('病史无接口录入数据');
      }
      break;
    }

    case 'fillDrug': {
      const studyDrugs = (cnData || enData)?.drugInfo?.['试验用药'] || [];
      const otherDrugs = (cnData || enData)?.drugInfo?.['合并用药'] || [];
      if (studyDrugs.length === 0) {
        console.log('试验用药无接口录入数据');
        return { shouldSkip: true };
      }
      // 检查每个药物的必填字段
      for (const drug of [...studyDrugs,...otherDrugs]) {
        if (!drug.expDoseTable_xml || !drug.base_xml) {
          throw new Error('无药物接口录入数据');
        }
      }
      break;
    }

    case 'fillEvent': {
      const events = cnData?.reportSaeDetailInfo || enData?.reportSaeDetailInfo || [];
      if (events.length === 0) {
        console.log('事件无接口录入数据');
        return { shouldSkip: true };
      }
      // 检查每个事件的必填字段
      for (const event of events) {
        if (!event.event_xml) {
          throw new Error('无事件录入数据');
        }
      }
      break;
    }

    case 'fillSaeDescribe': {
      const saeDescribe = cnData?.saeDescribe || enData?.saeDescribe;
      if (!saeDescribe) {
        console.log('分析无接口录入数据');
        return { shouldSkip: true };
      }
      if (!saeDescribe.narrative_xml) {
        throw new Error('无分析录入数据');
      }
      break;
    }

    case 'fillMeasure': {
      const measure = cnData?.measure || enData?.measure;
      if (!measure || !measure?.workLog?.length) {
        console.log('措施无接口录入数据');
        return { shouldSkip: true };
      }
      if (!measure.workLog_xml) {
        throw new Error('无措施录入数据');
      }
      break;
    }

    default:
      throw new Error(`未知的方法名: ${methodName}`);
  }

  return { shouldSkip: false };
}

export class RpaFill {
  static instance = null;
  static getInstance() {
    if (!RpaFill.instance) {
      RpaFill.instance = new RpaFill();
    }
    return RpaFill.instance;
  }
  constructor() { 
    this.reportDataCn = null;
    this.reportDataEn = null;
    this.taskId = null;
    this.newReportDataCn = null;
    this.newReportDataEn = null;
    
    this.baseUrl = null;
    this.argusNum = null;
    
    this.fillLabtestStatus = false;
    this.fillHistStatus = false;
    this.fillDrugStatus = false;
    this.fillEventStatus = false;
    this.fillSaeDescribeStatus = false;
    this.fillMeasureStatus = false;
    this.fillBaseInfoStatus = false;
    this.fillPatientStatus = false;
  }
  async init(reportDataCn, reportDataEn, taskId) {
    this.reportDataCn = reportDataCn;
    this.reportDataEn = reportDataEn;
    this.taskId = taskId;

    this.fillLabtestStatus = false;
    this.fillHistStatus = false;
    this.fillDrugStatus = false;
    this.fillEventStatus = false;
    this.fillSaeDescribeStatus = false;
    this.fillMeasureStatus = false;
    this.fillBaseInfoStatus = false;
    this.fillPatientStatus = false;

    
    this.baseUrl = await getBaseUrl(); 
    this.argusNum = localStorage.getItem("projectNum");

    const response = await rpaClient.sendRequest("makeHttpRequest", {
      url: `${this.baseUrl}/kb-server/api/report/convert`,
      options: { method: "POST" },
      headers: {
        // accessUrl: location.origin + '/',
        // tenantName: localStorage.getItem("tenantName"),
        tenantId: localStorage.getItem("sysTenantId"),   
        appId: localStorage.getItem("sysAppId"),   
      },
      body: JSON.stringify({
        cnJsonData: reportDataCn ? JSON.stringify(reportDataCn) : null,
        enJsonData: reportDataEn ? JSON.stringify(reportDataEn): null,
        argusNum: this.argusNum,
      }),
    });
    if (!response.data) {
      throw new Error(response.error);
    }
    if (!response.data.data) {
      throw new Error(response.data.message);
    }
    console.log('report/convert :>> ', response.data.data);
    this.newReportDataCn = response.data.data?.cnJsonData;
    this.newReportDataEn = response.data.data?.enJsonData;

    return this;
  }
  async fill(){ 
    if (!this.reportDataCn && !this.reportDataEn) {
      throw new Error("请先初始化报告数据");
    }
    if (!this.taskId) {
      throw new Error("请先设置任务ID");
    }

    validateBeforeFill('fillBaseInfo', this)
    validateBeforeFill('fillPatient', this)
    validateBeforeFill('fillLabtest', this)
    validateBeforeFill('fillHist', this)
    validateBeforeFill('fillDrug', this)
    validateBeforeFill('fillEvent', this)
    validateBeforeFill('fillSaeDescribe', this)
    validateBeforeFill('fillMeasure', this) 
     
    await this.fillBaseInfo();    
    await this.fillPatient();
    await this.fillLabtest();
    await this.fillHist();
    await this.fillDrug();
    await this.fillEvent();
    await this.fillSaeDescribe();
    await this.fillMeasure();
    // throw new Error("请手动保存并提交报告。");
  }
  async fillLabtest() {
    // 前置数据验证
    const validation = validateBeforeFill('fillLabtest', this);
    if (validation.shouldSkip) return;

    const labXmlCn = this.newReportDataCn?.reportSubjectInfo?.refExamineTable_xml;
    const labXmlEn = this.newReportDataEn?.reportSubjectInfo?.refExamineTable_xml;
    const count1 = (this.newReportDataCn?.reportSubjectInfo || this.newReportDataEn?.reportSubjectInfo)?.refExamineTable?.length || 0

    timer.start();
     
    await clickDom('#td_CF_Tab_2');
    
    await clickDom('#td_CF_PATIENT_tag_1'); 
    await waitDom('#addlabtest');  
 

    // 处理中文XML
    const indexedCnXml = updateXmlWithIndices(labXmlCn || labXmlEn);

    // 第一次写入标记过的中文XML
    console.log("开始第一次写入...");
    await writeLab(labXmlCn ? 'cn' : 'en', null, indexedCnXml, {dsplylngValue: '1'}); 
    let fetchResult = await fetchSequenceAndComments(labXmlCn ? 'cn' : 'en', indexedCnXml); 
  
    if(labXmlCn){    
      // 使用位置映射更新中文XML的序号并写入
      console.log("使用位置映射更新中文序号...");
      await writeLab('cn', fetchResult.positionToSeqNumMap, labXmlCn);
    }

    if(labXmlEn){    
      // 使用位置映射更新英文XML的序号并写入
      console.log("使用位置映射更新英文序号...");
      await writeLab('en', fetchResult.positionToSeqNumMap, labXmlEn,  {dsplylngValue: '0'});
    }

    console.log("中英文操作成功完成");


    await moduleLogs(
      `检查-双语-${count1}`,
      (timer.getElapsedTime()),
      this.taskId
    );
     
    this.fillLabtestStatus = true;  
  }
  
  async fillHist() {
    // 前置数据验证
    const validation = validateBeforeFill('fillHist', this);
    if (validation.shouldSkip) return;

    const hist_cn = JSON.parse(this.newReportDataCn?.reportSubjectInfo?.rel_hist_add_xml || 'null');
    const hist_en = JSON.parse(this.newReportDataEn?.reportSubjectInfo?.rel_hist_add_xml || 'null');

    timer.start();
    await clickDom('#td_CF_Tab_2');
    
    await clickDom('#td_CF_PATIENT_tag_1'); 
    await waitDom('#addlabtest');  
 
    setMainFrameForm({
      ...(hist_cn || hist_en)
    });   
    
    await clickDom('#td_CF_Tab_1');   
    (await waitDom('#td_CF_REP_tag_1'));
    
    const count = (this.newReportDataCn?.reportSubjectInfo || this.newReportDataEn?.reportSubjectInfo)?.rel_hist_add?.length || 0

    await moduleLogs(
      `病史-双语-${count}`,
      (timer.getElapsedTime()),
      this.taskId
    );

    this.fillHistStatus = true;
  }

  async fillDrug() {
    // 前置数据验证
    const validation = validateBeforeFill('fillDrug', this);
    if (validation.shouldSkip) return;

    const studyDrugs = (this.newReportDataCn || this.newReportDataEn )?.drugInfo?.['试验用药'] || [];
    timer.start();

    await clickDom('#tr_CF_Tab_3');   
    await clickDom('#tr_CF_PRD_1');  
    await waitDom('#Product_Details_Table'); 


    for (let index = 0; index < studyDrugs.length; index++) {
      const drug = studyDrugs[index];  
      // 选试验用药
      if(/^\d+$/.test(drug.pat_exposure)){
        await wait(500);
        await setFieldValue('#pat_exposure',drug.pat_exposure);

        const pat_exposure = await waitDom('#pat_exposure');

        if(pat_exposure.value != drug.pat_exposure){
          const opt = [...pat_exposure.options].find(opt => opt.textContent.indexOf(drug.product_name) > -1);
          const msg = `试验药物 "${drug.product_name}", 编码“${drug.pat_exposure}”错误, ${opt ? `请修改为正确编码“${opt.value}”`: '请联系管理员'}`
          console.log(msg);
          throw new Error(msg);
        }
        try{        
          await window.fm_MainApp.fm_MainFrame.fn_PatExposure();
        }catch(e){
          console.log('fn_PatExposure', e.message || e);
        }
        await waitDom('#Product_Details_Table'); 
      }


      
      const data = {
        regimen_info: drug.expDoseTable_xml || '',
        ...(drug.base_xml || {}),
        ...(drug.btnAddInd_xml || {}),
        ...(drug.product_xml || {}),
      }
      await waitDom('#Form1');
      await waitDom('#Product_Details_Header_Table');
      setMainFrameForm(data);   
       
      // 触发保存 
      await clickDom('#td_CF_Tab_1'); 
      (await waitDom('#td_CF_REP_tag_1'));
      // 跳回来
      await clickDom('#tr_CF_Tab_3');  
      await waitDom('#Product_Details_Table');       
      await waitDom('#Form1');
      await wait(200);
      try{        
        // 触发联动
        await window.fm_MainApp.fm_MainFrame.fn_Update_Regimen_Unit();
      }catch(e){
        console.log('fn_Update_Regimen_Unit', e.message || e);
      }
      const islast = index === studyDrugs.length - 1;
      if(!islast){
        // 新增药物
        await window.fm_MainApp.fm_MainFrame.fn_Study_Drug_Add();
        await waitFrameLoaded();
        await waitDom('#Product_Details_Table'); 
        await waitDom('#Form1');
      }
    }
    const combineDrugs = (this.newReportDataCn || this.newReportDataEn )?.drugInfo?.['合并用药'] || [];

    if(combineDrugs.length > 0) {
      // 新增药物 
      const els = window.fm_MainApp.fm_MainFrame.document.querySelectorAll('#tr_CF_PRD > td:not([id="td_CF_PRD_[[x]]"])');
      
      await clickDom('#'+ els[els.length - 1].id);
      await waitFrameLoaded();
      await waitDom('#Product_Details_Table');  
    }

    for (let index = 0; index < combineDrugs.length; index++) {
      const drug = combineDrugs[index];
      


      const data = {
        regimen_info: drug.expDoseTable_xml || '',
        ...(drug.base_xml || {}),
        ...(drug.btnAddInd_xml || {}),
        ...(drug.product_xml || {}),
      }
      await waitDom('#Form1');
      await waitDom('#Product_Details_Header_Table');
      setMainFrameForm(data);     

      (await waitDom('#product_name')).value = drug.product_name || '';
      
      (await waitDom('#drug_type_1')).checked = true;
      // 触发名称变化
      // await window.fm_MainApp.fm_MainFrame.fn_productNameChange();
      
      // 触发保存 
      
      await clickDom('#td_CF_Tab_1');   
      (await waitDom('#td_CF_REP_tag_1'));
      // 跳回来
      await clickDom('#tr_CF_Tab_3');    
      await waitDom('#Product_Details_Table'); 
      await waitDom('#Form1');
      await wait(200);
      try{        
        // 触发联动
        await window.fm_MainApp.fm_MainFrame.fn_Update_Regimen_Unit();
      }catch(e){
        console.log('fn_Update_Regimen_Unit', e.message || e);
      }
      
      const islast = index === combineDrugs.length - 1;
      if(!islast){
        // 新增药物
        const els = window.fm_MainApp.fm_MainFrame.document.querySelectorAll('#tr_CF_PRD > td:not([id="td_CF_PRD_[[x]]"])');
        await clickDom('#'+ els[els.length - 1].id);
        await waitFrameLoaded();
        await waitDom('#Product_Details_Table'); 
      }
    }
    
    // 触发保存 
    
    await clickDom('#td_CF_Tab_1');    
    (await waitDom('#td_CF_REP_tag_1')); 

    console.log("药品录入完成"); 
    await moduleLogs(
      `药品-双语-${studyDrugs.length + combineDrugs.length}`,
      (timer.getElapsedTime()),
      this.taskId
    );
    this.fillDrugStatus = true; 
    // throw new Error("药品录入已完成，请手动保存并提交报告。");
    
  }
  
  async fillEvent() {
    // 前置数据验证
    const validation = validateBeforeFill('fillEvent', this);
    if (validation.shouldSkip) return;

    const events = this.newReportDataCn?.reportSaeDetailInfo  || this.newReportDataEn?.reportSaeDetailInfo || [];

    timer.start();

    await clickDom('#td_CF_Tab_4');    
    await clickDom('#td_CF_EVENT_1');    
    await clickDom('#td_CF_EVT_1');    

    for (let index = 0; index < events.length; index++) {
      const event = events[index];  
       

      const data = {  
        ...(event.event_xml || {}), 
      }
      await waitDom('#CF_Form');
      await waitDom('#event_details');
      setMainFrameForm(data); 

      const islast = index === events.length - 1;
      if(!islast){
        try{
          window.fm_MainApp.fm_MainFrame.fn_DescriptionReported = () => {};
          window.fm_MainApp.fm_MainFrame.fn_CheckAutoEncode = () => {};
        }catch{
          // 
        }
        //新增
        const els = window.fm_MainApp.fm_MainFrame.document.querySelectorAll('#tr_CF_EVT > td:not([id="td_CF_EVT_[[x]]"])');
        await clickDom('#'+ els[els.length - 1].id);
        await waitDom('#event_details'); 
        
        try{
          window.fm_MainApp.fm_MainFrame.fn_DescriptionReported = () => {};
          window.fm_MainApp.fm_MainFrame.fn_CheckAutoEncode = () => {};
        }catch{
          // 
        }
        
      }
    } 
    
    try{
      window.fm_MainApp.fm_MainFrame.fn_DescriptionReported = () => {};
      window.fm_MainApp.fm_MainFrame.fn_CheckAutoEncode = () => {};
    }catch{
      // 
    }
    
    // 触发保存     
    await clickDom('#td_CF_Tab_1');     
    (await waitDom('#td_CF_REP_tag_1')); 

    console.log("事件录入完成"); 
    await moduleLogs(
      `事件-双语-${events.length}`,
      (timer.getElapsedTime()),
      this.taskId
    );
    
    this.fillEventStatus = true;
  }
  async fillSaeDescribe (){
    // 前置数据验证
    const validation = validateBeforeFill('fillSaeDescribe', this);
    if (validation.shouldSkip) return;

    const saeDescribe = this.newReportDataCn?.saeDescribe  || this.newReportDataEn?.saeDescribe || null;

    timer.start();

    await clickDom('#td_CF_Tab_5');
    await clickDom('#td_CF_ANALYSIS_1');
    const data = {  
      ...(saeDescribe.narrative_xml || {}), 
    }
    await waitDom('#CF_Analysis');
    await waitDom('#Summ_Header_Section');
    setMainFrameForm(data);  

     // 触发保存     
    await clickDom('#td_CF_Tab_1');     
    (await waitDom('#td_CF_REP_tag_1')); 
    


    console.log("分析录入完成"); 
    await moduleLogs(
      `分析-双语`,
      (timer.getElapsedTime()),
      this.taskId
    );
    
    this.fillSaeDescribeStatus = true;
  }

  async fillMeasure (){
    // 前置数据验证
    const validation = validateBeforeFill('fillMeasure', this);
    if (validation.shouldSkip) return;

    const measure = this.newReportDataCn?.measure  || this.newReportDataEn?.measure || null;

    timer.start();

    await clickDom('#td_CF_Tab_6');
    
    const data = {  
      ...(JSON.parse(measure.workLog_xml || 'null') || {}), 
    }
    await waitDom('#CF_Form');
    await waitDom('#lk_status_ddl');   
    setMainFrameForm(data);  

     // 触发保存     
    await clickDom('#td_CF_Tab_1');     
    (await waitDom('#td_CF_REP_tag_1')); 
    


    console.log("措施录入完成"); 
    await moduleLogs(
      `措施-双语-${measure?.workLog?.length}`,
      (timer.getElapsedTime()),
      this.taskId
    );
    
    this.fillMeasureStatus = true;
  }
  async fillBaseInfo(){
    // 前置数据验证
    const validation = validateBeforeFill('fillBaseInfo', this);
    if (validation.shouldSkip) return;

    const reportBaseInfo = this.newReportDataCn?.reportBaseInfo  || this.newReportDataEn?.reportBaseInfo || null;
    const reportReporterInfo_xml = this.newReportDataCn?.reportReporterInfo_xml  || this.newReportDataEn?.reportReporterInfo_xml || '';

    timer.start();

    await clickDom('#td_CF_Tab_1');   
    await waitDom('#tr_CF_REP_1')
    
    const data = {
      ...(reportBaseInfo.base_xml  || {}), 
      ...(reportBaseInfo.btnAddClass_xml  || {}), 
    }
    if( reportReporterInfo_xml ){
      data.rep_info = reportReporterInfo_xml;
    }   
    if( reportBaseInfo.btnAddFol_xml ){
      data.followup_info = reportBaseInfo.btnAddFol_xml;
    }
    setMainFrameForm(data);  

     // 触发保存     
    await clickDom('#td_CF_Tab_2');  
    await waitDom('#Pat_Lab_Header_Section')   

    console.log("通用录入完成"); 
    await moduleLogs(
      `通用-双语`,
      (timer.getElapsedTime()),
      this.taskId
    );
    
    this.fillBaseInfoStatus = true;
  }  
  async fillPatient(){
    // 前置数据验证
    const validation = validateBeforeFill('fillPatient', this);
    if (validation.shouldSkip) return;

    const reportSubjectInfo = this.newReportDataCn?.reportSubjectInfo  || this.newReportDataEn?.reportSubjectInfo || null;

    timer.start();

    await clickDom('#td_CF_Tab_2');       
    await clickDom('#td_CF_PATIENT_tag_1'); 
    await waitDom('#addlabtest');  
    
    const data = {
      ...(reportSubjectInfo.base_xml  || {}), 
    } 
    setMainFrameForm(data);  

     // 触发保存     
    await clickDom('#td_CF_Tab_1');  
    await waitDom('#td_CF_REP_tag_1'); 
    await clickDom('#td_CF_Tab_2'); 
    await waitDom('#addlabtest'); 

    console.log("患者录入完成"); 
    await moduleLogs(
      `患者-双语`,
      (timer.getElapsedTime()),
      this.taskId
    );
    
    this.fillPatientStatus = true;
  }
  
} 