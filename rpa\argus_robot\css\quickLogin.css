body {
  margin: 0;
  padding: 0;
}

#credentials-list {
  padding: 10px;
}

.credential-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
  cursor: pointer;
}

.icon-lock {
  padding: 7px;
  border-radius: 5px;
  background: #639cb9;
  display: flex;
  justify-content: center;
  align-items: center;
}

.icon-edit {
  width: 15px;
  height: 15px;
}

.username-item {
  display: flex;
  flex-direction: column;
  flex-wrap: nowrap;
  flex: 1;
}

.username-item .name {
  color: #7e7e70;
  font-size: 12px;
  width: 100%;
  white-space: nowrap; /* 强制文本在一行显示 */
  overflow: hidden; /* 隐藏超出部分 */
  text-overflow: ellipsis;
}

.username-item .username {
  color: #000;
  font-size: 14px;
  width: 100%;
  white-space: nowrap; /* 强制文本在一行显示 */
  overflow: hidden; /* 隐藏超出部分 */
  text-overflow: ellipsis;
}

#more-options {
  display: none;
  align-items: center;
  padding: 5px;
  position: absolute;
  padding-left: 20px;
  bottom: 0;
  width: calc(100% - 25px);
  box-shadow: rgba(0, 0, 0, 0.3) 0px 0px 10px;
}

/* 隐藏弹出框 */
#edit-credential-option {
  display: none;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: white;
  border: 1px solid #888;
  z-index: 999;
  padding: 20px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
  border-radius: 5px;
  width: 400px;
}

#edit-credential-option h2 {
  margin-top: 0;
  color: #333;
  border-bottom: 1px solid #ccc;
  padding-bottom: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
}

#edit-credential-option label {
  display: block;
  color: #555;
}

#edit-credential-option .flex-2{
    display: flex;
    align-items: center;
    gap: 10px;
}

.flex-2 > div{
    flex: 1;
}

.flex-2 .password{
    display: flex;
    align-items: center;
    gap: 5px;
}

#edit-credential-option input[type="text"],
#edit-credential-option input[type="password"],
#edit-credential-option textarea {
  width: calc(100% - 16px);
  padding: 8px;
  margin-top: 5px;
  margin-bottom: 15px;
  border: 1px solid #ccc;
  border-radius: 3px;
}

#advancedSettings {
  margin-top: 15px;
  color: #555;
  cursor: pointer;
}

#edit-credential-option button {
  padding: 8px 15px;
  margin-right: 10px;
  border: none;
  border-radius: 3px;
  cursor: pointer;
}

#cancelButton {
  background-color: #ccc;
  color: #333;
}

#saveButton {
  background-color: #e74c3c;
  color: white;
}
