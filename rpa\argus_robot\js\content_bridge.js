
class MessageBridge {
  constructor() {
    this.handlers = new Map();
    this.init();
  }

  init() {
    // 监听来自rpa.js的消息
    window.addEventListener('message', (event) => {
      if (event.data && event.data.type === 'RPA_REQUEST') {
        this.handleRpaRequest(event.data);
      }
    });

    // 监听来自background.js的消息
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      if (message.type === 'BACKGROUND_RESPONSE') {
        this.handleBackgroundResponse(message);
      }
    });
  }

  async handleRpaRequest(data) {
    const { requestId, action, params } = data;
    try {
      // 转发请求到background.js
      const response = await chrome.runtime.sendMessage({
        type: 'BRIDGE_REQUEST',
        action,
        params
      });
      
      // 将结果返回给rpa.js
      window.postMessage({
        type: 'BRIDGE_RESPONSE',
        requestId,
        data: response
      }, '*');
    } catch (error) {
      window.postMessage({
        type: 'BRIDGE_RESPONSE',
        requestId,
        error: error.message
      }, '*');
    }
  }

  handleBackgroundResponse(message) {
    // 转发background的消息到rpa.js
    window.postMessage({
      type: 'BACKGROUND_EVENT',
      data: message.data
    }, '*');
  }
}

// 初始化桥接器
const bridge = new MessageBridge();
