async function report_QC() {
  const mainAppId = "fm_MainApp";
  const mainFrameId = "fm_MainFrame";
  const iframeDialog = "#iframeDialog";
  const Loading = "loading";
  const Gloading = "gloading";
  function getDocument(doc) {
    return doc
      ? doc
      : document
          .getElementById(mainAppId)
          ?.contentDocument?.getElementById(mainFrameId)?.contentDocument;
  }

  function isXPath(key) {
    return key?.startsWith("//");
  }

  function getElement(doc, key, className) {
    try {
      return isXPath(key)
        ? (doc || getDocument())?.evaluate(
            key,
            doc || getDocument(),
            null,
            XPathResult.FIRST_ORDERED_NODE_TYPE,
            null
          )?.singleNodeValue
        : className
        ? (doc || getDocument())?.querySelector(className)
        : (doc || getDocument())?.getElementById(key);
    } finally {
      doc = null;
    }
  }

  function triggerEvent(element, eventType) {
    return new Promise((resolve) => {
      if (!element) {
        resolve();
        element = null;
        return;
      }

      const event = new Event(eventType, {
        bubbles: true,
      });

      let resolved = false;

      const timeoutId = setTimeout(() => {
        if (!resolved) {
          console.log(`Event ${eventType} not triggered, resolving anyway.`);
          resolved = true;
          resolve();
        }
      }, 100);

      element.addEventListener(eventType, function handler() {
        if (!resolved) {
          clearTimeout(timeoutId);
          element.removeEventListener(eventType, handler);
          resolved = true;
          resolve();
          element = null;
        }
      });

      element?.dispatchEvent(event);
    });
  }

  /**
   * 等待指定的 DOM 元素出现
   * @param {String} key - 元素的 ID 或 XPath 表达式
   * @param {Number} [timeout=3000] - 超时时间，默认为 3000 毫秒
   * @returns {Promise} - 返回一个 Promise，当元素出现时解析，当超时或出错时拒绝
   */
  function waitForElement(key, timeout = 3000) {
    return new Promise((resolve, reject) => {
      const startTime = Date.now(); // 记录开始时间
      let element = null; // 用于存储找到的元素
      function checkElement() {
        // 获取元素
        element =
          key === mainFrameId ? getDocument() : getElement(getDocument(), key);
        if (element) {
          clearInterval(intervalId);
          resolve(true);
          element = null;
        } else if (Date.now() - startTime > timeout) {
          clearInterval(intervalId);
          reject(new Error(`Element of ${key} not found within timeout`));
        } else {
          // 如果未找到元素且未超时，则继续下一帧检查
          requestAnimationFrame(checkElement);
        }
      }
      // 使用 requestAnimationFrame 进行定时检查
      const intervalId = requestAnimationFrame(checkElement);
    });
  }
  function monitorNetworkRequest(timeout = 10) {
    return new Promise((resolve) => {
      let pendingRequests = 0; // 用于跟踪未完成的请求数量
      let timeoutId; // 用于存储超时定时器的 ID

      // 劫持 XMLHttpRequest 的 open 方法
      const originalXHROpen = XMLHttpRequest.prototype.open;
      XMLHttpRequest.prototype.open = function () {
        // 在请求状态改变时触发的事件监听器
        this.addEventListener(
          "readystatechange",
          function () {
            if (this.readyState === 4) {
              // 请求完成
              pendingRequests--; // 减少未完成请求计数
              checkAllRequestsComplete(); // 检查所有请求是否已完成
            }
          },
          false
        );
        pendingRequests++; // 增加未完成请求计数
        clearTimeout(timeoutId); // 清除超时定时器
        originalXHROpen.apply(this, arguments); // 调用原始的 open 方法
      };

      // 劫持 fetch 方法
      const originalFetch = window.fetch;
      window.fetch = function () {
        pendingRequests++;
        clearTimeout(timeoutId);
        return originalFetch
          .apply(this, arguments)
          .then((response) => {
            pendingRequests--;
            checkAllRequestsComplete();
            return response;
          })
          .catch((error) => {
            pendingRequests--;
            checkAllRequestsComplete();
            throw error;
          });
      };

      // 劫持 $.ajax 方法
      // const originalAjax = $.ajax;
      // $.ajax = function(options) {
      //     pendingRequests++;
      //     clearTimeout(timeoutId);

      //     return originalAjax(options)
      //         .then(response => {
      //             pendingRequests--;
      //             checkAllRequestsComplete();
      //             return response;
      //         })
      //         .catch(error => {
      //             pendingRequests--;
      //             checkAllRequestsComplete();
      //             throw error;
      //         });
      // };

      // 检查是否所有请求都已完成
      function checkAllRequestsComplete() {
        if (pendingRequests === 0) {
          clearTimeout(timeoutId);
          resolve(true);
        }
      }

      // 设置超时
      timeoutId = setTimeout(() => {
        if (pendingRequests === 0) {
          resolve(true);
        } else {
          reject(new Error("请求超时"));
        }
      }, timeout);
    });
  }
  async function waitForPageReload(waitTime, key = mainFrameId) {
    let loadingDom;
    let GloadingDom;
    try {
      const requestFinish = await monitorNetworkRequest(waitTime);
      // await wait(1000)
      loadingDom = getDocument()?.getElementById?.(Loading);
      GloadingDom = getDocument()?.getElementById?.(Gloading);
      if (loadingDom || GloadingDom) {
        const loadingStyle = window.getComputedStyle(loadingDom);
        const gloadingStyle = window.getComputedStyle(GloadingDom);

        if (
          loadingStyle?.display !== "none" ||
          gloadingStyle?.display !== "none"
        ) {
          await waitForPageReload(waitTime, Loading);
          return;
        }
        if (isDialog()) {
          let loadingContainer;
          try {
            loadingContainer =
              getDialog()?.getElementById?.("loadingContainer");
            if (loadingContainer) {
              const loadingMaskStyle =
                window.getComputedStyle(loadingContainer);
              if (loadingMaskStyle?.display === "block") {
                await waitForPageReload(waitTime);
                return;
              }
            }
          } finally {
            loadingContainer = null;
          }
        }
        await waitForElement(key, waitTime);
        if (requestFinish && performance.timing.responseEnd) {
          return;
        } else {
          await waitForPageReload(waitTime, Loading);
        }
      } else {
        await waitForPageReload(waitTime, key);
      }
    } catch (e) {
      console.log("error", e.message);
    } finally {
      loadingDom = null;
      GloadingDom = null;
    }
  }

  function isDialog() {
    const mainDoc = document.getElementById(mainAppId)?.contentDocument;
    if (!mainDoc) return false;
    const dialogNode = mainDoc.querySelector(iframeDialog);
    const result = !!dialogNode;
    return result;
  }

  function getDialog() {
    const mainDoc = document.getElementById(mainAppId)?.contentDocument;
    if (!mainDoc) {
      return null;
    }
    const currentNodeList = mainDoc.querySelectorAll(iframeDialog);
    if (!currentNodeList || currentNodeList.length === 0) {
      return null;
    }
    for (const node of currentNodeList) {
      if (!node) {
        continue;
      }
      return node.contentDocument;
    }
    return null;
  }

  function getValue(doc, key) {
    const element = doc?.getElementById(key)
    if (element?.type === "select-one") {
      const selectedOption = element.options[element.selectedIndex];
      return selectedOption.text ? element?.value : '';
    }
    return element?.value;
  }

  function processObjectToArray(obj) {
    // 创建一个空对象用于存储分组后的数据
    const groupedData = {};

    // 遍历对象的每个键
    for (const key in obj) {
      // 使用正则表达式提取键名中的数字
      const match = key.match(/_(\d+)_/);
      if (match) {
        const index = parseInt(match[1], 10); // 提取的数字作为索引
        // 初始化索引对应的数组
        if (!groupedData[index]) {
          groupedData[index] = {};
        }
        // 将键值对加入对应的索引分组中
        groupedData[index][key] = obj[key];
      }
    }

    // 将分组后的数据转换为数组
    const resultArray = [];
    const indices = Object.keys(groupedData)
      .map(Number)
      .sort((a, b) => a - b);

    // 按照索引顺序填充数组
    indices.forEach((index) => {
      resultArray[index] = groupedData[index];
    });

    return resultArray;
  }

  function processObject(inputObj) {
    const evaluationList = [];
    const otherData = {};

    // 遍历输入对象的每个键值对
    for (const key in inputObj) {
      if (inputObj.hasOwnProperty(key)) {
        // 检查是否属于评价列表的键
        if (
          key.includes("method_id") ||
          key.includes("causality_id") ||
          key.includes("source_id")
        ) {
          // 提取前缀（TXT_rpt_，TXT_det_，TXT_prt_）
          const prefixMatch = key.match(/TXT_(rpt|det|prt)_/);
          if (prefixMatch) {
            const prefix = prefixMatch[0]; // e.g., TXT_rpt_

            // 查找或创建相应的评价对象
            let evaluation = evaluationList.find((e) => e.prefix === prefix);
            if (!evaluation) {
              evaluation = { prefix };
              evaluationList.push(evaluation);
            }

            // 根据类型存储值
            if (key.includes("source_id")) {
              evaluation.source_id = inputObj[key];
            } else if (key.includes("method_id")) {
              evaluation.method_id = inputObj[key];
            } else if (key.includes("causality_id")) {
              evaluation.causality_id = inputObj[key];
            }
          }
        } else {
          // 处理其他数据，去除后缀编号
          const baseKey = key.replace(/_\d+$/, "").replace(/\d+$/, ""); // 去除后缀编号
          otherData[baseKey] = inputObj[key];
        }
      }
    }

    // 移除评价对象的前缀
    evaluationList.map((e) => {
      delete e.prefix;
      return e;
    });

    // 返回最终结果
    return {
      评价列表: evaluationList,
      ...otherData,
    };
  }

  const getFormItems = (containerID, isTable) => {
    if (!containerID) {
      return;
    }
    const allElements = (typeof containerID === 'string' ? getDocument()
      .getElementById(containerID) : containerID)
      ?.getElementsByTagName("*");
    if (!allElements?.length) return;
    // 创建一个数组所有表单元素
    const formElements = [];

    // 遍历所有元素
    for (let i = 0; i < allElements?.length; i++) {
      const childElement = allElements[i];
      // 筛选存在tabindex属性，并且不是button或img的元素
      if (
        childElement.getAttribute("tabindex") &&
        !["BUTTON"].includes(childElement.nodeName)
      ) {
        if (childElement.nodeName === "IMG") {
          if (/^TableNotesAttach_\d+_imgNADocument$/.test(childElement.id))
            formElements.push(childElement);
        } else formElements.push(childElement);
      }
    }
    let result = {};

    for (let j = 0; j < formElements?.length; j++) {
      const form = formElements[j];
      if (
        ["YYYY/MM/DD", "00-MMM-0000"].includes(form.value) &&
        !/^TableNotesAttach_\d+_imgNADocument$/.test(form.id)
      )
        continue;
      else if (form?.id?.endsWith("_nfddl")) {
        if (+form?.value > 0) {
          result[form?.id?.replace("_nfddl", "_nfdiv")] = "click";
          result[form?.id] = form?.value;
        } else continue;
      }else if (form.type === "radio") {
        if (form.checked) result[form.id] = "1";
        else continue;
      }else if (form.type === "checkbox") {        
        result[form.id] = form.checked ? "1" : "";
      }else if (form.type === "select-one") {
        const selectedOption = form.options[form.selectedIndex];
        result[form.id] = selectedOption?.text ? form?.value : "";
      } else if (/^TableNotesAttach_\d+_imgNADocument$/.test(form.id))
        result[form.id] = "附件";
      else result[form.id] = form.value;
    }
    if (isTable) {
      result = processObjectToArray(result);
    }
    return result;
  };

  function wait(waitTime) {
    return new Promise((resolve) => {
      setTimeout(resolve, waitTime);
    });
  }

  function getEvaluationFormList() {
    // 返回
    const result = {};
    // 获取id为Event_Assess_Table的元素
    const eventAssessTable = getDocument().getElementById("Event_Assess_Table");
    if (!eventAssessTable) return;

    // 获取所有子元素
    const childElements = eventAssessTable?.getElementsByTagName("*");

    // 创建一个数组来存储id包含Product_Row_的元素
    const productRowElements = [];

    // 遍历所有子元素
    for (let i = 0; i < childElements.length; i++) {
      const childElement = childElements[i];
      // 如果元素的id包含Product_Row_，添加到数组中
      if (childElement.id && childElement.id.includes("Product_Row_")) {
        productRowElements.push(childElement);
      }
    }
    // 遍历所有productRowElements
    productRowElements.forEach((productRowElement) => {
      // 创建一个数组来存储id包含Event_Row_的元素
      const eventRowElements = [];
      // 获取productRowElement的所有子元素
      const productRowChildElements =
        productRowElement.getElementsByTagName("*");

      // 遍历所有子元素
      for (let j = 0; j < productRowChildElements.length; j++) {
        const productRowChildElement = productRowChildElements[j];
        // 如果元素的id包含Event_Row_，添加到数组中
        if (productRowChildElement.getAttribute("event_assess_seq_num")) {
          eventRowElements.push(productRowChildElement);
        }
      }
      result[
        productRowElement.querySelectorAll(".label-hyp")?.[0]?.textContent
      ] = eventRowElements;
    });
    return result;
  }

  // 通用
  async function collectGeneralTableData() {
    // 获取通用容器
    const GeneralTable = getDocument().getElementById("General_Table");
    if (!GeneralTable) {
      console.error("无法找到ID为 'General_Table' 的元素。");
      return;
    }
    const reportBaseInfo = {
      TXT_report_type: getValue(getDocument(), "TXT_report_type"),
      country_text: getValue(getDocument(), "country_text"),
      init_rept_date: getValue(getDocument(), "init_rept_date"),
      central_receipt_date: getValue(getDocument(), "central_receipt_date"),
      MEDICALLY_CONFIRM: getValue(getDocument(), "MEDICALLY_CONFIRM"),
      Initialjustification: getValue(getDocument(), "Initialjustification"),
      CSM_UD_DATE_1: getValue(getDocument(), "CSM_UD_DATE_1"),
      CSM_UD_NUMBER_2: getValue(getDocument(), "CSM_UD_NUMBER_2"),
      CSM_UD_NUMBER_3: getValue(getDocument(), "CSM_UD_NUMBER_3"),
      btnAddClass: getFormItems("Class_DataDiv", "table"),
      btnAddFol: getFormItems("Fol_Table", "table"),
      ...getFormItems("Study_Info_Content_Div"),
    };

    const reportReporterTabs = getDocument().getElementById("tr_CF_REP");
    if (!reportReporterTabs) {
      console.error("无法找到ID为 'tr_CF_REP' 的元素。");
      return;
    }
    // 获取所有报告者名称列表
    const repNameLists = [];

    for (let element of reportReporterTabs.children) {
      if (
        !(
          element.getAttribute("title")?.includes("(New)") ||
          element.getAttribute("title")?.includes("(新建)")
        )
      ) {
        repNameLists.push(element);
      }
    }
    const reportReporterInfo = [];
    for (let item of repNameLists) {
      triggerEvent(getElement(getDocument(), `//*[@id='${item.id}']`), "click");
      await waitForPageReload(1000);
      reportReporterInfo.push(getFormItems("Reporter_Data_Table"));
    }

    return {
      reportBaseInfo: reportBaseInfo,
      reportReporterInfo: reportReporterInfo,
    };
  }

  // 实验室检查
  function collectLabData() {
    // 获取实验室数据模块的容器
    const labDataDiv = getDocument().getElementById("Pat_LabData_Content_Div");
    if (!labDataDiv) {
      console.error("无法找到ID为 'Pat_LabData_Content_Div' 的元素。");
      return;
    }

    // 获取实验室测试表格
    const labTestsTable = labDataDiv.querySelector("#labtests");
    if (!labTestsTable) {
      console.error("无法找到ID为 'labtests' 的表格元素。");
      return;
    }

    // 获取所有实验室测试行，排除表头
    const labTestRows = labTestsTable.querySelectorAll("tr[id^='labtest_']");

    // 获取日期头部（examineDate）从crosstab表格
    const crosstabTable = labDataDiv.querySelector("#crosstab");
    if (!crosstabTable) {
      console.error("无法找到ID为 'crosstab' 的表格元素。");
      return;
    }

    // 获取所有日期列（examineDate）
    const dateHeaders = crosstabTable.querySelectorAll(
      "#labcontentheader input[name^='labdate_']"
    );
    const examineDates = Array.from(dateHeaders).map((input) =>
      input.value.trim()
    );

    // 初始化数据数组
    const labDataArray = [];

    // 遍历每一行实验室测试
    labTestRows.forEach((row, rowIndex) => {
      // 获取lab_data_llt_code和lab_data_llt
      const lltCodeElement = row.querySelector(
        "input[name='lab_data_llt_code']"
      );
      const lltNameElement = row.querySelector("input[name='lab_data_llt']");
      const lltCode = lltCodeElement ? lltCodeElement.value.trim() : "未知编码";
      const lltName = lltNameElement ? lltNameElement.value.trim() : "未知名称";

      // 组合字段labtest为 "代码(名称)"
      const labtest = `${lltCode}(${lltName})`;

      // 获取labtestreptd
      const labtestreptdElement = row.querySelector(
        "input[name='labtestreptd']"
      );
      const labtestreptd = labtestreptdElement
        ? labtestreptdElement.value.trim()
        : "";

      // 获取labtestlow和labtesthigh
      const labtestlowElement = row.querySelector("input[name='labtestlow']");
      const labtestlow = labtestlowElement
        ? labtestlowElement.value.trim()
        : "";

      const labtesthighElement = row.querySelector("input[name='labtesthigh']");
      const labtesthigh = labtesthighElement
        ? labtesthighElement.value.trim()
        : "";

      // 获取TXT_labunit_X（只读字段）
      const txtLabunitElement = row.querySelector(
        `input[name='TXT_labunit_${rowIndex}']`
      );
      const txtLabunit = txtLabunitElement
        ? txtLabunitElement.value.trim()
        : "";

      // 遍历每个检查日期
      examineDates.forEach((examineDate, dateIndex) => {
        // 构建单元格ID，如 cell_0_0
        const cellId = `cell_${rowIndex}_${dateIndex}`;
        const cell = getDocument().getElementById(cellId);
        if (!cell) {
          console.warn(`无法找到ID为 '${cellId}' 的单元格。`);
          return;
        }

        // 提取labresult
        const labresultElement = cell.querySelector("input[name='labresult']");
        const labresult = labresultElement ? labresultElement.value.trim() : "";
        
        // 提取comments
        const labassessElement = cell.querySelector('[id^="TXT_labassess_"]');
        const labassess = labassessElement ? labassessElement.value.trim() : "";

        // 提取labnotes
        const labnotesElement = cell.querySelector("textarea[name='labnotes']");
        const labnotes = labnotesElement ? labnotesElement.value.trim() : "";

        // 提取comments
        const commentsElement = cell.querySelector("textarea[name='comments']");
        const comments = commentsElement ? commentsElement.value.trim() : "";

        if (cell?.children?.length > 0) {
          const labDataObject = {
            labtestlow: labtestlow,
            labtesthigh: labtesthigh,
            comments: comments,
            qualitativeResultType: labassess,
            examineDate: examineDate.replace(/\//g, "-"),
            [`TXT_labunit_0`]: txtLabunit,
            labtestreptd: labtestreptd,
            labresult: labresult,
            labnotes: labnotes,
            labtest: labtest,
          };

          labDataArray.push(labDataObject);
        }
      });
    });

    // 输出最终的数据数组
    return labDataArray;
  }

  const getValueByNodename = (element) => {
    const tagName = element.tagName.toLowerCase();
    if (tagName === "input") {
      if (element.type === "checkbox") {
        return element.checked;
      } else {
        return element.value.trim();
      }
    } else if (tagName === "select") {
      const selectedOption = element.options[element.selectedIndex];
      return selectedOption.text ? element.value : '';
    } else if (tagName === "textarea") {
      return element.value.trim();
    } else {
      return element.textContent.trim();
    }
  };

  // 病史
  function collectSelectedRelHistData() {
    // 获取表格元素
    const table = getDocument().querySelector("#Rel_Hist_Table");
    if (!table) {
      console.error("无法找到ID为 'Rel_Hist_Table' 的表格元素。");
      return;
    }

    // 获取所有数据行，排除表头
    const rows = table.querySelectorAll("tr[id^='Rel_Hist_Table_']");

    // 初始化一个空数组，用于存储每一行的数据对象
    const selectedData = [];

    // 定义需要采集的字段列表及其对应的ID模板
    const fieldsMapping = {
      pat_hist_cont: "Rel_Hist_Table_{index}_pat_hist_cont",
      pat_hist_rptd: "Rel_Hist_Table_{index}_pat_hist_rptd",
      pat_hist_start: "Rel_Hist_Table_{index}_pat_hist_start",
      pat_hist_stop: "Rel_Hist_Table_{index}_pat_hist_stop",
      pat_hist_notes: "Rel_Hist_Table_{index}_pat_hist_notes",
      pat_hist_type: "TXT_Rel_Hist_Table_{index}_pat_hist_type",
      pat_hist_llt_code: "Rel_Hist_Table_{index}_pat_hist_llt_code",
      pat_hist_llt: "Rel_Hist_Table_{index}_pat_hist_llt",
    };

    // 遍历每一行
    rows.forEach((row, rowIndex) => {
      const rowData = {};

      // 遍历需要采集的字段
      for (const [key, idTemplate] of Object.entries(fieldsMapping)) {
        // 替换索引以构建实际的ID
        const fieldId = idTemplate.replace("{index}", rowIndex);

        // 尝试选择对应的元素（input、select、textarea）
        let element = row.querySelector(`#${fieldId}`);
        if (!element) {
          console.warn(
            `在第 ${rowIndex + 1} 行中，无法找到ID为 '${fieldId}' 的元素。`
          );
          rowData[key] = null; // 设置为null以表示缺失
          continue;
        }
        rowData[key] = getValueByNodename(element);
      }

      // 处理 "pat_hist_rptd_lltname" 字段
      // 组合 "pat_hist_llt_code" 和 "pat_hist_llt" 为 "pat_hist_rptd_lltname"
      const lltCode = rowData["pat_hist_llt_code"] || "未知编码";
      const lltName = rowData["pat_hist_llt"] || "未知名称";
      const lltNameFormatted = `${lltCode}(${lltName})`;

      // 构建最终的字段名，包括行索引
      const rptdLltNameKey = `Rel_Hist_Table_${rowIndex}_pat_hist_rptd_lltname`;
      rowData[rptdLltNameKey] = lltNameFormatted;

      // 构建最终的对象，只保留所需的字段
      const finalRowData = {
        [rptdLltNameKey]: rowData[rptdLltNameKey],
        [`Rel_Hist_Table_${rowIndex}_pat_hist_cont`]: rowData["pat_hist_cont"],
        [`Rel_Hist_Table_${rowIndex}_pat_hist_rptd`]: rowData["pat_hist_rptd"],
        [`Rel_Hist_Table_${rowIndex}_pat_hist_start`]:
          rowData["pat_hist_start"],
        [`Rel_Hist_Table_${rowIndex}_pat_hist_stop`]: rowData["pat_hist_stop"],
        [`Rel_Hist_Table_${rowIndex}_pat_hist_notes`]:
          rowData["pat_hist_notes"],
        [`TXT_Rel_Hist_Table_${rowIndex}_pat_hist_type`]:
          rowData["pat_hist_type"],
      };

      // 将当前行的数据对象添加到selectedData数组中
      selectedData.push(finalRowData);
    });

    // 输出最终的数据数组
    console.log(selectedData);
    return selectedData;
  }

  // 死因及尸检结果
  function collectCauseData() {
    // 获取表格元素
    const table = getDocument().querySelector("#Death_Details_Div");
    if (!table) {
      console.error("无法找到ID为 'Death_Details_Div' 的表格元素。");
      return;
    }

    // 获取所有数据行，排除表头
    const rows = table.querySelectorAll("tr[id^='CSDD_']");

    // 初始化一个空数组，用于存储每一行的数据对象
    const selectedData = [];

    // 定义需要采集的字段列表及其对应的ID模板
    const fieldsMapping = {
      cause_reptd: "CSDD_{index}_cause_reptd",
      term_type: "CSDD_{index}_term_type",
      cause_llt_code: "CSDD_{index}_cause_llt_code",
      cause_llt: "CSDD_{index}_cause_llt",
    };

    // 遍历每一行
    rows.forEach((row, rowIndex) => {
      const rowData = {};

      // 遍历需要采集的字段
      for (const [key, idTemplate] of Object.entries(fieldsMapping)) {
        // 替换索引以构建实际的ID
        const fieldId = idTemplate.replace("{index}", rowIndex);

        // 尝试选择对应的元素（input、select、textarea）
        let element = row.querySelector(`#${fieldId}`);
        if (!element) {
          console.warn(
            `在第 ${rowIndex + 1} 行中，无法找到ID为 '${fieldId}' 的元素。`
          );
          rowData[key] = null; // 设置为null以表示缺失
          continue;
        }

        // 根据元素类型提取值
        rowData[key] = getValueByNodename(element);
      }

      // 处理 "cause_llt_lltname" 字段
      // 组合 "cause_llt_code" 和 "cause_llt" 为 "cause_llt_lltname"
      const lltCode = rowData["cause_llt_code"] || "未知编码";
      const lltName = rowData["cause_llt"] || "未知名称";
      const lltNameFormatted = `${lltCode}(${lltName})`;

      // 构建最终的字段名，包括行索引
      const rptdLltNameKey = `CSDD_${rowIndex}_cause_llt_name`;
      rowData[rptdLltNameKey] = lltNameFormatted;

      // 构建最终的对象，只保留所需的字段
      const finalRowData = {
        [rptdLltNameKey]: rowData[rptdLltNameKey],
        [`CSDD_${rowIndex}_cause_reptd`]: rowData["cause_reptd"],
        [`CSDD_${rowIndex}_term_type`]: rowData["term_type"],
      };

      // 将当前行的数据对象添加到selectedData数组中
      selectedData.push(finalRowData);
    });
    return selectedData;
  }

  // 患者
  function collectPatientData() {
    // 获取通用容器
    const patientInfoDiv = getDocument().getElementById("Pat_Info_Content_Div");
    if (!patientInfoDiv) {
      console.error("无法找到ID为 'Pat_Info_Content_Div' 的元素。");
      return;
    }
    const patDetailsDiv = getDocument().getElementById(
      "Pat_Details_Content_Div"
    );
    if (!patDetailsDiv) {
      console.error("无法找到ID为 'Pat_Details_Content_Div' 的元素。");
      return;
    }
    return {
      reportSubjectInfo: {
        ...getFormItems("Pat_Info_Content_Div"),
        ...getFormItems("Pat_Details_Content_Div"),
        // 人种信息
        pat_race_add: getFormItems("ALC_RaceInfo", "table"),
        // 死亡详细信息
        deadReasonInfoTable: {
          death_date: getValue(getDocument(), "death_date"),
          autopsy_done: getValue(getDocument(), "autopsy_done"),
          autopsy_results: getValue(getDocument(), "autopsy_results"),
          btnAdd: collectCauseData(),
        },
        // 病史/过去用药史
        rel_hist_add: collectSelectedRelHistData(),
        // 实验室检查
        refExamineTable: collectLabData(),
      },
    };
  }

  // 产品适应症
  function collectIndTableData() {
    // 获取表格元素
    const table = getDocument().querySelector("#Ind_Table");
    if (!table) {
      console.error("无法找到ID为 'Ind_Table' 的表格元素。");
      return;
    }

    // 获取所有数据行，排除表头
    const rows = table.querySelectorAll("tr[id^='Ind_Table_']");

    // 初始化一个空数组，用于存储每一行的数据对象
    const selectedData = [];

    // 定义需要采集的字段列表及其对应的ID模板
    const fieldsMapping = {
      ind_reptd: "Ind_Table_{index}_ind_reptd",
      ind_code: "Ind_Table_{index}_ind_llt_code",
      ind_llt: "Ind_Table_{index}_ind_llt",
      ind_reptd_nfddl: "Ind_Table_{index}_ind_reptd_nfddl",
    };

    // 遍历每一行
    rows.forEach((row, rowIndex) => {
      const rowData = {};

      // 遍历需要采集的字段
      for (const [key, idTemplate] of Object.entries(fieldsMapping)) {
        // 替换索引以构建实际的ID
        const fieldId = idTemplate.replace("{index}", rowIndex);

        // 尝试选择对应的元素（input、select、textarea）
        let element = row.querySelector(`#${fieldId}`);
        if (!element) {
          console.warn(
            `在第 ${rowIndex + 1} 行中，无法找到ID为 '${fieldId}' 的元素。`
          );
          rowData[key] = null; // 设置为null以表示缺失
          continue;
        }

        // 根据元素类型提取值
        rowData[key] = getValueByNodename(element);
      }

      // 处理 "ind_llt_lltname" 字段
      // 组合 "ind_code" 和 "ind_llt" 为 "ind_llt_lltname"
      const lltCode = rowData["ind_code"] || "未知编码";
      const lltName = rowData["ind_llt"] || "未知名称";
      const lltNameFormatted = `${lltCode}(${lltName})`;

      // 构建最终的字段名，包括行索引
      const rptdLltNameKey = `Ind_Table_${rowIndex}_ind_reptd_lltname`;
      rowData[rptdLltNameKey] = lltNameFormatted;

      // 构建最终的对象，只保留所需的字段
      const finalRowData = {
        [rptdLltNameKey]: rowData[rptdLltNameKey],
        [`Ind_Table_${rowIndex}_ind_reptd`]: rowData["ind_reptd"],
        [`Ind_Table_${rowIndex}_ind_reptd_nfddl`]: rowData["ind_reptd_nfddl"],
      };

      if (rowData["ind_reptd_nfddl"] && +rowData["ind_reptd_nfddl"] > 0) {
        finalRowData[`Ind_Table_${rowIndex}_ind_reptd_nfdiv`] = "click";
      }

      // 将当前行的数据对象添加到selectedData数组中
      selectedData.push(finalRowData);
    });
    return selectedData;
  }

  // 产品
  async function collectProductData() {
    // 获取产品名称容器
    const allProNameLists = getDocument().getElementById("tr_CF_PRD");
    if (!allProNameLists) {
      console.error("无法找到ID为 'tr_CF_PRD' 的元素。");
      return;
    }

    // 获取所有产品名称列表
    const proNameLists = [];
    for (let element of allProNameLists.children) {
      if (
        !(
          element.getAttribute("title")?.includes("(New)") ||
          element.getAttribute("title")?.includes("(新建)")
        )
      ) {
        proNameLists.push(element);
      }
    }

    // 试验用药
    const experMedic = [];
    // 合并用药
    const combinMedic = [];

    const getMedicRegimen = async () => {
      // 获取产品名称容器
      const allMedicRegimens = getDocument().getElementById("tr_CF_DOSEREG");
      if (!allMedicRegimens) {
        console.error("无法找到ID为 'tr_CF_DOSEREG' 的元素。");
        return;
      }

      // 获取所有产品名称列表
      const medicRegimens = [];
      for (let element of allMedicRegimens.children) {
        if (
          !(
            element.getAttribute("title")?.includes("(New)") ||
            element.getAttribute("title")?.includes("(新建)")
          )
        ) {
          medicRegimens.push(element);
        }
      }
      const medicRegimenData = [];
      for (let item of medicRegimens) {
        triggerEvent(
          getElement(getDocument(), `//*[@id='${item.id}']`),
          "click"
        );
        await waitForPageReload(1000);
        // 获取给药方案
        medicRegimenData.push(getFormItems("Regimen_Data_Table"));
      }

      return medicRegimenData;
    };

    for (let item of proNameLists) {
      triggerEvent(getElement(getDocument(), `//*[@id='${item.id}']`), "click");
      await waitForPageReload(3000);

      let count = 0;
      while (
        !item.getAttribute("title").includes(getDocument().getElementById("product_name")?.value)
      ) {
        triggerEvent(
          getElement(getDocument(), `//*[@id='${item.id}']`),
          "click"
        );
        await waitForPageReload(3000);
        if (count >= 3) break;
        count++;
      }
      const isTrialDrugChecked =
        getDocument().getElementById("drug_type_0").checked;
      const isCombinedDrugChecked =
        getDocument().getElementById("drug_type_1").checked;

      const medType = isTrialDrugChecked
        ? "试验用药"
        : isCombinedDrugChecked
        ? "合并用药"
        : "";
      // 获取产品名称基本信息
      const prodInfoData = getFormItems("ProdInfo_Data_Table");
      if (medType === "合并用药") {
        prodInfoData.product_name_lltname = prodInfoData.who_drug_code
          ? `${prodInfoData.who_drug_code}(${prodInfoData.product_name})`
          : "";
      } else if (medType === "试验用药") {
        if (prodInfoData.who_drug_code)
          prodInfoData.product_name_lltname = `${prodInfoData.who_drug_code}(${prodInfoData.product_name})`;
      }
      const baseInfo = {
        ...prodInfoData,
        // 产品使用详情信息
        ...getFormItems("Product_Details_Data_Table"),
        // QC信息
        // ...getFormItems("QC_Table"),
        // 获取成份信息
        btnAddIng: getFormItems("IngredientTable", "table"),
        // 适应症信息
        btnAddInd: collectIndTableData(),
        // 获取给药方案
        expDoseTable: await getMedicRegimen(),
        ...getFormItems("Product_Details_Table"),
      };
      // 产品名称关联信息
      // getFormItems('PartNameTable')

      if (medType === "试验用药") experMedic.push(baseInfo);
      else if (medType === "合并用药") combinMedic.push(baseInfo);
    }

    return {
      drugInfo: {
        试验用药: experMedic,
        合并用药: combinMedic,
      },
    };
  }

  // 事件
  async function collectEventData() {
    const eventNameTabDivs = getDocument().getElementById("tr_CF_EVT");
    if (!eventNameTabDivs) {
      console.error("无法找到ID为 'tr_CF_EVT' 的元素。");
      return;
    }
    const eventNameTabs = [];
    for (let element of eventNameTabDivs.children) {
      if (
        !(
          element.getAttribute("title")?.includes("(New)") ||
          element.getAttribute("title")?.includes("(新建)")
        )
      ) {
        eventNameTabs.push(element);
      }
    }

    const eventNameList = [];
    for (let eventName of eventNameTabs) {
      triggerEvent(
        getElement(getDocument(), `//*[@id='${eventName.id}']`),
        "click"
      );
      await waitForPageReload(2000);
      let eventData = getFormItems("Event_Info_Content_Div");
      let count = 0;
      if(getDocument().getElementById("sc_hosp").checked){
        triggerEvent(getDocument().getElementById("btn_Hosp"), "click");
        await waitForPageReload(1000);
        // 判断弹窗是否出现
        while(!isDialog()) {
          count++;
          triggerEvent(getDocument().getElementById("btn_Hosp"), "click");
          await waitForPageReload(1000);
          if(count >= 5) break;
        }
      }
      count = 0;
      // 判断是否因为网络延迟，弹窗内容未加载
      if(isDialog()){
        while(!document.getElementById(mainAppId).contentDocument?.getElementById("dialogTitle")?.textContent){
          count++;
          console.log('等待事件信息弹窗加载');
          await waitForPageReload(1000);
          if(count >= 15) break;
        }
      }
      await waitForPageReload(1000);
      eventData = {
        ...eventData,
        start_date: getDialog()?.getElementById("start_date")?.value,
        end_date: getDialog()?.getElementById("end_date")?.value,
        event_caused: getDialog()?.getElementById("event_caused")?.checked ? '1' : '',
        prolonged: getDialog()?.getElementById("prolonged")?.checked ? '1' : '',
        desc_lltname: getDocument()
          .getElementById("inc_term_display")
          ?.value?.replace(/(.+?)\s*\((\d+)\)/, "$2($1)"),
      };
      eventNameList.push(eventData);
      await waitForPageReload(1000);
      triggerEvent(getDialog()?.getElementById("btn_Cancel"), "click");
      await waitForPageReload(1000);
      while(isDialog()){
        triggerEvent(getDialog()?.getElementById("btn_Cancel"), "click");
        triggerEvent(getDialog()?.getElementById("bt_cancel"), "click");
        await waitForPageReload(1000);
      }
    
    }
    return {
      reportSaeDetailInfo: eventNameList,
    };
  }

  // 事件评估
  async function collectEventAssessmentData() {
    const eventTabDivs = getDocument().getElementById("tr_CF_EVENT");
    if (!eventTabDivs) {
      console.error("无法找到ID为 'tr_CF_EVENT' 的元素。");
      return;
    }
    // 获取事件Tab列表
    let eventTabs = [];
    for (let element of eventTabDivs.children) {
      eventTabs.push(element);
    }
    eventTabs = eventTabs.slice(1, 3);
    let comparaData = [];
    for (let item of eventTabs) {
      triggerEvent(getElement(getDocument(), `//*[@id='${item.id}']`), "click");
      await waitForPageReload(2000);
      const eventAssessTableDiv =
        getDocument().getElementById("Event_Assess_Table");
      if (!eventAssessTableDiv) {
        console.error("无法找到ID为 'Event_Assess_Table' 的元素。");
        return;
      }
      const evaluationFormList = getEvaluationFormList();
      let matrixList = [];
      for (let name in evaluationFormList) {
        let eventMatrix = evaluationFormList[name];
        for (let event of eventMatrix) {
          const descReptd =
            event.querySelectorAll(".label-hyp-italic")?.[0]?.textContent;
          matrixList.push({
            product_name: name?.trim(),
            desc_reptd: descReptd.match(/^\((.*)\)$/)?.[1],
            ...processObject(getFormItems(event)),
          });
        }
      }
      comparaData.push(matrixList);
    }
    function processData(inputArray) {
      // 确保输入是一个包含至少两个数组的数组
      if (!Array.isArray(inputArray) || inputArray.length < 2) {
        throw new Error("输入数据格式不正确，必须是至少包含两个数组的数组。");
      }

      const firstArray = inputArray[0];
      const secondArray = inputArray[1];

      // 遍历第一项数组中的每一个对象
      firstArray.forEach((item, index) => {
        if (item.det_list_id !== undefined) {
          // 将det_list_id复制到第二项数组中对应的对象
          if (secondArray[index]) {
            secondArray[index].det_list_id = item.det_list_id;
          }
        }
      });

      // 返回外层数组的第二个数组
      return secondArray;
    }

    return {
      药品和不良反应评价矩阵: processData(comparaData),
    };
  }

  // 分析
  function collectSaeDescribeData() {
    // 获取通用容器
    const saeDescribeDiv = getDocument().getElementById("Narr_Content_Div");
    if (!saeDescribeDiv) {
      console.error("无法找到ID为 'Narr_Content_Div' 的元素。");
      return;
    }

    return {
      saeDescribe: {
        ...getFormItems("Narr_Content_Div"),
        ...getFormItems("Summ_Content_Div"),
      },
    };
  }

  // 附加信息
  function collectAdditionalInfoData() {
    // 获取通用容器
    const tableNotesAttachDiv =
      getDocument().getElementById("TableNotesAttach");
    if (!tableNotesAttachDiv) {
      console.error("无法找到ID为 'TableNotesAttach' 的元素。");
      return;
    }

    return {
      additionalInfo: {
        reportAttachment: getFormItems("TableNotesAttach", "isTable"),
        referenceInfo: getFormItems("TableReference", "isTable"),
      },
    };
  }

  // 最终生成的data对象
  const execDataMap = new Map([
    // 通用数据
    [
      "reportBaseInfo",
      {
        path: [`//*[@id='td_CF_Tab_1']`],
        checkId: "Gen_Info_Content_Div",
        event: collectGeneralTableData,
        isAsync: true,
      },
    ],
    // 患者数据
    [
      "reportSubjectInfo",
      {
        path: [`//*[@id='td_CF_Tab_2']`],
        checkId: "Pat_Info_Content_Div",
        event: collectPatientData,
      },
    ],
    // 产品数据
    [
      "试验用药",
      {
        path: [`//*[@id='td_CF_Tab_3']`],
        checkId: "div_tabs_CF_PRD",
        event: collectProductData,
        isAsync: true,
      },
    ],
    // 事件数据
    [
      "reportSaeDetailInfo",
      {
        path: [`//*[@id='td_CF_Tab_4']`, `//*[@id='td_CF_EVENT_1']`],
        event: collectEventData,
        checkId: "div_tabs_CF_EVT",
        isAsync: true,
      },
    ],
    // 事件评估数据
    [
      "药品和不良反应评价矩阵",
      {
        path: [`//*[@id='td_CF_Tab_4']`, `//*[@id='td_CF_EVENT_2']`],
        checkId: "Event_Assess_Table_Main",
        event: collectEventAssessmentData,
        isAsync: true,
      },
    ],
    // 分析
    [
      "saeDescribe",
      {
        path: [`//*[@id='td_CF_Tab_5']`],
        event: collectSaeDescribeData,
        checkId: "Narr_Content_Div",
      },
    ],
    // 附件信息
    [
      "additionalInfo",
      {
        path: [`//*[@id='td_CF_Tab_7']`],
        event: collectAdditionalInfoData,
        checkId: "TableNotesAttachDiv",
      },
    ],
  ]);

  const moduleKeys = [
    "reportBaseInfo",
    "reportSubjectInfo",
    "试验用药",
    "reportSaeDetailInfo",
    "药品和不良反应评价矩阵",
    "saeDescribe",
    "additionalInfo",
  ];

  let finallyData = {};
  // 从起始索引开始执行模块
  for (let i = 0; i < moduleKeys.length; i++) {
    const key = moduleKeys[i];
    const module = execDataMap.get(key);
    if (module) {
      let count = 0;
      while (!getElement(getDocument(), module.checkId)) {
        count++;
        for (let path of module.path) {
          triggerEvent(getElement(getDocument(), path), "click");
          await waitForPageReload(3000);
        }
        if(count > 3) break;
      }
      if (module.isAsync) {
        const data = await module.event();
        if (data) {
          finallyData = { ...finallyData, ...data };
        }
      } else {
        finallyData = { ...finallyData, ...module.event() };
      }
    } else {
      console.log(`No found for module key "${key}".`);
    }
  }
  return finallyData;
}


window.addEventListener("message", async function (event) {
  if (event.data.type === "reportQCDataExec") {    
    console.log('QC执行');
    
    const data = await report_QC()
    window.postMessage(
      { type: "reportQCData", data: data },
      window.location.origin
    );
  }
});
