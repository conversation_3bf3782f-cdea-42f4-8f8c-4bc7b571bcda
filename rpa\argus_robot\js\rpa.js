(async () => { 
  // 获取当前脚本的基础URL
  const getBaseUrl = () => {
    const currentScript =
      document.currentScript || document.querySelector(`script[src*="rpa.js"]`);
    if (!currentScript) {
      throw new Error("无法找到当前脚本元素");
    }
    return new URL(".", currentScript.src).href;
  };
  
  if(window.__RPA_VERSION){
    return;
  }
  window.__RPA_VERSION = 'R' + Date.now();

  if(location.pathname.indexOf('DocViewer.aspx') >= 0){
    return;
  }

  await import(`${getBaseUrl()}rpa-main.js?v=${window.__RPA_VERSION}`);
 
  if(location.origin === 'https://daers.adrs.org.cn'){ 
    await import(`${getBaseUrl()}rpa-DataFeedback.js?v=${window.__RPA_VERSION}`);
  }
  
  await import(`${getBaseUrl()}argus-plus.js?v=${window.__RPA_VERSION}`); 

})();
