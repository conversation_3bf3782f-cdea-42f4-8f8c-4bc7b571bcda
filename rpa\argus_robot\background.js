
async function reloadPages(){
  const tabs = await chrome.tabs.query({});
  for (let index = 0; index < tabs.length; index++) {
    const tab = tabs[index];
    if(!(await isUrlAllowed(tab.url))){
      continue;
    }
    chrome.tabs.reload(tab.id);
  }
}

chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.action === "reloadPages") {
    reloadPages();
    return;
  } 

  if (message.action === "strucutrueData") {
    // 从存储中获取激活插件的网页Tab ID，并将数据发送回去
    chrome.storage.local.get("activeTabId", function (items) {
      if (items.activeTabId) {
        chrome.tabs.sendMessage(items.activeTabId, {          
          type: "BACKGROUND_RESPONSE",
          data: {
            type: "strucutrueData",
            data: message.data,
            md5: message.md5,
          }
        });
      }
    });
  }

  if (message.action === "followUpstrucutrueData") {
    // 从存储中获取激活插件的网页Tab ID，并将数据发送回去
    chrome.storage.local.get("activeTabId", function (items) {
      if (items.activeTabId) {
        chrome.tabs.sendMessage(items.activeTabId, {
          type: "BACKGROUND_RESPONSE",
          data: {
            type: "followUpstrucutrueData",
            data: message.data,
            md5: message.md5,
          }
        });
      }
    });
  }

  if (message.action === "getEnvironmentConfig") {
    chrome.storage.local.get(['selectedEnvironment', 'environmentUrl'], (result) => {
      sendResponse({
        urls: environmentUrls,  
        savedEnv: result.selectedEnvironment,
        savedUrl: result.environmentUrl
      });
    });
    return true;
  }
});

// 使用 Fetch API
async function makeHttpRequest(url, options, body, headers = {}) {
  try{    
    const response = await fetch(url, {
      headers: {
        "Content-Type": "application/json",
        ...headers,
      },
      ...options,
      cache: "no-cache",
      body: body, // 仅在非 GET 请求时设置 body
    });
    const data = await response.json();
    return {
      success: true,
      data: data,
    } 

  }catch(error){
    return {
      success: false,
      error: error.toString(),
    }
  }
}

async function makeFileHttpRequest(url, options, body, headers = {}) {
  try{    
    const response = await fetch(url, {
      headers: {
        "Content-Type": "application/octet-stream",
        ...headers,
      },
      ...options,
      cache: "no-cache",
      body: body, // 仅在非 GET 请求时设置 body
    });
    if (response.headers.get("content-type").includes("application/json")) {
      return { success: false, data: "文件不存在" };
    }

    const filename = response.headers.get("content-disposition")?.split("filename=")?.[1]
    const blob = await response.blob();

    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.addEventListener("error", () => {
        reject(new Error("Failed to read blob "+ filename));
      });
  
      reader.addEventListener("load", () => {
        resolve({ success: true, data: reader.result, fileName: filename });
      }); 
      reader.readAsDataURL(blob); // 将 Blob 转换为 Data URL 
    });

  }catch(error){
    return {
      success: false,
      error: error.toString(),
    }
  } 
}
 

// 监听所有请求的开始阶段
chrome.webRequest.onBeforeRequest.addListener(
  function (details) {
    // 获取所有的标签页
    chrome.tabs.query({}, function (tabs) {
      tabs.forEach(function (tab) {
        // 向每个标签页的 content.js 发送请求信息
        chrome.tabs.sendMessage(tab.id, {
          type: "BACKGROUND_RESPONSE",
          data: {
            type: "requestInfo",
            url: details.url,
          }
        });
      });
    });
  },
  {
    urls: [
      "*://*/oam/server/auth_cred_submit",
      "*://*/Login/Login.asp",
      "*://*/Aris/ArisLogOnServlet*",
    ],
  },
  []
);


const environmentUrls = {
  local: 'http://localhost:3866',  
  test: 'https://copilot-test.pharmaronclinical.com',    
  uat: 'https://copilot-uat.pharmaronclinical.com'    
};

const defaultEnv = 'uat'; 

const getEnvironment = async () => {
  const result = await chrome.storage.local.get(['selectedEnvironment', 'environmentUrl']); 
  if(result.environmentUrl){
    return {
      env: result.selectedEnvironment,
      url: result.environmentUrl 
    }
  }else{
    return {
      env: defaultEnv,
      url: environmentUrls[defaultEnv] 
    }
  }
}


// 更新CSP移除规则
async function updateCspRemovalRules(allowedUrls) {
  // 将URL patterns转换为规则
  const rules = allowedUrls.map((pattern, index) => ({
    id: index + 1, // 规则ID从1开始
    priority: 1,
    action: {
      type: 'modifyHeaders',
      responseHeaders: [
        { header: 'content-security-policy', operation: 'remove' },
        { header: 'content-security-policy-report-only', operation: 'remove' }
      ]
    },
    condition: {
      urlFilter: pattern,
      resourceTypes: ['main_frame', 'sub_frame', 'stylesheet', 'script', 'image', 'font', 'object', 'xmlhttprequest', 'ping', 'csp_report', 'media', 'websocket', 'webtransport', 'webbundle']
    }
  }));

  // 更新动态规则
  try {
    await chrome.declarativeNetRequest.updateDynamicRules({
      removeRuleIds: rules.map(rule => rule.id), // 移除旧规则
      addRules: rules // 添加新规则
    });
  } catch (error) {
    console.error('Error updating CSP removal rules:', error);
  }
}

// 从服务器获取允许的URL patterns，并缓存结果
async function fetchAllowedUrls() {
  // 尝试从缓存获取
  const cache = await chrome.storage.local.get('allowedUrlsCache');
  if (cache.allowedUrlsCache) {
    const { data, timestamp } = cache.allowedUrlsCache;
    // 检查缓存是否在10分钟内
    if (Date.now() - timestamp < 10 * 60 * 1000) {
      return data;
    }
  }

  // 缓存不存在或已过期，从服务器获取
  const env = await getEnvironment();
  try {
    const response = await fetch(env.url + '/pv-manus-front/conf/allow_url.json');
    const data = await response.json();
    const matches = data.matches || [];
    
    // 更新缓存
    await chrome.storage.local.set({
      allowedUrlsCache: {
        data: matches,
        timestamp: Date.now()
      }
    });

    // 更新CSP移除规则
    await updateCspRemovalRules(matches);
    
    return matches;
  } catch (error) {
    console.error('Error fetching allowed URLs:', error);
    return [];
  }
}

// 检查URL是否匹配pattern (支持通配符)
function matchesPattern(url, pattern) {
  // 将通配符pattern转换为正则表达式
  const regexPattern = pattern
    .replace(/\./g, '\\.')
    .replace(/\//g, '\\/')
    .replace(/\*/g, '.*');
  
  const regex = new RegExp(`^${regexPattern}$`);
  return regex.test(url);
}

// 检查当前URL是否在允许列表中
async function isUrlAllowed(url) { 
  const allowedPatterns = await fetchAllowedUrls(); 
  console.log('allowedPatterns :>> ', allowedPatterns);
  return allowedPatterns.some(pattern => matchesPattern(url, pattern));
} 



(function () {
  // 在插件初始化时获取允许的URLs
  fetchAllowedUrls().catch(error => {
    console.error('Error fetching allowed URLs on init:', error);
  });

  // 注入脚本的函数
  async function injectContentScript(tabId, url) {
    const env = await getEnvironment();
    try {
      if (await isUrlAllowed(url)) {
        const result = await chrome.scripting.executeScript({
          target: { tabId: tabId },
          func: () => { 
            return !!document.querySelector('#pvCopilotPanel');
          }
        }); 
        const loaded = result && result[0] && result[0].result === true;
        if(loaded) return;
        console.log(`Injecting script to tab ${tabId} for URL: ${url}`);
        // 注入脚本
        await chrome.scripting.executeScript({
          target: { tabId },
          files: ['js/content_bridge.js']
        }); 

        await chrome.scripting.executeScript({
          target: { tabId },
          world: 'MAIN',
          args: [env.url],
          func : (url) => {
            var script = document.createElement('script'); 
            script.src = `${url}/pv-manus-front/js/rpa.js?_v=${Date.now()}`; 
            document.head.appendChild(script); 
          }, 
        }); 

         
      }
    } catch (err) {
      console.error(`Error injecting script to tab ${tabId}:`, err);
    }
  }
  
  // 在tab更新时检查并注入content script
  chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
    // 检查URL是否存在
    if (!tab.url) return;
    
    // 当页面开始加载或URL发生变化时进行检查 
    if (changeInfo.status === 'complete' && tab.url) { 
      // 总是在页面加载阶段尝试注入脚本
      injectContentScript(tabId, tab.url);  
    }

    
    if (changeInfo.status === 'complete' && tab.url && !tab.url.startsWith('chrome://')) {
      injectPvToolsContentScript(tabId);
    }
  }); 
   
  // 在插件安装或更新时
  chrome.runtime.onInstalled.addListener(async (details) => {
    console.log("Extension Installed");

    const lastEnv = await getEnvironment();
    // 清除所有缓存
    await chrome.storage.local.clear();

    // 重新设置必要的配置
    await chrome.storage.local.set({
      selectedEnvironment: lastEnv.env,
      environmentUrl: lastEnv.url
    });
    await fetchAllowedUrls();

    // 清除 allowedUrlsCache
    console.log("Cleared allowedUrlsCache on install/update");

    if (details.reason === 'update') {
      reloadPages();
    }
  });

  // 浏览器启动时
  chrome.runtime.onStartup.addListener(async () => {
    console.log("Browser Started");
    await fetchAllowedUrls().catch(error => {
      console.error('Error fetching allowed URLs on browser startup:', error);
    });
  });

  chrome.runtime.onUpdateAvailable.addListener(() => {
    // 清除所有缓存，包括 allowedUrlsCache
    chrome.storage.local.clear(() => {
      console.log("All cache cleared on update available!");
    });
});
 


})();

function injectPvToolsContentScript(tabId) { 
  // 在页面中执行内容脚本来检查meta标签
  chrome.scripting.executeScript({
    target: { tabId: tabId },
    function: checkForMetaTag
  })
  .then(injectionResults => {
    const hasMetaTag = injectionResults[0].result;
    if (!hasMetaTag) {
      return;
    }

    // 注入脚本
    chrome.scripting.executeScript({
      target: { tabId },
      files: ['js/pv_tools_content.js']
    });

  })
  .catch(error => {
    console.error(`无法在标签页 ${tabId} 执行脚本: ${error}`);
  });
}

function checkForMetaTag() {
  const metaTag = document.querySelector('meta[name="PV_TOOLS"][content="true"]');
  return metaTag !== null;
}

async function captureFullPage(tabId, format) { 
  await chrome.debugger.attach({ tabId }, '1.3');
  try {

    // 先获取页面完整尺寸
    const metrics = await chrome.debugger.sendCommand({ tabId }, 'Page.getLayoutMetrics');
    const width = metrics.contentSize.width;
    const height = metrics.contentSize.height;
    // 设置视口大小为完整页面大小
    await chrome.debugger.sendCommand({ tabId }, 'Emulation.setDeviceMetricsOverride', {
      width: width,
      height: height,
      deviceScaleFactor: 1,
      mobile: false
    });
    // 截图
    const result = await chrome.debugger.sendCommand({ tabId }, 'Page.captureScreenshot', {
      format: format,
      captureBeyondViewport: true,
    });
    // 恢复原始视口设置
    await chrome.debugger.sendCommand({ tabId }, 'Emulation.clearDeviceMetricsOverride');
 
     
    const dataUrl = `data:image/${format};base64,${result.data}`;
    
    return dataUrl;
  } finally {
    // 4. 断开debugger连接
    await chrome.debugger.detach({ tabId });
  }
}

 
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.type === 'BRIDGE_REQUEST') {
    handleBridgeRequest(message, sender).then(sendResponse);
    return true; // 保持消息通道开放
  }
});

// 存储URL请求监听器的Map，键为监听器ID，值为监听器引用
const urlRequestListeners = new Map();

async function handleBridgeRequest(message, sender) {
  const { action, params } = message; 
  
  switch (action) {
    case 'openTab':{
      const tab = await chrome.tabs.create({ url: params.url });
      return { tabId: tab.id };
    }
    case 'echo':
      return { ...params };
      
    case 'makeHttpRequest':
      return await makeHttpRequest(params.url, params.options, params.body, params.headers);
      
    case 'makeFileHttpRequest':
      return await makeFileHttpRequest(params.url, params.options, params.body, params.headers);

    case 'ifInterfaceReqCompleted':
      // 监听所有请求的开始阶段
      chrome.webRequest.onCompleted.addListener(
        function (details) {
          // console.log(details);
        },
        {
          urls: [`*://*${params.reqUrl}`],
        },
        []
      );
      return {};
      
    case 'getEnvironment':  
      return await getEnvironment();

    case 'getManifest':  
      return chrome.runtime.getManifest()

    case 'setUserId':
      await chrome.storage.local.set({ userId: params.hash });
      return {};

    case 'getUserId':
      return await chrome.storage.local.get('userId');

    case 'getTabs':{      
      const tabs = await chrome.tabs.query({url: params.url});
      const urlInfos = tabs.map((tab) => tab.url);
      return { data: urlInfos } 
    }
    case 'openRecognition': {
      const env = await getEnvironment();
      const tab = await chrome.tabs.create({
        url: env.url + '/pv-manus-front/html/recognition.html' + params.search, 
      });
      await chrome.storage.local.set({
        activeTabId: sender.tab.id,
        newTabId: tab.id,
      });
      return {};
    }
    
    case 'captureScreenshot': {
      const tabId = params.tabId || sender.tab.id;
      const format = params.format || 'png';
      const quality = params.quality || 80;
      
      try {
        // 捕获整个可见标签页
        const dataUrl = await captureFullPage(tabId, format);
        
        // 如果指定了元素选择器，则裁剪图像
        if (params.selector) {
          // 在目标标签页中执行脚本获取元素尺寸和位置
          const elementInfo = await chrome.scripting.executeScript({
            target: { tabId: tabId },
            function: (selector) => {
              const element = document.querySelector(selector);
              if (!element) return null;
              
              const rect = element.getBoundingClientRect();
              return {
                left: rect.left,
                top: rect.top,
                width: rect.width,
                height: rect.height,
                devicePixelRatio: window.devicePixelRatio || 1
              };
            },
            args: [params.selector]
          });
          
          // 检查元素是否存在
          const info = elementInfo[0]?.result;
          if (!info) {
            return { success: false, error: '未找到指定元素' };
          } 

          function dataURLToBlob(dataURL) {
            // 提取 MIME 类型和 base64 编码数据
            const parts = dataURL.split(';base64,');
            if (parts.length !== 2) {
              throw new Error('Invalid Data URL format');
            }
            
            const contentType = parts[0].split(':')[1];
            const raw = atob(parts[1]);
            const rawLength = raw.length;
            
            // 将解码后的数据转换为字节数组
            const uInt8Array = new Uint8Array(rawLength);
            for (let i = 0; i < rawLength; ++i) {
              uInt8Array[i] = raw.charCodeAt(i);
            }
            
            // 创建正确的 Blob
            return new Blob([uInt8Array], { type: contentType });
          }
          // 使用OffscreenCanvas进行图像处理
          const bitmap = await createImageBitmap(dataURLToBlob(dataUrl)) 
          // 创建OffscreenCanvas进行裁剪
          const canvas = new OffscreenCanvas(info.width * info.devicePixelRatio,
                                            info.height * info.devicePixelRatio);
          const ctx = canvas.getContext('2d');
          
          // 设置canvas大小为元素大小
          canvas.width = info.width * info.devicePixelRatio;
          canvas.height = info.height * info.devicePixelRatio;
          
          // 在canvas上绘制裁剪后的图像
          ctx.drawImage(
            bitmap,
            info.left * info.devicePixelRatio,
            info.top * info.devicePixelRatio,
            info.width * info.devicePixelRatio,
            info.height * info.devicePixelRatio,
            0, 0,
            info.width * info.devicePixelRatio,
            info.height * info.devicePixelRatio
          );
          const blob = await canvas.convertToBlob({type: `image/${format}`, quality }); 
          const croppedDataUrl = await new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onloadend = () => resolve(reader.result);
            reader.onerror = () => reject(new Error('Failed to convert blob to Data URL'));
            reader.readAsDataURL(blob);
          });
           
          return { success: true, dataUrl: croppedDataUrl };
        }
        
        return { success: true, dataUrl: dataUrl };
      } catch (error) {
        console.error(error.stack);
        return { success: false, error: error.toString() };
      }
    }
      
    // 添加其他API处理...
    
    default:
      throw new Error(`Unknown action: ${action}`);
  }
}
 