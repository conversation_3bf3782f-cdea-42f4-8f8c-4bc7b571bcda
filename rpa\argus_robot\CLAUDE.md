# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Common Commands

### Linting
```bash
npm run lint
```
Runs ESLint on `js/rpa.js` specifically.

### Development Server
For the followup-diff tool:
```bash
cd followup-diff
npm install express cors
node server.js
```
Starts development server on http://localhost:3001/followup-diff/index.html

## Architecture Overview

### Chrome Extension Structure
This is a **Chrome Extension** (manifest v3) called "Argus助手" that assists with data entry workflows in clinical trial systems. The extension architecture follows a standard Chrome extension pattern:

- **Background Script**: `background.js` - Service worker handling message passing and tab management
- **Content Scripts**: `js/content.js`, `js/rpa.js` - Injected into web pages for DOM manipulation
- **Popup Interface**: `html/popup.html` - Extension popup with action buttons
- **Web-accessible Resources**: Multiple HTML pages and JS modules loaded dynamically

### Key Components

#### RPA (Robotic Process Automation) System
- **Entry Point**: `js/rpa.js` - Dynamically imports modules based on URL patterns
- **Core Logic**: `js/rpa-main.js` - Main RPA functionality
- **Data Processing**: `js/rpa-DataFeedback.js` - Handles data feedback for specific domains
- **Utilities**: `js/rpa-util.js`, `js/rpa-fill-util.js` - Helper functions

#### Multi-purpose HTML Pages
- `html/recognition.html` - Data recognition interface
- `html/followup.html` - Follow-up data entry
- `html/question.html` - Question handling interface
- `html/navigation.html` - Navigation assistance
- `html/quickLogin.html` - Quick login functionality

#### Followup-Diff Tool
Standalone Alpine.js application for clinical data comparison:
- **Location**: `followup-diff/` directory
- **Purpose**: Three-stage data comparison (Initial Report → Followup Report → Entered Data)
- **Tech Stack**: Alpine.js + Bootstrap
- **Key Files**:
  - `index.html` - Main interface
  - `js/followup-diff.js` - Alpine.js application logic
  - `js/data-compare.js` - Deep object comparison algorithms
  - `js/key-generator.js` - Smart primary key generation for array data

### Domain-Specific Logic
The extension operates on specific clinical trial platforms:
- **Argus Safety**: pharmaronclinical.com domains
- **Data Feedback**: daers.adrs.org.cn domain
- **Environment Detection**: Automatically detects UAT vs production environments
- **URL Patterns**: Configured in `allow_url.json` and `conf/allow_url.json`

### Extension Permissions
Requires extensive permissions for web automation:
- `<all_urls>` - Access to all websites
- `debugger`, `webNavigation`, `scripting` - Web page manipulation
- `storage`, `tabs`, `activeTab` - State management and tab control

### Data Flow
1. **Content Script Injection**: Based on URL patterns, appropriate modules are loaded
2. **Background Communication**: Service worker handles cross-tab messaging
3. **DOM Manipulation**: Content scripts interact with clinical trial forms
4. **Data Processing**: Structured data extraction and transformation
5. **UI Enhancement**: Dynamic interface elements for improved user experience

### Development Notes
- **Version Control**: Uses timestamp-based versioning for cache busting
- **Module Loading**: Dynamic ES6 imports with version parameters
- **Configuration**: JSON-based configuration files for URLs and settings
- **Memory Management**: Detailed tracking in `memory-bank/` directory with context and decision logs