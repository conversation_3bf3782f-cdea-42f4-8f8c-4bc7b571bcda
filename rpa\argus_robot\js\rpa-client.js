
export class RpaClient {
  constructor() {
    this.requestId = 0;
    this.callbacks = new Map();    
    this.listeners = new Map();
    this.init();
  }

  init() {
    // 监听bridge的响应
    window.addEventListener("message", (event) => {
      if (event.data.type === "BRIDGE_RESPONSE") {
        this.handleBridgeResponse(event.data);
      }
      if (event.data.type === "BACKGROUND_EVENT") {
        this.handleBackgroundEvent(event.data);
      }
    });
  }

  async sendRequest(action, params) {
    const requestId = ++this.requestId;

    return new Promise((resolve, reject) => {
      this.callbacks.set(requestId, { resolve, reject });

      window.postMessage(
        {
          type: "RPA_REQUEST",
          requestId,
          action,
          params,
        },
        "*"
      );
    });
  }

  handleBridgeResponse(response) {
    const callback = this.callbacks.get(response.requestId);
    if (callback) {
      if (response.error) {
        callback.reject(new Error(response.error));
      } else {
        callback.resolve(response.data);
      }
      this.callbacks.delete(response.requestId);
    }
  }
  on(type, callback) {
    if (this.listeners.has(type)) {
      this.listeners.get(type).push(callback);
    } else {
      this.listeners.set(type, [callback]);
    } 
  }
  off(type, callback) {
    if (this.listeners.has(type)) {
      const callbacks = this.listeners.get(type);
      const index = callbacks.indexOf(callback);
      if (index !== -1) {
        callbacks.splice(index, 1);
      }
    }
  }
  handleBackgroundEvent(event) {
    const callbacks = this.listeners.get(event.data.type);
    if(!callbacks ||callbacks.length === 0) {
      return;
    }
    callbacks.forEach((callback) => {
      callback(event.data);
    });
    // 处理来自background的事件
    // console.log("Background event:", event.data);
  }
}

export const getBaseUrl = async () => {
  const env = await rpaClient.sendRequest('getEnvironment',{});
  return env.url;
}
 
// 初始化RPA客户端
export const rpaClient = new RpaClient();