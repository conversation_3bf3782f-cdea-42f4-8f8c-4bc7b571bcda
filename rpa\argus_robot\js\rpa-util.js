const {
  rpaClient,getBaseUrl
} = await import(`./rpa-client.js?v=${window.__RPA_VERSION}`);

export function waitForIframeLoad(iframeDom, timeout = 0) {
  return new Promise((resolve, reject) => {
    iframeDom.onload = function (){
      resolve(iframeDom)
    }
    if (timeout > 0) {
      setTimeout(() => {
        iframeDom.onload = () => {};
        reject(new Error('iframe onload timeout'));
      }, timeout);
    }
  });
}

export function waitForPostMessage(options = {}) {
  const {
      source = window,
      origin = '*',
      timeout = 0,
      action = '',
      type = '',
  } = options;
  return new Promise((resolve, reject) => {
      const handler = (event) => {
          // 验证来源
          if (origin !== '*' && event.origin !== origin) return;
           
          if(event.data.action !== action &&  event.data.type !== type) return; 
          // 移除监听器
          source.removeEventListener('message', handler);
          
          // 解析 Promise
          resolve(event);
      };
      // 添加监听器
      source.addEventListener('message', handler);
      // 可选：超时处理
      if (timeout > 0) {
          setTimeout(() => {
              source.removeEventListener('message', handler);
              reject(new Error('PostMessage timeout'));
          }, timeout);
      }
  });
}


 
export function onPageReady(callback) {
  // 如果文档已经完成加载，直接执行回调
  if (
    document.readyState === "complete" ||
    document.readyState === "interactive"
  ) {
    // 将回调函数推入事件循环末尾执行，确保执行顺序
    setTimeout(callback, 1);
    return;
  }
  // 如果文档尚未加载完成，监听 DOMContentLoaded 事件
  document.addEventListener("load", function () {
    callback();
  });
}


// 检查扩展是否有效
export async function isExtensionValid() {
  const rev = await rpaClient.sendRequest('echo',{ txt: 'hello'});
  return rev.txt === 'hello';
}


// 辅助函数：将 Data URL 转换为 Blob
export function dataURLtoBlob(dataurl) {
  const arr = dataurl.split(",");
  const mime = arr[0].match(/:(.*?);/)[1];
  const bstr = atob(arr[1]);
  let n = bstr.length;
  const u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  return new Blob([u8arr], { type: mime });
}


// 下载插件
export async function downloadPlugins() {
  const baseUrl = await getBaseUrl();
  const response = await rpaClient.sendRequest("makeFileHttpRequest",{
    url: `${baseUrl}/base-server/plugin/download`,
    options: { method: "get" },
    headers: {
      argusUsername: localStorage.getItem("userName"),
      tenantId: localStorage.getItem("sysTenantId"),
      accessAddress: window.location.origin,
      userId: localStorage.getItem("userId"),
    },
  });
  if (response.success) {
    let a = document.createElement("a");
    let blob;
    try {
      // 使用 Data URL 创建 Blob
      blob = dataURLtoBlob(response.data);

      a.href = response.data;
      a.download = response.fileName;
      a.click();
      console.log("插件下载成功！");
    } catch (e) {
      console.log(e);
    } finally {
      blob = null;
      a = null;
    }
  } else {
    console.log("插件下载失败");
  }
}

export async function simulateFileUpload(filePath, fileName, fileInput) {
  if (!(await isExtensionValid())) {
    console.log("扩展上下文无效，等待重试..."); 
    throw new Error(`附件${filePath}` + "插件需要刷新");
  }
  const baseUrl = await getBaseUrl();

  const response = await rpaClient.sendRequest("makeFileHttpRequest", { 
    url: `${baseUrl}/base-server/autoInput/download`,
    options: { method: "post" },
    headers: {
      argusUsername: localStorage.getItem("userName"),
      tenantId: localStorage.getItem("sysTenantId"),
      accessAddress: window.location.origin,
      userId: localStorage.getItem("userId"),
    },
    body: JSON.stringify({ fileId: filePath }), // 仅在非 GET 请求时设置 body
  }); 
  
  if (response.success) {
    let blob = null;
    let file = null;
    let dataTransfer = null;
    try {
      // 使用 Data URL 创建 Blob
      blob = dataURLtoBlob(response.data);

      // 创建一个新的 File 对象
      file = new File([blob], fileName, { type: blob.type });

      // 创建一个 DataTransfer 对象并将文件添加到其中
      dataTransfer = new DataTransfer();
      dataTransfer.items.add(file);

      // 设置文件输入元素的文件列表
      fileInput.files = dataTransfer.files;

      // 触发文件输入元素的 change 事件以模拟用户选择文件
      const event = new Event("change", { bubbles: true });
      fileInput.dispatchEvent(event);

      console.log("File uploaded successfully."); 
    } catch (e) {
      console.log(e);
    } finally {
      blob = null;
      file = null;
      dataTransfer = null;
    }
  } else {
    throw new Error(`附件${filePath}` + (response?.data || "File uploaded error"));
  }
}


const EnMonth = [
  "JAN",
  "FEB",
  "MAR",
  "APR",
  "MAY",
  "JUN",
  "JUL",
  "AUG",
  "SEP",
  "OCT",
  "NOV",
  "DEC",
];
// 判断字段录入是否正确，将字段数据进行转换
// 转换数据函数 - 转换日期 - 字符转换为大写
export function transformDate(value) {
  value = (value + "")?.replaceAll("-", "/")?.replaceAll("/", "/");
  const dates = value.split("/");
  if (dates?.length > 1 && (dates?.[0] === "??" || /^\d+$/.test(dates?.[0]))) {
    function isEnDate(val) {
      // 是英文格式，例如日-月-年或??-???-YYYY
      if (val?.[2]?.split(" ")?.[0]?.length === 4) {
        const month = ["??", "???"].includes(dates?.[1])
          ? `${dates?.[1]}/`
          : EnMonth.includes(dates?.[1])
          ? dates?.[1] + "/"
          : (/^\d+$/.test(dates?.[1])
              ? EnMonth[+dates?.[1] - 1]
              : dates?.[1].toUpperCase()) + "/";
        const year = ["00:00", "00"].includes(dates?.[2]?.split(" ")?.[1])
          ? dates?.[2]?.split(" ")?.[0]
          : dates?.[2];
        return dates?.[2]?.length >= 4
          ? (dates?.[0] ? dates?.[0] + "/" : "/") + month + year
          : value;
      } else {
        const year = ["00:00", "00"].includes(dates?.[2]?.split(" ")?.[1])
          ? dates?.[2]?.split(" ")?.[0]
          : dates?.[2];
        return (
          (dates?.[0] ? dates?.[0] + "/" : "/") +
          (dates?.[1] ? dates?.[1] + "/" : "/") +
          year
        );
      }
    }
    return isEnDate(dates);
  }
  if (/[A-Za-z]/.test(value)) {
    return value.toUpperCase();
  }
  return value;
}



export function isExceedsLength(element, key, value) {
  const maxAttr = element.getAttribute("maxlength");
  const maxLength = maxAttr ? parseInt(maxAttr, 10) : null;
  if (
    maxLength !== null &&
    !isNaN(maxLength) &&
    (value + "")?.length > maxLength
  ) {
    throw `The length of the field ${key}:${value} exceeds the maxlength of ${maxLength}`;
  }
}

const excludeFoundDomList = [
  "protocalNumber",
  "reportLanguage",
  "isDead",
  "Subject's Primary Disease",
];

// 获取页面fm_MainFrame元素
let documentMainFrame = null;
export const mainAppId = "fm_MainApp";
export const mainFrameId = "fm_MainFrame";

const iframeDialog = "#iframeDialog";
const Loading = "loading";
const Gloading = "gloading";
const LoadingContainer = "loadingContainer";

const uselessFields = ["attachmentUrl", "attachmentName"];

// 循环录入三次，录入是否录入成功，不成功则报错，录入失败
export async function multipleFillValue(key, value, doc) {
  let element;
  try {
    element = await waitDom(getDocument(doc), key);
    if (!element) return;
    if (/^TableNotesAttach_\d+_notes$/.test(key)) {
      if (value.endsWith(".eml")) value = value + ".doc";
      if (value.endsWith(".rar")) value = value + ".zip";
    }
    await fillField(key, value, doc);
    await waitForPageReload(500);
    // 录入失败，试验次数
    let count = 0;
    if (/Fol_Table_(.*)_amendment/.test(key)) {
      if (value != 2) return;
      while (element?.checked === (value === 2)) {
        await loopExec(key, value, doc, count);
        count++;
      }
      return;
    }
    if (
      key.endsWith("nfdiv") ||
      key.endsWith("nfddl") ||
      excludeFoundDomList.includes(key) ||
      key.endsWith("lltname") ||
      element.type === "radio" ||
      key === "dose_description"
    )
      return;
    async function loopExec(key, value, doc, count) {
      if (count > 2) {
        throw `Failed to set field ${key} to ${value}`;
      }
      await simulateSingleInput(doc, key, value);
      await waitForPageReload(2000);
      element = await waitDom(getDocument(doc), key);
    }
    if (
      key.includes("country") ||
      [
        /^TheContactLog_\d+_contact_date$/,
        /^TheContactLog_\d+_contact_date_sent$/,
      ].some((pattern) => pattern.test(key))
    ) {
      while (!element?.value) {
        await loopExec(key, value, doc, count);
        count++;
      }
      return;
    }
    while (
      !element?.value ||
      transformDate(element?.value) != transformDate(value)
    ) {
      await loopExec(key, value, doc, count);
      count++;
    }
  } finally {
    element = null;
  }
}
// 字段直接全部赋值
export async function fillField(key, value, doc) {
  let element = await waitDom(getDocument(doc), key);
  if (element) {
    if (/Fol_Table_(.*)_amendment/.test(key)) {
      element.checked = value === "2";
      return;
    }
    if (element.type === "checkbox") {
      if (element.checked !== (value === "1")) {
        element.click();
      }
    } else if (element.type === "radio" || key.endsWith("_nfdiv")) {
      // radio或者nullflavor字段的后缀
      element.click();
    } else {
      // 相关病史的字段也需要用鼠标选中 并模拟键盘输入才能生效
      if (key.endsWith("pat_hist_type")) {
        await simulateInput(doc, key, value);

        await waitForPageReload(3000);
      } else {
        isExceedsLength(element, key, value);
        element.value = value;
      }

      triggerEvent(element, "input");
      triggerEvent(element, "change");
      triggerEvent(element, "blur");
    }
    doc = null;
    element = null;
    console.log(`Set field ${key} to ${value}`);
  } else if (
    !(
      excludeFoundDomList.includes(key) ||
      key.endsWith("_lltname") ||
      /^[0-9]+$/.test(key)
    )
  ) {
    console.log(`Element with ID or XPath ${key} not found`);
  }
}


// 双语需要录入的字段值
const fieldBilingualInput = [
  "center_name",
  "rep_institution",
  "rep_suffix",
  "rep_first_name",
  "rep_last_name",
  "rep_department",
  "rep_city",
  "rep_state",
  "sc_other_text",
  "narrative",
  "company_comment",
  "product_name",
  "labtestreptd",
  "labresult",
  "labnotes",
  "comments",
  "desc_reptd",
  "labtestlow",
  "labtest",
  "Fol_Table_{index}_justification",
  "Ind_Table_{index}_ind_reptd",
  "CSDD_{index}_cause_reptd",
  "TableNotesAttach_{index}_notes",
  "{prefix}_{index}_pat_hist_rptd",
  "{prefix}_{index}_pat_hist_notes",
  "TableReference_{index}_ref_notes",
  "TheContactLog_{index}_contact_description",
  "dose_description",
];

// 判断双语录入中文时字段是否需要录入，字典字段、日期字段等则不需要录入
export function checkFieldBilingualInput(key) {
  if (fieldBilingualInput.includes(key)) return true;
  // 定义正则表达式模式，用于匹配动态字段
  const dynamicPatterns = [
    /^Fol_Table_\d+_justification$/,
    /^Ind_Table_\d+_ind_reptd$/,
    /^CSDD_\d+_cause_reptd$/,
    /^TableNotesAttach_\d+_notes$/,
    /^(?:Rel_Hist_Table|CPH)_\d+_pat_hist_rptd$/,
    /^(?:Rel_Hist_Table|CPH)_\d+_pat_hist_notes$/,
    /^TableReference_\d+_ref_notes$/,
  ];
  // 检查字段是否符合动态字段的正则表达式模式
  if (dynamicPatterns.some((pattern) => pattern.test(key))) return true;
  if (fieldBilingualInput.find((item) => key.includes(item))) return true;
}

// 筛选是否需要录入
export async function fillSingleField(key, value, doc, bilingual) {
  if (!checkFieldBilingualInput(key) && bilingual) return;

  if (key === "study_num" || key === "generic_name" || key === "product_name")
    return;

  if (!value || value === "" || uselessFields.includes(key)) {
    return;
  }

  await multipleFillValue(key, value, doc);
}
// 根据name标签获取页面元素
export function getDocumentByName(ifName) {
  return document
    .getElementById(mainAppId)
    ?.contentDocument?.getElementsByName(ifName)?.[0]?.contentDocument;
}
// 清除字段值
export async function clearSingleField(key, doc) {
  if (!key || key === "") {
    return;
  }
  let element = await waitDom(getDocument(doc), key);
  doc = null;
  if (element) {
    if (element.type === "checkbox") {
      element.checked = "";
    } else if (element.type === "radio" || key.endsWith("_nfdiv")) {
      // radio或者nullflavor字段的后缀
      element.click();
    } else {
      element.value = "";
      triggerEvent(element, "input");
      triggerEvent(element, "change");
      triggerEvent(element, "blur");
    }
    console.log(`Set field ${key} to ''`);
    element = null;
  } else {
    console.log(`Element with ID or XPath ${key} not found`);
  }
}
// 元素触发事件
export function triggerEvent(element, eventType) {
  return new Promise((resolve) => {
    if (!element) {
      resolve();
      element = null;
      return;
    }

    const event = new Event(eventType, {
      bubbles: true,
    });

    let resolved = false;

    const timeoutId = setTimeout(() => {
      if (!resolved) {
        console.log(`Event ${eventType} not triggered, resolving anyway.`);
        resolved = true;
        resolve();
      }
    }, 100);

    element.addEventListener(eventType, function handler() {
      if (!resolved) {
        clearTimeout(timeoutId);
        element.removeEventListener(eventType, handler);
        resolved = true;
        resolve();
        element = null;
      }
    });

    element?.dispatchEvent(event);
  });
}
// 判断是否是xPath
export function isXPath(key) {
  return key?.startsWith("//") || key?.startsWith("(//");
}

export function monitorNetworkRequest(timeout = 10) {
  return new Promise((resolve,reject) => {
    let pendingRequests = 0; // 用于跟踪未完成的请求数量
    let timeoutId; // 用于存储超时定时器的 ID

    // 劫持 XMLHttpRequest 的 open 方法
    const originalXHROpen = XMLHttpRequest.prototype.open;
    XMLHttpRequest.prototype.open = function () {
      // 在请求状态改变时触发的事件监听器
      this.addEventListener(
        "readystatechange",
        function () {
          if (this.readyState === 4) {
            // 请求完成
            pendingRequests--; // 减少未完成请求计数
            checkAllRequestsComplete(); // 检查所有请求是否已完成
          }
        },
        false
      );
      pendingRequests++; // 增加未完成请求计数
      clearTimeout(timeoutId); // 清除超时定时器
      originalXHROpen.apply(this, arguments); // 调用原始的 open 方法
    };

    // 劫持 fetch 方法
    const originalFetch = window.fetch;
    window.fetch = function () {
      pendingRequests++;
      clearTimeout(timeoutId);
      return originalFetch
        .apply(this, arguments)
        .then((response) => {
          pendingRequests--;
          checkAllRequestsComplete();
          return response;
        })
        .catch((error) => {
          pendingRequests--;
          checkAllRequestsComplete();
          throw error;
        });
    };

    // 劫持 $.ajax 方法
    // const originalAjax = $.ajax;
    // $.ajax = function(options) {
    //     pendingRequests++;
    //     clearTimeout(timeoutId);

    //     return originalAjax(options)
    //         .then(response => {
    //             pendingRequests--;
    //             checkAllRequestsComplete();
    //             return response;
    //         })
    //         .catch(error => {
    //             pendingRequests--;
    //             checkAllRequestsComplete();
    //             throw error;
    //         });
    // };

    // 检查是否所有请求都已完成
    function checkAllRequestsComplete() {
      if (pendingRequests === 0) {
        clearTimeout(timeoutId);
        resolve(true);
      }
    }

    // 设置超时
    timeoutId = setTimeout(() => {
      if (pendingRequests === 0) {
        resolve(true);
      } else {
        reject(new Error("请求超时"));
      }
    }, timeout);
  });
}
// 等待页面元素
export async function waitDom(dom, key, waitTime = 1000, className) {
  if (
    !key ||
    excludeFoundDomList.includes(key) ||
    key.endsWith("_lltname") ||
    /^[0-9]+$/.test(key)
  ) {
    return;
  }
  let i = 0;
  let result;
  while (true) {
    result = getElement(dom, key, className);
    if (result) {
      i++;
      break;
    } else {
      await wait(waitTime);
      i++;
    }
    if (i === 10) {
      console.log(`${key}元素查找不到`);
      break;
    }
  }
  return result;
}

export function wait(waitTime) {
  return new Promise((resolve) => {
    setTimeout(resolve, waitTime);
  });
}
// 页面加载等待加载加载元素加载完毕
export async function waitForPageReload(waitTime, key = mainFrameId) {
  let loadingDom;
  let GloadingDom;
  let LoadingContainerDom;
  try {
    const requestFinish = await monitorNetworkRequest(waitTime);
    // await wait(1000)
    loadingDom = getDocument()?.getElementById?.(Loading);
    GloadingDom = getDocument()?.getElementById?.(Gloading);
    LoadingContainerDom = getDocument()?.getElementById?.(LoadingContainer);
    if (loadingDom || GloadingDom || LoadingContainerDom) {
      const loadingStyle = window.getComputedStyle(loadingDom);
      const gloadingStyle = window.getComputedStyle(GloadingDom);
      const LoadingContainerStyle =
        window.getComputedStyle(LoadingContainerDom);

      if (
        loadingStyle?.display !== "none" ||
        gloadingStyle?.display !== "none" ||
        (LoadingContainerStyle?.display === "block" && !isDialog())
      ) {
        await waitForPageReload(waitTime);
        return;
      }
      if (isDialog()) {
        LoadingContainerDom = getDialog()?.getElementById?.(LoadingContainer);
        if (LoadingContainerDom) {
          const loadingMaskStyle = window.getComputedStyle(LoadingContainerDom);
          if (loadingMaskStyle?.display === "block") {
            await waitForPageReload(waitTime);
            return;
          }
        }
      }
      await waitForElement(key, waitTime);
      if (requestFinish && performance.timing.responseEnd) {
        return;
      } else {
        await waitForPageReload(waitTime, Loading);
      }
    } else {
      await waitForPageReload(waitTime, key);
    }
  } catch (e) {
    console.log("error", e.message);
  } finally {
    loadingDom = null;
    GloadingDom = null;
    LoadingContainerDom = null;
  }
}

export function getElement(doc, key, className) {
  try {
    return isXPath(key)
      ? (doc || getDocument())?.evaluate(
          key,
          doc || getDocument(),
          null,
          XPathResult.FIRST_ORDERED_NODE_TYPE,
          null
        )?.singleNodeValue
      : className
      ? (doc || getDocument())?.querySelector(className)
      : (doc || getDocument())?.getElementById(key);
  } finally {
    doc = null;
  }
}

/**
 * 等待指定的 DOM 元素出现
 * @param {String} key - 元素的 ID 或 XPath 表达式
 * @param {Number} [timeout=3000] - 超时时间，默认为 3000 毫秒
 * @returns {Promise} - 返回一个 Promise，当元素出现时解析，当超时或出错时拒绝
 */
export function waitForElement(key, timeout = 3000) {
  return new Promise((resolve, reject) => {
    const startTime = Date.now(); // 记录开始时间
    let element = null; // 用于存储找到的元素
    function checkElement() {
      // 获取元素
      element =
        key === mainFrameId ? getDocument() : getElement(getDocument(), key);
      if (element) {
        clearInterval(intervalId);
        resolve(true);
        element = null;
      } else if (Date.now() - startTime > timeout) {
        clearInterval(intervalId);
        reject(`Element of ${key} not found within timeout`);
      } else {
        // 如果未找到元素且未超时，则继续下一帧检查
        requestAnimationFrame(checkElement);
      }
    }
    // 使用 requestAnimationFrame 进行定时检查
    const intervalId = requestAnimationFrame(checkElement);
  });
}

export function getDocument(doc) {
  if (documentMainFrame) {
    documentMainFrame = null;
  }
  documentMainFrame = doc
    ? doc
    : document
        .getElementById(mainAppId)
        ?.contentDocument?.getElementById(mainFrameId)?.contentDocument;
  doc = null;
  return documentMainFrame;
}
/**
 * 获取主文档中的对话框节点列表
 * @returns {NodeList|null} 对话框节点列表或null
 */
export function getDialogNodes() {
  const mainDoc = document.getElementById(mainAppId)?.contentDocument;
  if (!mainDoc) return null;

  const nodes = mainDoc.querySelectorAll(iframeDialog);
  return nodes?.length ? nodes : null;
}
/**
 * 获取单个对话框文档
 * @param {string} [title] - 对话框标题（可选）
 * @returns {Document|null} 对话框文档或null
 */
export function getDialog(title) {
  const nodes = getDialogNodes();
  if (!nodes) return null;

  for (const node of nodes) {
    if (!node) continue;

    // 如果没有指定title，返回第一个有效的对话框
    if (!title) return node.contentDocument;

    // 如果指定了title，查找匹配的对话框
    const href = node.getAttribute("src");
    if (href?.includes(title)) return node.contentDocument;
  }

  return null;
}

/**
 * 检查是否存在对话框
 * @returns {boolean} 是否存在对话框
 */
export function isDialog() {
  try {
    return !!getDialogNodes();
  } catch {
    return false;
  }
}

/**
 * 获取所有匹配的对话框文档
 * @param {string} [title] - 对话框标题（可选）
 * @returns {Document[]|null} 对话框文档数组或null
 */
export function getDialogs(title) {
  const nodes = getDialogNodes();
  if (!nodes) return null;
  const result = [];
  for (const node of nodes) {
    if (!node) continue;
    // 如果没有指定title，收集所有对话框
    if (!title) {
      result.push(node.contentDocument);
      continue;
    }
    // 如果指定了title，只收集匹配的对话框
    const href = node.getAttribute("src");
    if (href?.includes(title)) {
      result.push(node.contentDocument);
    }
  }
  return result.length ? result : null;
}
// 关闭弹窗
export async function closeDialog(reportLocale = "cn") {
  await cancelDialog(reportLocale);
  await okDialog(reportLocale);
  // 处理弹出窗口没有关闭
}
// 判断事件评价的结果是否触发弹窗，触发后点击第一行关闭弹窗
export async function closeEventAssJust(reportLocale) {
  if (isDialog()) {
    await waitForPageReload(100);
    triggerEvent(await waitDom(getDialog(), "td1", 100), "click");
    await waitForPageReload(100);
    await clickFun("//*[@id='okbtn']", reportLocale);
  }
}
// 弹窗点击
export async function clickFun(path, reportLocale, title, isJust = true) {
  while (isDialog()) {
    let dialogTitle = getElement(getDialog(), "dialogTitle");
    if (
      dialogTitle?.innerHTML?.includes(
        reportLocale === "cn" ? "理由" : "Justification"
      ) &&
      isJust
    ) {
      await closeEventAssJust(reportLocale);
      dialogTitle = null;
    }
    let dom = await waitDom(getDialog(title), path, 100);
    if (dom) {
      triggerEvent(dom, "click");
      await waitForPageReload(200);
      dom = null;
      dialogTitle = null;
      return;
    } else {
      break;
    }
  }
}
// 点击确认按钮关闭弹窗
export async function okDialog(reportLocale) {
  // 处理弹出窗口没有关闭
  await clickFun(
    "//button[@value='" + (reportLocale === "cn" ? "确定" : "OK") + "']",
    reportLocale
  );
  await clickFun(
    "//input[@value='" + (reportLocale === "cn" ? "确定" : "OK") + "']",
    reportLocale
  );
  await clickFun("//input[@value='OK']", "Argus", reportLocale);
  await clickFun("//*[@id='btnOkYes']", reportLocale);
  await clickFun("//*[@id='okbtn']", reportLocale);
  await clickFun("//*[@id='btn_OK']", reportLocale);
}
// 点击取消按钮关闭弹窗
export async function cancelDialog(reportLocale) {
  // 处理弹出窗口没有关闭
  await clickFun(
    "//button[@value='" + (reportLocale === "cn" ? "取消" : "Cancel") + "']",
    reportLocale
  );

  // 处理弹出窗口没有关闭，科伦的中文whodrugcoding使用的是英文的控件，造成中英文的适配失效
  await clickFun("//button[@value='Cancel']", reportLocale, "Drug");

  // 处理弹出窗口没有关闭
  await clickFun("//*[@id='btnNo']", reportLocale);

  // 处理弹出窗口没有关闭
  await clickFun("//*[@id='bt_cancel']", reportLocale);

  await clickFun("//*[@id='btnOkYes']", reportLocale);

  await clickFun("//*[@id='btn_cancel']", reportLocale);
  await clickFun("//*[@id='btnCancel']", reportLocale);
}
// 循环录入三次，录入是否录入成功，不成功则报错，录入失败
export async function multipleSimulateInput(doc, key, value, bilingual) {
  if (!checkFieldBilingualInput(key) && bilingual) return;
  if (!value || value === "" || uselessFields.includes(key)) {
    return;
  }
  let element;
  try {
    element = await waitDom(getDocument(doc), key);
    if (!element) return;
    await simulateSingleInput(getDocument(doc), key, value);
    await waitForPageReload(500);
    if (
      key.endsWith("nfdiv") ||
      key.endsWith("nfddl") ||
      excludeFoundDomList.includes(key) ||
      key.endsWith("lltname") ||
      element.type === "radio"
    )
      return;
    async function loopExec(key, value, doc, count) {
      if (count > 2) {
        throw `Failed to set field ${key} to ${value}`;
      }
      await simulateSingleInput(doc, key, value);
      await waitForPageReload(2000);
      element = await waitDom(getDocument(doc), key);
    }
    // 试验用药编码选择失败，再次试验
    let count = 0;
    if (key.includes("country")) {
      while (!element?.value) {
        await loopExec(key, value, doc, count);
        count++;
      }
      return;
    }
    while (
      !element?.value ||
      transformDate(element?.value) != transformDate(value)
    ) {
      await loopExec(key, value, doc, count);
      count++;
    }
  } finally {
    element = null;
  }
}

export async function simulateSingleInput(doc, key, value) {
  let element;
  try {
    element = await waitDom(getDocument(doc), key);
    if (element) {
      element.scrollIntoView({
        behavior: 'auto',
        block: 'center',
        inline: 'center'
      });
      await wait(1);
      element.focus();      
      element.value = value;
      if(typeof element.ondblclick === 'function') {
        // 创建一个双击事件
        const doubleClickEvent = new MouseEvent("dblclick", {
          bubbles: true,
          cancelable: true,
          view: window,
        });
        element.dispatchEvent(doubleClickEvent);
        
        const listId = element.getAttribute('list');
        if(listId){
          await waitDom(getDocument(doc), listId);
        }else{
          await wait(1000);
        }
      }
      isExceedsLength(element, key, value);
      element.dispatchEvent(new Event("input", {
        bubbles: true,
        cancelable: true
      })); // 触发 input 事件以模拟真实输入后的效果      
      element.dispatchEvent(new Event("change", {
        bubbles: true,
        cancelable: true
      })); // 触发 change 事件以模拟真实输入后的效果
      
      await wait(10);
      element.blur();
      // 添加延迟以模拟真实的输入速度
      element.dispatchEvent(new Event("blur")); // 触发 blur 事件以模拟真实输入后的效果
      console.log(`Set field ${key} to ${value}`);
    } else {
      console.log(`Element with ID or XPath ${key} not found`);
    }
  } finally {
    element = null;
  }
}

// 模拟输入
export async function simulateInput(doc, key, value, bilingual, reportLocale) {
  if (!checkFieldBilingualInput(key) && bilingual) return;
  // 双语录入时手动输入的将不需要再次录入
  if (!value || value === "" || bilingual) {
    return;
  }

  if (key === "TXT_Class_Table_0_class_id") {
    if (value === "1") {
      triggerEvent(await waitDom(getDocument(doc), "btnAddClass"), "click");
      await simulateInput(
        getDocument(doc),
        "TXT_Class_Table_0_class_id",
        reportLocale === "en" ? "Non-valid" : "无效报告"
      );
      return;
    }
  }
  await multipleSimulateInput(doc, key, value, bilingual);
}
// 录入完毕判断是否跳转首页，否则即返回首页
export async function buttonNotSaved() {
  triggerEvent(getDocument().getElementById("Header:imgClose"), "click");
  await waitForPageReload(1000);
  await clickFun("//*[@id='btnNo']");
  await waitForPageReload(1000);
  while (isDialog()) {
    await closeDialog();
  }
}

// 附件上传文件
export async function uploadFile(filePath, fileName, index, reportLocale, taskId) {
  if (fileName.endsWith(".eml")) fileName = fileName + ".doc";
  if (fileName.endsWith(".rar")) fileName = fileName + ".zip";
  await waitForPageReload(300);
  if (isDialog()) {
    await closeDialog();
  }
  // 上传文件类型为eml的跳过
  if (fileName?.endsWith(".eml")) return;
  // 上传文件类型为rar的跳过
  if (fileName?.endsWith(".rar")) return;
  triggerEvent(getElement(getDocument(), `TableNotesAttach_${index}`), "click");
  await waitForPageReload(300);
  //点击添加附件
  triggerEvent(getElement(getDocument(), "NA_AttachFile"), "click");
  await wait(1000);
  if (
    getElement(getDialog(), "txtMessage")?.innerHTML?.includes(
      "Do you wish to overwrite the current attachment for the selected row?"
    )
  ) {
    triggerEvent(getElement(getDialog(), "btnOkYes"), "click");
    await wait(1000);
  }
  await wait(1000);
  let fileInput = await waitDom(
    getDialog(),
    "FileUploadControl_FileUploadCtrl",
    3000
  );
  while (!fileInput) {
    fileInput = await waitDom(
      getDialog(),
      "FileUploadControl_FileUploadCtrl",
      3000
    );
  }
  if (!fileInput || !fileName) {
    // 请求修改新建状态的接口
    await request("autoInput/updateStatus", "post", {
      taskId: taskId,
      [reportLocale === "en" ? "inputStatusEn" : "inputStatusCn"]: 3,
      message: "附件上传错误",
    });
    await buttonNotSaved();
    return;
  }

  // 判断是否上传文件成功
  async function judgeIsUpload(count, close) {
    if (count < 3) {
      if (getDialogs()?.length > 1) {
        for (let [i, item] of getDialogs().entries()) {
          let txtMessage;
          try {
            txtMessage = item.getElementById("PageName");
            await waitForPageReload(500);
            if (txtMessage?.innerHTML?.includes?.("File Upload Validation")) {
              triggerEvent(item.getElementById("btnOkYes"), "click");
            }
          } finally {
            txtMessage = null;
          }
        }
        count++;
        if (!close) {
          await simulateFileUpload(filePath, fileName, fileInput);
          triggerEvent(await waitDom(getDialog(), "btnOk"), "click");
        }
        await judgeIsUpload(count, close);
      }
    }
  }

  // 调用函数，传入本地文件路径
  await simulateFileUpload(filePath, fileName, fileInput);
  await waitForPageReload(3000);
  let count = 0;
  await judgeIsUpload(count);
  if (isDialog()) triggerEvent(await waitDom(getDialog(), "btnOk"), "click");
  await waitForPageReload(1000);
  await judgeIsUpload(count, "close");
  if (isDialog())
    triggerEvent(await waitDom(getDialog(), "btnCancel"), "click");
  await waitForPageReload(5000);
}



export async function request(url, method, data) { 
  if (!(await isExtensionValid())) {
    throw new Error("插件需要刷新");
  }
  const baseUrl = await getBaseUrl();
  // 如果是 GET 请求，将数据转换为查询参数
  if (method.toUpperCase() === "GET") {
    const queryParams = new URLSearchParams(data).toString();
    url += `?${queryParams}`;
  }
  if (!localStorage.getItem("sysTenantId")) {
    return;
  }
  const response = await rpaClient.sendRequest('makeHttpRequest',{
    url: `${baseUrl}/base-server/${url}`,
    options: { method: method },
    headers: {
      argusUsername: localStorage.getItem("argusUsername"),
      tenant_id: localStorage.getItem("sysTenantId"),
      tenantId: localStorage.getItem("sysTenantId"),
      app_id: localStorage.getItem("sysAppId"),
      appId: localStorage.getItem("sysAppId"),
      accessAddress: window.location.origin,
      user_id: localStorage.getItem("userId"),
      userId: localStorage.getItem("userId"),
    },
    body: method.toUpperCase() === "GET" ? null : JSON.stringify(data), // 仅在非 GET 请求时设置 body
  });
  if (!url.includes("autoInput/clientHeart")) {
    console.log(url, JSON.stringify(data), response.success);
  }
  if (response.data) {
    return response.data.data;
  } else {
    throw new Error(response.error);
  }
}

export  async function heartbeatRequest(taskId) {
  await request("autoInput/clientHeart", "get", {
    clientId: localStorage.getItem("userId"),
    taskId: taskId,
  });
}

export async function moduleLogs(logContent, depleteTime, taskId) {
  // 日志
  await request("autoInput/addLog", "post", {
    taskId: taskId,
    depleteTime: depleteTime,
    logContent: logContent,
  });
  heartbeatRequest(taskId);
}




class Timer {
  constructor() {
    this.startTime = 0;
    this.pausedTime = 0;
    this.isPaused = false;
    this.pauseStartTime = 0;
    
    // 监听页面可见性变化
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        this.pause();
      } else {
        this.resume();
      }
    });
  }
  start() {
    this.startTime = performance.now();
    this.pausedTime = 0;
    this.isPaused = false;
  }
  pause() {
    if (!this.isPaused) {
      this.isPaused = true;
      this.pauseStartTime = performance.now();
    }
  }
  resume() {
    if (this.isPaused) {
      this.isPaused = false;
      this.pausedTime += performance.now() - this.pauseStartTime;
    }
  }
  getElapsedTime() {
    const currentTime = performance.now();
    const totalTime = currentTime - this.startTime - this.pausedTime;
    return totalTime / 1000; // 返回秒数
  }
}
export const timer = new Timer();