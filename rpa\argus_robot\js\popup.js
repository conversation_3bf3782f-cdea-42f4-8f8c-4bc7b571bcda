document.addEventListener('DOMContentLoaded', function() {
    const environmentSelect = document.getElementById('environmentSelect');
    const environmentUrlInput = document.getElementById('environmentUrl');
    const saveButton = document.getElementById('saveButton');

    let environmentUrls = {}; // Will be populated from background script

    // Request configuration from background script
    chrome.runtime.sendMessage({ action: "getEnvironmentConfig" }, (response) => {
        if (chrome.runtime.lastError) {
            console.error("Error getting environment config:", chrome.runtime.lastError.message || chrome.runtime.lastError);
            // Handle error, maybe disable UI or show a message
            environmentSelect.disabled = true;
            environmentUrlInput.disabled = true;
            saveButton.disabled = true;
            // Optionally display an error message in the popup body
            document.body.insertAdjacentHTML('beforeend', '<p style="color: red;">无法加载配置。</p>');
            return;
        }

        if (response && response.urls) {
            environmentUrls = response.urls; // Store the URLs received from background
            const savedEnv = response.savedEnv;
            const savedUrl = response.savedUrl;

            // Populate dropdown options
            environmentSelect.innerHTML = ''; // Clear existing options
            for (const envKey in environmentUrls) {
                const option = document.createElement('option');
                option.value = envKey;
                // Simple capitalization for display
                option.textContent = envKey.charAt(0).toUpperCase() + envKey.slice(1);
                environmentSelect.appendChild(option);
            }

            // Set initial values based on saved data or defaults from config
            // Use 'local' as a fallback default if nothing is saved or saved is invalid
            const initialEnv = savedEnv && environmentUrls[savedEnv] ? savedEnv : 'local';
            if (environmentUrls[initialEnv]) { // Ensure the initialEnv exists in our config
                 environmentSelect.value = initialEnv;
                 // Use savedUrl if available, otherwise use the URL from config for the initialEnv
                 environmentUrlInput.value = savedUrl || environmentUrls[initialEnv];
            } else if (Object.keys(environmentUrls).length > 0) {
                // Fallback if 'local' isn't even in the config, just pick the first one
                const firstEnv = Object.keys(environmentUrls)[0];
                environmentSelect.value = firstEnv;
                environmentUrlInput.value = savedUrl || environmentUrls[firstEnv];
            } else {
                 console.error("No environments defined in the configuration.");
                 // Handle case with no environments
                 environmentSelect.disabled = true;
                 environmentUrlInput.disabled = true;
                 saveButton.disabled = true;
            }

        } else {
            console.error("Invalid response received from background script for environment config:", response);
            // Handle error, similar to runtime error case
            environmentSelect.disabled = true;
            environmentUrlInput.disabled = true;
            saveButton.disabled = true;
            document.body.insertAdjacentHTML('beforeend', '<p style="color: red;">配置响应无效。</p>');
        }
    });


    // When dropdown selection changes, update the URL input box to the default for that env
    environmentSelect.addEventListener('change', function() {
        const selectedEnv = environmentSelect.value;
        if (environmentUrls[selectedEnv]) {
             environmentUrlInput.value = environmentUrls[selectedEnv];
        } else {
             environmentUrlInput.value = ''; // Clear if selection is somehow invalid
        }
    });

    // Save button click handler
    saveButton.addEventListener('click', function() {
        const selectedEnv = environmentSelect.value;
        const currentUrl = environmentUrlInput.value;

        // Basic validation: Ensure URL is not empty
        if (!currentUrl.trim()) {
            alert("URL 不能为空。"); // Simple feedback
            return;
        }

        chrome.storage.local.set({
            selectedEnvironment: selectedEnv,
            environmentUrl: currentUrl
        }, function() {
            if (chrome.runtime.lastError) {
                console.error("Error saving environment:", chrome.runtime.lastError.message || chrome.runtime.lastError);
                alert("保存失败: " + (chrome.runtime.lastError.message || '未知错误'));
                return;
            }
            chrome.runtime.sendMessage({action: 'reloadPages'})
            console.log('环境和 URL 已保存:', selectedEnv, currentUrl);
            window.close(); // Close popup after successful save

        });
    });
});
