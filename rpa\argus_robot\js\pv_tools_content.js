window.addEventListener("message", function (event) {
  if (event.source !== window) {
    return;
  }
  // 转发给background
  const currentUrl = window.location.href;
  const currentTitle = document.title;
  chrome.runtime.sendMessage({ ...event.data, url: currentUrl, title: currentTitle });
  // if (event.data.action && event.data.action === "followUpstrucutrueData") {
  //   chrome.runtime.sendMessage(event.data);
  // }
  // if (event.data.action && event.data.action === "strucutrueData") {
  //   chrome.runtime.sendMessage(event.data);
  // }
});
