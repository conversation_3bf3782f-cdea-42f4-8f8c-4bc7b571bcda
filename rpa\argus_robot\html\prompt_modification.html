<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="UTF-8">
<title>提示词自动优化器</title>
<style>
  body{font-family:-apple-system,BlinkMacSystemFont,"Segoe UI",<PERSON><PERSON>,"Helvetica Neue",Arial,sans-serif;margin:40px;background:#f7f9fc;}
  h1{color:#333}
  label{font-weight:600;display:block;margin:12px 0 4px;}
  textarea,select{width:100%;padding:10px;border:1px solid #ccc;border-radius:4px;font-size:14px;resize:vertical;}
  button{margin-top:20px;padding:12px 24px;background:#007bff;color:#fff;border:none;border-radius:4px;font-size:16px;cursor:pointer;}
  button:disabled{background:#8ab4ff;cursor:not-allowed;}
  .result{margin-top:30px;padding:20px;background:#fff;border:1px solid #e5e7eb;border-radius:4px;}
  #status{color:#555;margin-top:10px;font-style:italic;}
</style>
</head>
<body>
<h1>提示词自动优化器</h1>

<!-- 1. 选择大模型 -->
<label for="model">选择大模型</label>
<select id="model">
  <option value="model1">togetherai-deepseek-r1</option>
  <option value="model2">azure-openai-o3</option>
  <option value="model3" selected>klhc-qwen3-r1</option>
  <option value="model4">azure-openai-o1</option>
  <option value="model5">azure-openai-o4-mini</option>
  <option value="model6">doubao-1.5-vision-pro</option>
  <option value="model7">doubao-1-5-thinking-pro</option>
  <option value="model8">doubao-1-5-thinking-pro-m-250415</option>
  <option value="model9">doubao-1.5-vision-pro-250328</option>
  <option value="model10">doubao-1.5-ui-tars-250328</option>
</select>

<!-- 其余表单元素保持原样 -->
<!-- 2. 期望结果 -->
<label for="desired">期望结果（可选）</label>
<textarea id="desired" rows="3" placeholder="描述你希望最终得到的内容"></textarea>

<!-- 3. 当前结果 -->
<label for="currentResult">当前结果（可选）</label>
<textarea id="currentResult" rows="3" placeholder="上一次调用的实际输出"></textarea>

<!-- 4. 着重优化点 -->
<label for="focus">着重优化点（可选）</label>
<textarea id="focus" rows="2" placeholder="如：提升逻辑性 / 增加实例 / 控制字数"></textarea>

<!-- 5. 输入参数 -->
<label for="extra">输入参数（可选）</label>
<textarea id="extra" rows="2" placeholder="任何希望模型额外参考的数据或细节"></textarea>

<!-- 6. 当前提示词 -->
<label for="currentPrompt">当前提示词（可选）</label>
<textarea id="currentPrompt" rows="4" placeholder="目前正在使用的 Prompt"></textarea>

<button id="submitBtn" onclick="optimizePrompt()">生成优化后的提示词</button>

<!-- 结果 / 状态区 -->
<div id="output" class="result" style="display:none;">
  <div id="status"></div>
  <h3>优化后的提示词</h3>
  <pre id="optimizedPrompt" style="white-space:pre-wrap;"></pre>
  <button onclick="copyPrompt()">复制到剪贴板</button>
</div>

<script>
/* ------------------------------------------------------
   各模型配置（重点修改model3配置，其他保持原样）
------------------------------------------------------ */
const MODEL_CONFIG = {
  model1: {
    endpoint : 'https://api.together.xyz/v1/chat/completions',
    modelName: 'deepseek-ai/DeepSeek-R1',
    maxTokens: 8192, 
    apiKey: '15809bdd2045b10e9d32d3663ab7dacdd74a05268775f4acac3934d6f807df42', 
    headers  : { 
      'Content-Type':'application/json',
      'Authorization':'Bearer 15809bdd2045b10e9d32d3663ab7dacdd74a05268775f4acac3934d6f807df42',
    },
  },
  model2: {  
    endpoint : 'https://admin-m82eu2lb-eastus2.openai.azure.com/openai/deployments/pv-dev-o3/chat/completions?api-version=2025-01-01-preview',
    modelName: 'pv-dev-o3',
    apiKey: '********************************', 
    headers  : { 
      'Authorization': 'Bearer 8TDBZVDRu5SkbmovU9wY3QZAyN5E3opGY5IVysgEEbI5rOAviRKEJQQJ99BCACHYHv6XJ3w3AAAAACOG39qp',
      'Content-Type':'application/json',
    },
    max_completion_tokens: 8192,
    supportsMultimodal: true
  },
  model3: {
    endpoint : 'https://chat.r2ai.com.cn/v1/chat/completions',
    modelName: 'qwen3',
    maxTokens: 8192, 
    apiKey: 'sk-********************************', 
    headers  : { 
      'Content-Type':'application/json',
      'Authorization':'Bearer sk-********************************', // 关键修改：包含sk-前缀
    },
    enable_stream: true // 新增流式支持
  },
  // 其他模型配置保持不变（为简洁省略，实际使用时需完整保留）
  // ...（这里应保留您原代码中model4到model10的配置）
};

/* ---------------- 主逻辑（关键修改） ---------------- */
async function optimizePrompt () {
  const key   = document.getElementById('model').value;
  const cfg   = MODEL_CONFIG[key];
  if (!cfg) { alert('未找到模型配置'); return; }

  const desired       = document.getElementById('desired').value.trim();
  const currentResult = document.getElementById('currentResult').value.trim();
  const focus         = document.getElementById('focus').value.trim();
  const extra         = document.getElementById('extra').value.trim();
  const currentPrompt = document.getElementById('currentPrompt').value.trim();

  const btn    = document.getElementById('submitBtn');
  const output = document.getElementById('output');
  const status = document.getElementById('status');
  output.style.display = 'block';
  status.textContent   = '思考中…';
  btn.disabled = true; btn.innerText = '优化中…';

  /* 将用户填写的条目逐条拼接 */
  const lines = [];
  if (desired)        lines.push(`期望结果：${desired}`);
  if (currentResult)  lines.push(`当前结果：${currentResult}`);
  if (focus)          lines.push(`着重优化点：${focus}`);
  if (extra)          lines.push(`输入参数：${extra}`);
  if (currentPrompt)  lines.push(`当前提示词：${currentPrompt}`);

  const systemMsg = { 
    role:'system', 
    content:'你是一位资深提示词工程师，请根据用户提供的信息改写提示词，使其尽可能满足用户需求。直接返回优化后的提示词文本，不需要解释。' 
  };
  const userMsg   = { 
    role:'user',   
    content: lines.join('\n') + '\n\n请输出改写后的提示词，仅返回提示词本身。'
  };

  /* 组装请求体（关键修改点） */
  const body = {
    messages:[systemMsg, userMsg],
    max_tokens: cfg.maxTokens, // 修改字段名
    temperature: 0.7, // 新增必要参数
    top_p: 1.0,      // 新增必要参数
    stream: false    // 新增流式设置
  };
  if (cfg.modelName) body.model = cfg.modelName;

  try {
    const res = await fetch(cfg.endpoint, {
      method:'POST',
      headers: cfg.headers,
      body: JSON.stringify(body)
    });

    if (!res.ok) throw new Error(await res.text());
    const data = await res.json();
    const optimized = data.choices?.[0]?.message?.content?.trim() || '(模型未返回内容)';
    document.getElementById('optimizedPrompt').textContent = optimized;
    status.textContent = '已完成 ✅';

  } catch (err) {
    console.error(err);
    status.textContent = '调用失败 ❌：' + err.message;
    alert('调用模型失败：' + err.message);
  } finally {
    btn.disabled = false;
    btn.innerText = '生成优化后的提示词';
  }
}

/* 复制功能 */
function copyPrompt () {
  navigator.clipboard.writeText(
    document.getElementById('optimizedPrompt').innerText
  ).then(()=>alert('已复制到剪贴板!'))
   .catch(e=>alert('复制失败：'+e));
}
</script>
</body>
</html>