ThumbmarkJS.setOption('exclude', ['canvas', 'permissions', 'screen.mediaMatches', 'plugins', 'system.useragent', 'system.browser.name', 'system.browser.version', 'hardware.videocard.vendorUnmasked', 'hardware.videocard.rendererUnmasked', 'fonts'])

ThumbmarkJS.getFingerprint((includeData = true)).then(function (fp) {
    console.log('userId', fp.hash);
    // console.log('userId', JSON.stringify(fp)); 
    window.parent.postMessage(
      {
        type: "RPA_REQUEST",
        requestId: 'navigation_' + Date.now(),
        action: 'setUserId',
        params: { hash: fp.hash },
      },
      "*"
    );
});