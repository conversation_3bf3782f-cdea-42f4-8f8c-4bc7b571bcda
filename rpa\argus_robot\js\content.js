const mainAppId = 'fm_MainApp';
const mainFrameId = 'fm_MainFrame';
const iframeDialog = '#iframeDialog';
const Loading = 'loading';
const Gloading = 'gloading';
const LoadingContainer = 'loadingContainer'
const excludeFoundDomList = ['protocalNumber', 'reportLanguage', 'isDead', 'Subject\'s Primary Disease']
const uselessFields = ['attachmentUrl', 'attachmentName'];
const EnMonth = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'];
// 获取页面fm_MainFrame元素
let documentMainFrame = null;
// 右侧按钮列表
let features;

const avbUrls = [
    /^https:\/\/argus\.pharmaronclinical\.com\//,
    /^https:\/\/argusval\.pharmaronclinical\.com\//,
]
const uatPatterns = [
    /^https:\/\/argus\.pharmaronclinical\.com\//,
    /^https:\/\/39\.103\.224\.255\//,
    /^https:\/\/kelunbiopv\.guorongit\.com\//,
    /^https:\/\/kelunbiopv\.clin-nov\.com\//,
];

const loginArgusUrls = [
    /^https:\/\/argussso\.pharmaronclinical\.com\//,
    /^https:\/\/argusvalsso\.pharmaronclinical\.com/,
]
const loginLssUrls = [
    /^https:\/\/lssmtcval\.arisglobal\.com\.cn\//,
]

const currentUrl = window.location.href;

const isUat = uatPatterns.some(pattern => pattern.test(currentUrl));

const baseUrl = isUat
    ? 'https://copilot-uat.pharmaronclinical.com'
    : 'https://copilot-test.pharmaronclinical.com';

// const baseUrl = 'http://10.11.71.85:49000'

// 双语需要录入的字段值
const fieldBilingualInput = [
    'center_name', 
    'rep_institution', 
    'rep_suffix', 
    'rep_first_name', 
    'rep_last_name', 
    'rep_department', 
    'rep_city', 
    'rep_state', 
    'sc_other_text', 
    'narrative', 
    'company_comment', 
    'product_name', 
    'labtestreptd', 
    'labresult', 
    'labnotes', 
    'comments',
    'desc_reptd',
    'labtestlow',
    'labtest',
    'Fol_Table_{index}_justification', 
    'Ind_Table_{index}_ind_reptd', 
    'CSDD_{index}_cause_reptd', 
    'TableNotesAttach_{index}_notes', 
    '{prefix}_{index}_pat_hist_rptd',
    '{prefix}_{index}_pat_hist_notes',
    'TableReference_{index}_ref_notes',
    'TheContactLog_{index}_contact_description',
    'dose_description'
];    
// 事件评价录入需要处理的前缀
const evaluationMap = new Map([
    [0, 'TXT_rpt_'],
    [1, 'TXT_det_'],
    [2, 'TXT_prt_'],
])

// 实验室检查双语暂存数据
let labtestlowCn = {};
// 随访报告暂存数据
let followUpData = {};
// 创建与录入状态
let isExecing = false;
// 录入时报告的数据
let reportData = {};
// 获取页面租户的编号的JS代码
let getEnvTenantScript = '';

// 首页置灰，进入报告后能点击的菜单
const SCHEME_FEATURES = ['openRecognitionButton', 'registerRpaButton', 'singleEntry', 'historyData', 'singleQc', 'singleSuppAdmis'];

// 菜单配置：跳转新页面的菜单
const featureUrlMap = {
    'openRecognitionButton': 'html/recognition.html',
    'migrationConfiguration': 'pv-migrate-config/index.html',
    'AosePinchWriting': 'html/aose.html'
};

// 录入状态颜色
const loadingStatusColor = new Map([
    ['ongoing', '#766ACA'],
    ['error', '#DB9B3B'],
    ['finish', '#3A8176'],
])

// 补录需要删除的多记录
const SuppAdmisModuleKeys =  [
    "btnAddClass",
    "btnAddFol",
    "reportReporterInfo",
    "btnAdd",
    "pat_race_add",
    "rel_hist_add",
    "refExamineTable",
    "试验用药",
    "合并用药",
    "reportSaeDetailInfo",
    "workLog",
    "reportAttachment",
    "referenceInfo",
]

// 【报告识别工具、注册RPA客户端】当浏览器有这个页面时，只能触发一次点击，多次触发会造成多个连接
const statusRecogRegis = [{
    'openRecognitionButton': 0,
    'registerRpaButton': 0,
}]

// 判断双语录入中文时字段是否需要录入，字典字段、日期字段等则不需要录入
function checkFieldBilingualInput(key) {
    if(fieldBilingualInput.includes(key)) return true
    // 定义正则表达式模式，用于匹配动态字段
    const dynamicPatterns = [
        /^Fol_Table_\d+_justification$/,
        /^Ind_Table_\d+_ind_reptd$/,
        /^CSDD_\d+_cause_reptd$/,
        /^TableNotesAttach_\d+_notes$/,
        /^(?:Rel_Hist_Table|CPH)_\d+_pat_hist_rptd$/,
        /^(?:Rel_Hist_Table|CPH)_\d+_pat_hist_notes$/,
        /^TableReference_\d+_ref_notes$/
    ];
    // 检查字段是否符合动态字段的正则表达式模式
    if(dynamicPatterns.some(pattern => pattern.test(key))) return true;
    if(fieldBilingualInput.find(item => key.includes(item))) return true
}

// 检查扩展是否有效
function isExtensionValid() {
    return chrome.runtime && chrome.runtime.id;
}
// 判断字段录入是否正确，将字段数据进行转换
// 转换数据函数 - 转换日期 - 字符转换为大写
function transformDate(value){
    value = (value + '')?.replaceAll('-', '/')?.replaceAll('/', '/')
    const dates = value.split('/')
    if(dates?.length > 1 && (dates?.[0] === "??" || /^\d+$/.test(dates?.[0]))){
        function isEnDate(val){
            // 是英文格式，例如日-月-年或??-???-YYYY
            if(val?.[2]?.split(' ')?.[0]?.length === 4){
                const month = ['??', '???'].includes(dates?.[1]) ? `${dates?.[1]}/` : (EnMonth.includes(dates?.[1]) ? (dates?.[1] + '/') : ((/^\d+$/.test(dates?.[1]) ? EnMonth[+dates?.[1] - 1] : dates?.[1].toUpperCase()) + '/'))
                const year = ['00:00', '00'].includes(dates?.[2]?.split(' ')?.[1]) ? dates?.[2]?.split(' ')?.[0] : dates?.[2]
                return dates?.[2]?.length >= 4 ? (dates?.[0] ? (dates?.[0] + '/') : '/') + month + year : value
            }else{
                const year = ['00:00', '00'].includes(dates?.[2]?.split(' ')?.[1]) ? dates?.[2]?.split(' ')?.[0] : dates?.[2]
                return (dates?.[0] ? (dates?.[0] + '/') : '/') + (dates?.[1] ? (dates?.[1] + '/') : '/') + year
            }
        }
        return isEnDate(dates)
    }
    if(/[A-Za-z]/.test(value)){
        return value.toUpperCase()
    }
    return value
}

function isExceedsLength(element, key, value){
    const maxAttr = element.getAttribute('maxlength');
    const maxLength = maxAttr ? parseInt(maxAttr, 10) : null;
    if (maxLength !== null && !isNaN(maxLength) && (value + '')?.length > maxLength) {
        throw `The length of the field ${key}:${value} exceeds the maxlength of ${maxLength}`;
    }
}

// 循环录入三次，录入是否录入成功，不成功则报错，录入失败
async function multipleFillValue(key, value, doc){
    let element;
    try{
        element = await waitDom(doc, key);
        if(!element) return
        if(/^TableNotesAttach_\d+_notes$/.test(key)){
            if(value.endsWith('.eml')) value = value + '.doc'
            if(value.endsWith('.rar')) value = value + '.zip'
        }
        await fillField(key, value, doc);
        await waitForPageReload(500)
        // 录入失败，试验次数
        let count = 0;
        if(/Fol_Table_(.*)_amendment/.test(key)){
            if(value != 2) return;
            while(element?.checked === (value===2)){
                await loopExec(key, value, doc, count)
                count++;
            }
            return;
        }
        if(key.endsWith('nfdiv') || key.endsWith('nfddl') || excludeFoundDomList.includes(key) || key.endsWith('lltname') || element.type === 'radio' || key === 'dose_description') return;
        async function loopExec(key, value, doc, count){
            if(count > 2) {
                throw(`Failed to set field ${key} to ${value}`)
            }
            await simulateSingleInput(doc, key, value);
            await waitForPageReload(2000);
            element = await waitDom(doc, key);
        }
        if(key.includes('country') || [/^TheContactLog_\d+_contact_date$/, /^TheContactLog_\d+_contact_date_sent$/].some(pattern => pattern.test(key))) {
            while(!element?.value){
                await loopExec(key, value, doc, count)
                count++;
            }
            return;
        }
        while(!element?.value || transformDate(element?.value) != transformDate(value)){
            await loopExec(key, value, doc, count)
            count++;
        }
    }finally{
        element = null;
    }
}
// 字段直接全部赋值
async function fillField(key, value, doc){
    let element = await waitDom(getDocument(doc), key);
    if (element) {
        if(/Fol_Table_(.*)_amendment/.test(key)){
            element.checked = value === '2';
            return
        }
        if (element.type === 'checkbox') {
            if(element.checked !== (value === '1')) {
                element.click();
            }
        } else if (element.type === 'radio' || key.endsWith('_nfdiv')) {
            // radio或者nullflavor字段的后缀
            element.click();
        } else {

            // 相关病史的字段也需要用鼠标选中 并模拟键盘输入才能生效
            if (key.endsWith('pat_hist_type')) {
                await simulateInput(doc, key, value)

                await waitForPageReload(3000);
            } else {
                isExceedsLength(element, key, value);
                element.value = value;
            }

            triggerEvent(element, 'input');
            triggerEvent(element, 'change');
            triggerEvent(element, 'blur');
        }
        doc = null;
        element = null;
        console.log(`Set field ${key} to ${value}`);
    } else if (!(excludeFoundDomList.includes(key) || key.endsWith('_lltname') || /^[0-9]+$/.test(key))) {
        console.log(`Element with ID or XPath ${key} not found`);
    }
}

// 筛选是否需要录入
async function fillSingleField(key, value, doc, bilingual) {
    if(!checkFieldBilingualInput(key) && bilingual) return
    
    if(key === 'study_num' || key === 'generic_name' || key === 'product_name') return

    if (!value || value === '' || uselessFields.includes(key)) {
        return;
    }

    await multipleFillValue(key, value, doc)    
}
// 根据name标签获取页面元素
function getDocumentByName(ifName) {
    return document.getElementById(mainAppId)?.contentDocument?.getElementsByName(ifName)?.[0]?.contentDocument;
}
// 清除字段值
async function clearSingleField(key, doc) {
    if (!key || key === '') {
        return;
    }
    let element = await waitDom(getDocument(doc), key);
    doc = null;
    if (element) {
        if (element.type === 'checkbox') {
            element.checked = '';
        } else if (element.type === 'radio' || key.endsWith('_nfdiv')) {
            // radio或者nullflavor字段的后缀
            element.click();
        } else {
            element.value = '';
            triggerEvent(element, 'input');
            triggerEvent(element, 'change');
            triggerEvent(element, 'blur');
        }
        console.log(`Set field ${key} to ''`);
        element = null;
    } else {
        console.log(`Element with ID or XPath ${key} not found`);
    }
}
// 元素触发事件
function triggerEvent(element, eventType) {
    return new Promise((resolve) => {
        if (!element) {
            resolve();
            element = null;
            return;
        }

        const event = new Event(eventType, {
            bubbles: true
        });

        let resolved = false;

        const timeoutId = setTimeout(() => {
            if (!resolved) {
                console.log(`Event ${eventType} not triggered, resolving anyway.`);
                resolved = true;
                resolve();
            }
        }, 100);

        element.addEventListener(eventType, function handler() {
            if (!resolved) {
                clearTimeout(timeoutId);
                element.removeEventListener(eventType, handler);
                resolved = true;
                resolve();
                element = null;
            }
        });

        element?.dispatchEvent(event);
    });
}
// 判断是否是xPath
function isXPath(key) {
    return key?.startsWith("//") || key?.startsWith("(//");
}

function monitorNetworkRequest(timeout = 10) {
    return new Promise((resolve) => {
        let pendingRequests = 0; // 用于跟踪未完成的请求数量
        let timeoutId; // 用于存储超时定时器的 ID

        // 劫持 XMLHttpRequest 的 open 方法
        const originalXHROpen = XMLHttpRequest.prototype.open;
        XMLHttpRequest.prototype.open = function () {
            // 在请求状态改变时触发的事件监听器
            this.addEventListener('readystatechange', function () {
                if (this.readyState === 4) { // 请求完成
                    pendingRequests--; // 减少未完成请求计数
                    checkAllRequestsComplete(); // 检查所有请求是否已完成
                }
            }, false);
            pendingRequests++; // 增加未完成请求计数
            clearTimeout(timeoutId); // 清除超时定时器
            originalXHROpen.apply(this, arguments); // 调用原始的 open 方法
        };

        // 劫持 fetch 方法
        const originalFetch = window.fetch;
        window.fetch = function () {
            pendingRequests++;
            clearTimeout(timeoutId);
            return originalFetch.apply(this, arguments)
                .then(response => {
                    pendingRequests--;
                    checkAllRequestsComplete();
                    return response;
                })
                .catch(error => {
                    pendingRequests--;
                    checkAllRequestsComplete();
                    throw error;
                });
        };

        // 劫持 $.ajax 方法
        // const originalAjax = $.ajax;
        // $.ajax = function(options) {
        //     pendingRequests++;
        //     clearTimeout(timeoutId);

        //     return originalAjax(options)
        //         .then(response => {
        //             pendingRequests--;
        //             checkAllRequestsComplete();
        //             return response;
        //         })
        //         .catch(error => {
        //             pendingRequests--;
        //             checkAllRequestsComplete();
        //             throw error;
        //         });
        // };

        // 检查是否所有请求都已完成
        function checkAllRequestsComplete() {
            if (pendingRequests === 0) {
                clearTimeout(timeoutId);
                resolve(true)
            }
        }

        // 设置超时
        timeoutId = setTimeout(() => {
            if (pendingRequests === 0) {
                resolve(true);
            } else {
                reject(new Error("请求超时"));
            }
        }, timeout);
    })

}
// 等待页面元素
async function waitDom(dom, key, waitTime = 1000, className) {
    if (!key || excludeFoundDomList.includes(key) || key.endsWith('_lltname') || /^[0-9]+$/.test(key)) {
        return;
    }
    let i = 0;
    let result;
    while (true) {
        result = getElement(dom, key, className)
        if (result) {
            i++;
            break;
        } else {
            await wait(waitTime);
            i++;
        }
        if (i === 10) {
            console.log(`${key}元素查找不到`)
            break
        }
    }
    return result;
}

function wait(waitTime) {
    return new Promise(resolve => {
        setTimeout(resolve, waitTime);
    });
}
// 页面加载等待加载加载元素加载完毕
async function waitForPageReload(waitTime, key = mainFrameId) {
    let loadingDom;
    let GloadingDom;
    let LoadingContainerDom;
    try {
        const requestFinish = await monitorNetworkRequest(waitTime);
        // await wait(1000)
        loadingDom = getDocument()?.getElementById?.(Loading);
        GloadingDom = getDocument()?.getElementById?.(Gloading);
        LoadingContainerDom = getDocument()?.getElementById?.(LoadingContainer);
        if (loadingDom || GloadingDom || LoadingContainerDom) {
            const loadingStyle = window.getComputedStyle(loadingDom)
            const gloadingStyle = window.getComputedStyle(GloadingDom)
            const LoadingContainerStyle = window.getComputedStyle(LoadingContainerDom)

            if (loadingStyle?.display !== 'none' || gloadingStyle?.display !== 'none' || (LoadingContainerStyle?.display === 'block' && !isDialog())) {
                await waitForPageReload(waitTime)
                return
            }
            if(isDialog()){
                LoadingContainerDom = getDialog()?.getElementById?.(LoadingContainer)
                if(LoadingContainerDom){
                    const loadingMaskStyle = window.getComputedStyle(LoadingContainerDom)
                    if(loadingMaskStyle?.display === 'block'){
                        await waitForPageReload(waitTime)
                        return
                    }
                }
                
            }
            await waitForElement(key, waitTime)
            if (requestFinish && performance.timing.responseEnd) {
                return
            } else {
                await waitForPageReload(waitTime, Loading)
            }
        } else {
            await waitForPageReload(waitTime, key)
        }
        

    } catch (e) {
        console.log('error', e.message);
    }finally{
        loadingDom = null;
        GloadingDom = null;
        LoadingContainerDom = null;
    }
}

function getElement(doc, key, className) {
    try{
        return isXPath(key) ?
        (doc || getDocument())?.evaluate(key, (doc || getDocument()), null, XPathResult.FIRST_ORDERED_NODE_TYPE, null)?.singleNodeValue :
        className ? (doc || getDocument())?.querySelector(className) : (doc || getDocument())?.getElementById(key);
    }finally{
        doc = null;
    }

}

/**
 * 等待指定的 DOM 元素出现
 * @param {String} key - 元素的 ID 或 XPath 表达式
 * @param {Number} [timeout=3000] - 超时时间，默认为 3000 毫秒
 * @returns {Promise} - 返回一个 Promise，当元素出现时解析，当超时或出错时拒绝
 */
function waitForElement(key, timeout = 3000) {
    return new Promise((resolve, reject) => {
        const startTime = Date.now(); // 记录开始时间
        let element = null; // 用于存储找到的元素
        function checkElement() {
            // 获取元素
            element = key === mainFrameId ? getDocument() : getElement(getDocument(), key)
            if (element) {
                clearInterval(intervalId);
                resolve(true);
                element = null;
            } else if (Date.now() - startTime > timeout) {
                clearInterval(intervalId);
                reject(`Element of ${key} not found within timeout`);
            } else {
                // 如果未找到元素且未超时，则继续下一帧检查
                requestAnimationFrame(checkElement);
            }
        }
        // 使用 requestAnimationFrame 进行定时检查
        const intervalId = requestAnimationFrame(checkElement);
    });
}

function getDocument(doc) {
    if (documentMainFrame) {
        documentMainFrame = null;
    }
    documentMainFrame = doc ? doc : document.getElementById(mainAppId)?.contentDocument?.getElementById(mainFrameId)?.contentDocument;
    doc = null;
    return documentMainFrame;
}
/**
 * 获取主文档中的对话框节点列表
 * @returns {NodeList|null} 对话框节点列表或null
 */
function getDialogNodes() {
    const mainDoc = document.getElementById(mainAppId)?.contentDocument;
    if (!mainDoc) return null;
    
    const nodes = mainDoc.querySelectorAll(iframeDialog);
    return nodes?.length ? nodes : null;
}
/**
 * 获取单个对话框文档
 * @param {string} [title] - 对话框标题（可选）
 * @returns {Document|null} 对话框文档或null
 */
function getDialog(title) {
    const nodes = getDialogNodes();
    if (!nodes) return null;

    for (const node of nodes) {
        if (!node) continue;
        
        // 如果没有指定title，返回第一个有效的对话框
        if (!title) return node.contentDocument;
        
        // 如果指定了title，查找匹配的对话框
        const href = node.getAttribute('src');
        if (href?.includes(title)) return node.contentDocument;
    }
    
    return null;
}

/**
 * 检查是否存在对话框
 * @returns {boolean} 是否存在对话框
 */
function isDialog() {
    try {
        return !!getDialogNodes();
    } catch {
        return false;
    }
}

/**
 * 获取所有匹配的对话框文档
 * @param {string} [title] - 对话框标题（可选）
 * @returns {Document[]|null} 对话框文档数组或null
 */
function getDialogs(title) {
    const nodes = getDialogNodes();
    if (!nodes) return null;
    const result = [];
    for (const node of nodes) {
        if (!node) continue;
        // 如果没有指定title，收集所有对话框
        if (!title) {
            result.push(node.contentDocument);
            continue;
        }
        // 如果指定了title，只收集匹配的对话框
        const href = node.getAttribute('src');
        if (href?.includes(title)) {
            result.push(node.contentDocument);
        }
    }
    return result.length ? result : null;
}
// 关闭弹窗
async function closeDialog(reportLocale = 'cn') {
    await cancelDialog(reportLocale)
    await okDialog(reportLocale)
    // 处理弹出窗口没有关闭
}
// 判断事件评价的结果是否触发弹窗，触发后点击第一行关闭弹窗
async function closeEventAssJust(reportLocale){
    if(isDialog()){
        await waitForPageReload(100)
        triggerEvent(await waitDom(getDialog(), 'td1', 100), 'click');
        await waitForPageReload(100)
        await clickFun("//*[@id='okbtn']", reportLocale)
    }
}
// 弹窗点击
async function clickFun(path, reportLocale, title, isJust = true) {
    while (isDialog()) {
        let dialogTitle = getElement(getDialog(), 'dialogTitle')
        if(dialogTitle?.innerHTML?.includes(reportLocale === 'cn' ? '理由' : 'Justification') && isJust){
            await closeEventAssJust(reportLocale)
            dialogTitle = null;
        }
        let dom = await waitDom(getDialog(title), path, 100)
        if (dom) {
            triggerEvent(dom, "click");
            await waitForPageReload(200);
            dom = null;
            dialogTitle = null;
            return;
        } else {
            break;
        }
    }
}
// 点击确认按钮关闭弹窗
async function okDialog(reportLocale) {
    // 处理弹出窗口没有关闭
    await clickFun("//button[@value='" + (reportLocale === 'cn' ? '确定' : 'OK') + "']", reportLocale)
    await clickFun("//input[@value='" + (reportLocale === 'cn' ? '确定' : 'OK') + "']", reportLocale)
    await clickFun("//input[@value='OK']", 'Argus', reportLocale)
    await clickFun("//*[@id='btnOkYes']", reportLocale)
    await clickFun("//*[@id='okbtn']", reportLocale)
    await clickFun("//*[@id='btn_OK']", reportLocale)
}
// 点击取消按钮关闭弹窗
async function cancelDialog(reportLocale) {
    // 处理弹出窗口没有关闭
    await clickFun("//button[@value='" + (reportLocale === 'cn' ? '取消' : 'Cancel') + "']", reportLocale)

    // 处理弹出窗口没有关闭，科伦的中文whodrugcoding使用的是英文的控件，造成中英文的适配失效
    await clickFun("//button[@value='Cancel']",reportLocale, 'Drug')

    // 处理弹出窗口没有关闭
    await clickFun("//*[@id='btnNo']",reportLocale)

    // 处理弹出窗口没有关闭
    await clickFun("//*[@id='bt_cancel']", reportLocale)
    
    await clickFun("//*[@id='btnOkYes']", reportLocale)

    await clickFun("//*[@id='btn_cancel']", reportLocale)
    await clickFun("//*[@id='btnCancel']", reportLocale)
}
// 循环录入三次，录入是否录入成功，不成功则报错，录入失败
async function multipleSimulateInput(doc, key, value, bilingual){
    if(!checkFieldBilingualInput(key) && bilingual) return
    if (!value || value === '' || uselessFields.includes(key)) {
        return;
    }
    let element;
    try{
        element = await waitDom(doc, key);
        if(!element) return;
        await simulateSingleInput(doc, key, value);
        await waitForPageReload(500)
        if(key.endsWith('nfdiv') || key.endsWith('nfddl') || excludeFoundDomList.includes(key) || key.endsWith('lltname') || element.type === 'radio') return;
        async function loopExec(key, value, doc, count){
            if(count > 2) {
                throw(`Failed to set field ${key} to ${value}`)
            }
            await simulateSingleInput(doc, key, value);
            await waitForPageReload(2000);
            element = await waitDom(doc, key);
        }
        // 试验用药编码选择失败，再次试验
        let count = 0;
        if(key.includes('country')) {
            while(!element?.value){
                await loopExec(key, value, doc, count);
                count++;
            }
            return;
        }
        while(!element?.value || transformDate(element?.value) != transformDate(value)){
            await loopExec(key, value, doc, count);
            count++;
        }
    }finally{
        element = null;
    }
}

async function simulateSingleInput(doc, key, value) {
    let element;
    try{
        element = await waitDom(doc, key);
        if (element) {
            element.focus();
            // 创建一个双击事件
            const doubleClickEvent = new MouseEvent('dblclick', {
                bubbles: true,
                cancelable: true,
                view: window
            });
            element.dispatchEvent(doubleClickEvent);
            element.value = '';
            for (let i = 0; i < value.length; i++) {
                let char = value[i];
                let keyDownEvent = new KeyboardEvent('keydown', {
                    key: char
                });
                let keyPressEvent = new KeyboardEvent('keypress', {
                    key: char
                });
                let inputEvent = new InputEvent('input', {
                    data: char
                });
                let keyUpEvent = new KeyboardEvent('keyup', {
                    key: char
                });
    
                element.dispatchEvent(keyDownEvent);
                element.dispatchEvent(keyPressEvent);
                element.value += char;
                element.dispatchEvent(inputEvent);
                element.dispatchEvent(keyUpEvent);
    
                if (i !== value.length - 1) {
                    // 添加延迟以模拟真实的输入速度
                    await wait(50);
                }
            }
            await wait(2000);
            isExceedsLength(element, key, value);
            element.dispatchEvent(new Event('input')); // 触发 input 事件以模拟真实输入后的效果
            element.dispatchEvent(new Event('change')); // 触发 change 事件以模拟真实输入后的效果
            // 添加延迟以模拟真实的输入速度
            element.dispatchEvent(new Event('blur')); // 触发 blur 事件以模拟真实输入后的效果
            console.log(`Set field ${key} to ${value}`);
        } else {
            console.log(`Element with ID or XPath ${key} not found`);
        }
    }finally{
        element = null;
    }
    
}

// 模拟输入
async function simulateInput(doc, key, value, bilingual, reportLocale) {
    if(!checkFieldBilingualInput(key) && bilingual) return
    // 双语录入时手动输入的将不需要再次录入
    if (!value || value === '' || bilingual) {
        return;
    }
    
    if(key === 'TXT_Class_Table_0_class_id'){
        if(value === '1'){
            triggerEvent(await waitDom(getDocument(), 'btnAddClass'), 'click')
            await simulateInput(getDocument(), 'TXT_Class_Table_0_class_id', reportLocale === 'en' ? 'Non-valid' : '无效报告')
            return
        }
    }
    await multipleSimulateInput(doc, key, value, bilingual);
}
// 录入完毕判断是否跳转首页，否则即返回首页
async function buttonNotSaved(){
    triggerEvent(getDocument().getElementById('Header:imgClose'), 'click')
    await waitForPageReload(1000)
    await clickFun("//*[@id='btnNo']")
    await waitForPageReload(1000)
    while(isDialog()){
        await closeDialog();
    }
}

// 辅助函数：将 Data URL 转换为 Blob
function dataURLtoBlob(dataurl) {
    const arr = dataurl.split(',');
    const mime = arr[0].match(/:(.*?);/)[1];
    const bstr = atob(arr[1]);
    let n = bstr.length;
    const u8arr = new Uint8Array(n);
    while(n--){
        u8arr[n] = bstr.charCodeAt(n);
    }
    return new Blob([u8arr], {type:mime});
}
// 附件上传文件
async function uploadFile(filePath, fileName, index, reportLocale, taskId) {
    if(fileName.endsWith('.eml')) fileName = fileName + '.doc'
    if(fileName.endsWith('.rar')) fileName = fileName + '.zip'
    await waitForPageReload(300);
    if(isDialog()){
        await closeDialog();
    }
    // 上传文件类型为eml的跳过
    if(fileName?.endsWith('.eml')) return;
    // 上传文件类型为rar的跳过
    if(fileName?.endsWith('.rar')) return;
    triggerEvent(getElement(getDocument(), `TableNotesAttach_${index}`), 'click');
    await waitForPageReload(300);
    //点击添加附件
    triggerEvent(getElement(getDocument(), "NA_AttachFile"), 'click');
    await wait(1000);
    if(getElement(getDialog(), 'txtMessage')?.innerHTML?.includes('Do you wish to overwrite the current attachment for the selected row?')){
        triggerEvent(getElement(getDialog(), 'btnOkYes'), 'click')
        await wait(1000);
    }
    await wait(1000);
    let fileInput = await waitDom(getDialog(), "FileUploadControl_FileUploadCtrl", 3000);
    while(!fileInput){
        fileInput = await waitDom(getDialog(), "FileUploadControl_FileUploadCtrl", 3000);
    }
    if(!fileInput || !fileName){
        // 请求修改新建状态的接口
        await request('autoInput/updateStatus', 'post', {taskId: taskId, [reportLocale === 'en' ? 'inputStatusEn' : 'inputStatusCn']: 3, message: '附件上传错误'})
        await buttonNotSaved()
        return;
    }

    async function simulateFileUpload(filePath) {   
        if(!isExtensionValid){
            console.log('扩展上下文无效，等待重试...');
            reject(`附件${filePath}` + (response?.data || '插件需要刷新'));
        }
        return new Promise((resolve, reject) => {
            chrome.runtime.sendMessage({
                action: 'makeFileHttpRequest',
                url: `${baseUrl}/base-server/autoInput/download`,
                options: {method: 'post'},
                headers: {
                    argusUsername: localStorage.getItem('userName'), 
                    tenantId: localStorage.getItem('tenantId'),
                    accessAddress: window.location.origin,
                    userId: localStorage.getItem('userId'),
                },
                body: JSON.stringify({fileId: filePath}) // 仅在非 GET 请求时设置 body
              }, async (response) => {
                if (response.success) {
                    let blob = null;
                    let file = null;
                    let dataTransfer = null;
                    try{
                        // 使用 Data URL 创建 Blob
                        blob = dataURLtoBlob(response.data);
                                            
                        // 创建一个新的 File 对象
                        file = new File([blob], fileName, {type: blob.type});

                        // 创建一个 DataTransfer 对象并将文件添加到其中
                        dataTransfer = new DataTransfer();
                        dataTransfer.items.add(file);

                        // 设置文件输入元素的文件列表
                        fileInput.files = dataTransfer.files;

                        // 触发文件输入元素的 change 事件以模拟用户选择文件
                        const event = new Event('change', { bubbles: true });
                        fileInput.dispatchEvent(event);

                        console.log('File uploaded successfully.');
                        resolve('File uploaded successfully.')
                    }catch(e){
                        console.log(e);
                    }finally{
                        blob = null;
                        file = null;
                        dataTransfer = null;
                    }
                    
                } else {
                    reject(`附件${filePath}` + (response?.data || 'File uploaded error'));
                }
              });
        })
      
    }
    // 判断是否上传文件成功
    async function judgeIsUpload(count, close){
        if(count < 3){
            if(getDialogs()?.length > 1){
                for (let [i, item] of getDialogs().entries()) {
                    let txtMessage;
                    try{
                        txtMessage = item.getElementById('PageName');
                        await waitForPageReload(500)
                        if(txtMessage?.innerHTML?.includes?.('File Upload Validation')){
                            triggerEvent(item.getElementById('btnOkYes'), 'click');
                        }
                    }finally{
                        txtMessage = null;
                    }
                    
                }
                count++;
                if(!close) {
                    await simulateFileUpload(filePath);
                    triggerEvent(await waitDom(getDialog(), 'btnOk'), 'click');
                }
                await judgeIsUpload(count, close);
            }
        }
    }

    // 调用函数，传入本地文件路径
    await simulateFileUpload(filePath);
    await waitForPageReload(3000);
    let count = 0;
    await judgeIsUpload(count)
    if(isDialog()) triggerEvent(await waitDom(getDialog(), 'btnOk'), 'click');
    await waitForPageReload(1000);
    await judgeIsUpload(count, 'close');
    if(isDialog()) triggerEvent(await waitDom(getDialog(), 'btnCancel'), 'click');
    await waitForPageReload(5000);
}
// 检查数据是否已经查询完成并等待DOM元素出现
async function checkIfDataQueried(noRecord, records, elementDom, noRecordId, recordsId, reqUrl, code) {
    records = await waitDom(elementDom, recordsId, 300);
    const startTime = Date.now();
    const maxWaitTime = 5 * 60 * 1000; // 5分钟转换为毫秒
    function sendMessagePromise(reqUrl) {
        return new Promise((resolve, reject) => {
          chrome.runtime.sendMessage({
            action: 'ifInterfaceReqCompleted',
            data: reqUrl
          }, () => {
            if (chrome.runtime.lastError) {
              if(chrome.runtime.lastError.message === 'The message port closed before a response was received.') resolve(true);
              else reject(chrome.runtime.lastError);
            } else {
              resolve(true);
            }
          });
        });
    }
    while(!records){
        // 检查无记录状态
        noRecord = await waitDom(elementDom, noRecordId, 300);
        records = await waitDom(elementDom, recordsId, 300);

        // 药品代码模式的特殊处理
        if (code) {
            if (records) {
                // 检查代码是否匹配
                if (await waitDom(elementDom, 'CODE_LLT')?.innerHTML?.includes(code)) {
                    break;
                }
            }
        } else {
            if (noRecord || records) break;
        }
        // 检查是否明确无记录
        if (['未找到相关记录', 'No Records Found'].includes(noRecord?.textContent?.trim())) {
            break;
        }        
        try {
            const response = await sendMessagePromise(reqUrl);
            if (response) {
              records = await waitDom(elementDom, recordsId, 300);
              // 收到成功的返回，跳出循环
              break;
            }
        } catch (error) {
            console.log('编码判断接口完成问题', error);
        }
        
        if (Date.now() - startTime >= maxWaitTime) {
            break;
        }
        await waitForPageReload(3000)
    }
    return records;
}

// 合并用药编码
async function combineDrugCode(lltname, reportCountry, product_name, product_count) {
    // 科伦中英文都有meddra按钮，AVB仅用英文页面有，直接判断编码按钮是否存在
    let drugCodingButton = await waitDom(getDocument(), "btn_DrugSelect");
    console.info('++++++++++++++++', drugCodingButton)
    if (drugCodingButton) {
        // 使用正则表达式匹配名称和编码
        const regex = /^(.+?)\((.*)\)$/;
        const matches = lltname.match(regex);
        // 使用报告发生国家作为whodrug的默认国家查询条件
        let country = reportCountry;

        if (matches) {
            // code(name) 避免name中出现括号导致正则匹配遗漏数据
            const code = matches[1];
            const name = matches[2];
            console.log(`whodrug名称: ${name}`);
            console.log(`whodrug编码: ${code}`);

            triggerEvent(drugCodingButton, "click");
            await waitForPageReload(2000);
            drugCodingButton = null;
            let whodrugDialog = getDialog('Drug');
            let searchButton = await waitDom(whodrugDialog, 'b_Search');
            try{
                while (whodrugDialog && searchButton) {
                    // whodrug code
                    await fillField('search_drug_code', code, whodrugDialog)
                    // whodrug name
                    await fillField('search_drug_description', name, whodrugDialog)
                    // whodrug formulation
                    await fillField('search_formulation', /[\u4e00-\u9fa5]/.test(name) ? '未指名的' : 'Unspecified', whodrugDialog)

                    console.log('country', country);

                    // 国家存在
                    if (country && country !== '') {
                        // whodrug country
                        await fillField('search_country', country, whodrugDialog)
                    } else {
                        await clearSingleField('search_country', whodrugDialog)
                    }

                    // 点击搜索
                    await waitForPageReload(500);
                    triggerEvent(searchButton, "click");
                    await waitForPageReload(2000);

                    // 选择标准编码 b_Search
                    let drugRecord = await waitDom(whodrugDialog, "//table[@id='codingsearch']//tr[1]");
                    let noDrugRecord = await waitDom(whodrugDialog, "//table[@id='codingsearch']//tr[td/span/b/text()='No data found']\n", 300);

                    // 如果国家存在，则清除国家，再查询一次
                    if (noDrugRecord) {
                        if (country && country.trim() !== '') {
                            country = '';
                            continue;
                        }
                    }
                    let drugRecords = await waitDom(whodrugDialog, "//table[@id='codingsearch']/tbody[tr]", 300);
                    drugRecords = await checkIfDataQueried(noDrugRecord, drugRecords, whodrugDialog, '//table[@id="codingsearch"]/tbody[tr]/td[1]/span/b/text()', "//table[@id='codingsearch']/tbody[tr]", '/Dictionary/AjaxAddWhoDrug.asp')
                    noDrugRecord = await waitDom(whodrugDialog, "//table[@id='codingsearch']//tr[td/span/b/text()='No data found']\n", 300);
                    if (!noDrugRecord) {
                        drugRecord = await waitDom(whodrugDialog, "//table[@id='codingsearch']//tr[1]");
                        triggerEvent(drugRecord, "click");
                        await waitForPageReload(1000);
                        drugRecord = null;

                        // 确认
                        let OK = await waitDom(whodrugDialog, "b_Select")
                        triggerEvent(OK, "click");
                        await waitForPageReload(2000);
                        OK = null;
                    } else {
                        // 取消
                        let cancel = await waitDom(whodrugDialog, "cancelbtn")
                        triggerEvent(cancel, "click");
                        await waitForPageReload(2000);
                        cancel = null;
                        noDrugRecord = null;
                        if(product_count < 3) {
                            product_count++;
                            await combineDrugCode(lltname, reportCountry, product_name, product_count);
                        }else{
                            throw(`lltname匹配 ${product_name} 失败,已尝试3次,请检查数据`)
                        }
                    }
                    break;
                }
            
            }finally{
                whodrugDialog = null;
                searchButton = null;
            }

            
        } else {
            await simulateInput(getDocument(), 'product_name', product_name)
            console.log("匹配失败");
        }
    }
}
// 药品名称没有lltName时，重新将药品名称赋值
async function assignDrugName(product_name) {
    // 科伦中英文都有meddra按钮，AVB仅用英文页面有，直接判断编码按钮是否存在
    let drugCodingButton = await waitDom(getDocument(), "btn_DrugSelect");
    console.info('++++++++++++++++', drugCodingButton)
    if (drugCodingButton) {

        triggerEvent(drugCodingButton, "click");
        await waitForPageReload(2000);
        drugCodingButton = null;
        let whodrugDialog = getDialog('Drug');
        try{
            if(getDocument().getElementById('product_name'))
                getDocument().getElementById('product_name').value = product_name;
            await waitForPageReload(1000)
            // 取消
            let cancel = await waitDom(whodrugDialog, "cancelbtn")
            triggerEvent(cancel, "click");
            await waitForPageReload(1000);
            cancel = null;
        
        }finally{
            whodrugDialog = null;
        }
    }
}
// 右键触发新增试验用药
async function addStudyDrug() {
    const event = new MouseEvent('contextmenu', {
        bubbles: true,
        cancelable: true,
        view: window,
        button: 2, // 右键是 button 为 2
    });

    // 在药品名称的字段上面触发试验用药的右键弹窗
    let productName = await waitDom(getDocument(), 'product_name');
    productName.dispatchEvent(event);

    await waitForPageReload(1000, "PopupMenu_regaddstudy")
    productName = null;

    let rightClickAddButton = await waitDom(getDocument(), 'PopupMenu_regaddstudy');
    if (rightClickAddButton) {
        triggerEvent(rightClickAddButton, 'click');
        await waitForPageReload(1000);
        rightClickAddButton = null;
        return true;
    }

    await waitForPageReload(1000);
    // 触发
    return false;
}


// Helper function to process drug records
const processDrugRecords = (drugRecords, type, bilingual) => {

    if (!drugRecords) {
        return [];
    }

    return drugRecords.map((drugRecord) => {
        const {
            btnAddInd,
            btnAddIng,
            expDoseTable,
            ...otherDrugInfo
        } = drugRecord;
        const firstDose = expDoseTable.length > 0 ? expDoseTable.shift() : undefined
        return {
            ...otherDrugInfo,
            "indication": {
                "events": [
                    {
                        path: bilingual ? 'multiple@IngredientTable_' : type === '试验用药' ? 'multiple@IngredientTable_' : `//*[@id='btnAddIng']`,
                        event: 'click',
                        waitTime: 2000,
                        loop: true,
                        records: btnAddIng ? btnAddIng : []
                    },
                    {
                        path: bilingual ? 'multiple@Ind_Table_' : `//*[@id='btnAddInd']`,
                        event: 'click',
                        waitTime: 2000,
                        loop: true,
                        records: btnAddInd ? btnAddInd : []
                    },
                ]
            },
            "expDoseTable": {
                "events": [{
                    path: `//*[@id='td_CF_DOSEREG_1']`,
                    event: 'click',
                    waitTime: 2000,
                    loop: true,
                    records: firstDose ? [firstDose] : []
                },
                    {
                        path: bilingual ? "multiple-records@//tr[@id='tr_CF_DOSEREG_" : `//tr[@id='tr_CF_DOSEREG']/td[position()=last()-1]`,
                        event: 'click',
                        waitTime: 2000,
                        loop: true,
                        records: expDoseTable
                    }
                ]
            }
        };
    });
};
// 获取事件评价表格元素数据
function getEvaluationFormList() {
    // 返回
    const result = {};
    // 获取id为Event_Assess_Table的元素
    const eventAssessTable = getDocument().getElementById('Event_Assess_Table');
    if(!eventAssessTable) return;

    // 获取所有子元素
    const childElements = eventAssessTable?.getElementsByTagName('*');

    // 创建一个数组来存储id包含Product_Row_的元素
    const productRowElements = [];

    // 遍历所有子元素
    for (let i = 0; i < childElements.length; i++) {
        const childElement = childElements[i];
        // 如果元素的id包含Product_Row_，添加到数组中
        if (childElement.id && childElement.id.includes('Product_Row_')) {
            productRowElements.push(childElement);
        }
    }
    // 遍历所有productRowElements
    productRowElements.forEach(productRowElement => {
        // 创建一个数组来存储id包含Event_Row_的元素
        const eventRowElements = [];
        // 获取productRowElement的所有子元素
        const productRowChildElements = productRowElement.getElementsByTagName('*');

        // 遍历所有子元素
        for (let j = 0; j < productRowChildElements.length; j++) {
            const productRowChildElement = productRowChildElements[j];
            // 如果元素的id包含Event_Row_，添加到数组中
            if (productRowChildElement.getAttribute('event_assess_seq_num')) {
                eventRowElements.push(productRowChildElement);
            }
        }
        const eventByProductName = productRowElement.querySelectorAll(".label-hyp")?.[0]?.textContent?.trim()
        if(result[eventByProductName]) {
            result[eventByProductName] = [...(eventRowElements || []), ...(result[eventByProductName] || [])]
        }else result[eventByProductName] = eventRowElements
    });
    return result
}
// 给事件评价det_list_id赋值
async function setDetListId(doc, value, reportLocale){
    let DataSheet_Table = doc.querySelector('#DataSheet_Table')
    let elements = DataSheet_Table.getElementsByTagName('*')
    let element = [];
    // 遍历所有子元素
    for (let j = 0; j < elements.length; j++) {
        const det_list_id = elements[j];
        // 如果元素的id包含Event_Row_，添加到数组中
        if (det_list_id.id && det_list_id.id.startsWith('det_list_id')) {
            element.push(det_list_id);
        }
    }
    if(element?.[0]) {
        element[0].value = value;
        element[0].dispatchEvent(new Event('change'))
        DataSheet_Table = null;
        elements = []
        element = []
    }
    await waitForPageReload(500)
    closeEventAssJust(reportLocale)
}
// 获取实验室检查数据模块的所有日期
function getExamineDateSet() {
    // 获取实验室数据模块的容器
    const labDataDiv = getDocument().getElementById(
      "Pat_LabData_Content_Div"
    );
    if (!labDataDiv) {
      console.error("无法找到ID为 'Pat_LabData_Content_Div' 的元素。");
      return;
    }

    // 获取日期头部（examineDate）从crosstab表格
    const crosstabTable = labDataDiv.querySelector("#crosstab");
    if (!crosstabTable) {
      console.error("无法找到ID为 'crosstab' 的表格元素。");
      return;
    }

    // 获取所有日期列（examineDate）
    const dateHeaders = crosstabTable.querySelectorAll(
      "#labcontentheader input[name^='labdate_']:not([style*='display: none'])"
    );

    // 获取所有实验室测试行，排除表头
    return Array.from(dateHeaders).map((input) =>
        input?.value.trim()?.replaceAll('/', '-')
      ).map(item => ['YYYY-??-??'].includes(item) ? '' : item)
  }
// 转换实验室检查json数据为页面所需录入数据
async function transformData(reportData, inputData, originLabtestSet, isBilingual) {
    let labtestSet = [];
    let examineDateSet = [...new Set(inputData?.map(item => item.examineDate))];
    if(isBilingual && isBilingual === 'bilingual'){
        examineDateSet = getExamineDateSet()
    }
    const cnData = reportData?.reportDataCn?.['reportSubjectInfo']?.refExamineTable || []
    const enData = reportData?.reportDataEn?.['reportSubjectInfo']?.refExamineTable || []

    // 判断唯一标识
    function isEntoCn(item, lab, cnItem){
        if(lab?.labtestreptd === item?.labtestreptd && item?.labtest === lab?.labtest && item?.labtestlow === lab?.labtestlow && item?.labtesthigh === lab?.labtesthigh && item?.TXT_labunit_0 === lab?.TXT_labunit_0 && !cnItem) return true
        if(lab?.labtestreptdEn === item?.labtestreptd && item?.labtest === lab?.labtestEn && item?.labtestlow === lab?.labtestlowEn && item?.labtesthigh === lab?.labtesthighEn && item?.TXT_labunit_0 === lab?.TXT_labunit_0En && lab?.labtestreptdCn === cnItem?.labtestreptd && cnItem?.labtest === lab?.labtestCn && cnItem?.labtestlow === lab?.labtestlowCn && cnItem?.labtesthigh === lab?.labtesthighCn && cnItem?.TXT_labunit_0 === lab?.TXT_labunit_0Cn) return true
        return false
    }

    // 根据双语调换顺序
    if(isBilingual === 'bilingual') { 
        for(let index = 0; index < inputData?.length; index++){
            if(index >= originLabtestSet?.length) break;
            const labtestlowIndex = await waitDom(getDocument(), `//tr[@id='labtest_${index}']//input[@id='labtestlow']`, 10) 
            
            if(labtestlowIndex?.value) {
                const val = labtestlowCn[labtestlowIndex?.value];
                let enIndexs;
                let enIndex;
                if(val){
                    enIndexs = enData?.reduce((acc, currentValue, index) => {
                        if (isEntoCn(currentValue, originLabtestSet[val.index], cnData[index])) {
                            acc.push(index);
                        }
                        return acc;
                    }, []);
                    
                    enIndex = enData?.findIndex?.((item, index) => isEntoCn(item, val, undefined) && enIndexs.includes(index))
                    
                    if(typeof enIndex === 'number' && enIndex > -1) {
                        labtestSet[index] = {
                            ...originLabtestSet[val.index],
                            labtestreptd: inputData?.[enIndex]?.labtestreptd,
                            labtest: inputData?.[enIndex]?.labtest,
                            labtestlow: inputData?.[enIndex]?.labtestlow,
                            labtesthigh: inputData?.[enIndex]?.labtesthigh,
                            TXT_labunit_0: inputData?.[enIndex]?.TXT_labunit_0,
                            enIndexs: enIndexs
                        };
                    }
                }
                
            }
        }
    }
    if(labtestSet?.length < originLabtestSet?.length){
        labtestSet = [...originLabtestSet]
    }    
    // 创建输出对象
    const output = {
        labtest_0: labtestSet,
        labdate_0: examineDateSet,
        data: []
    };

    for(let index = 0; index < inputData?.length; index++){
        let item = inputData[index]
        const examineDateIndex = examineDateSet.indexOf(item.examineDate);
        const labtestSetIndex = labtestSet.findIndex((lab) => {
            const enItem = isBilingual ? (isBilingual === 'isBilingual' ? item : enData[index]) : item
            const cnItem = isBilingual ? (isBilingual === 'bilingual' ? item : cnData[index]) : undefined;
            // if(lab?.enIndexs) return isEntoCn(item, lab, isBilingual ? (isBilingual === 'isBilingual' ? cnData[index] : enData[index]) : undefined, isBilingual)
            return isEntoCn(enItem, lab, cnItem)
        });
        const labtestlowItem = {
            labtestreptd: item.labtestreptd,
            labtest: item.labtest,
            labtestlow: item.labtestlow,
            labtesthigh: item.labtesthigh,
            TXT_labunit_0: item.TXT_labunit_0,
            labtestreptdCn: cnData?.[index]?.labtestreptd,
            labtestCn: cnData?.[index]?.labtest,
            labtestlowCn: cnData?.[index]?.labtestlow,
            labtesthighCn: cnData?.[index]?.labtesthigh,
            TXT_labunit_0Cn: cnData?.[index]?.TXT_labunit_0,
            index: labtestSetIndex
        }
        if(isBilingual === 'isBilingual') labtestlowCn[(labtestSetIndex + 1) + ''] = labtestlowItem
        const newItem = {
            ...item, 
            [`labtestlow`]: isBilingual === 'isBilingual' ? (labtestSetIndex + 1) : item.labtestlow,
            [`TXT_labunit_0_${index}`]: item.TXT_labunit_0, 
            [`cell_${labtestSetIndex}_${examineDateIndex}`]: item.qualitativeResultType, 
            [`TXT_labassess_${labtestSetIndex}`]: item[`TXT_labassess_${labtestSetIndex}_0_0`], 
            [`labdate_${examineDateIndex}`]: item.examineDate, 
            [`labtestIndex`]: labtestSetIndex
        }; 
        // 删除不需要的字段 
        delete newItem.TXT_labunit_0; 
        delete newItem[`TXT_labassess_${index}_0_0`]; 
        output.data.push(newItem);
    }
    if(isBilingual === 'bilingual') {
        // 提取并排序
        output.data = output.data.sort((a, b) => {
            // 找到对象中以 'cell_' 开头的属性
            const keyA = Object.keys(a).find(key => key.startsWith('cell_'));
            const keyB = Object.keys(b).find(key => key.startsWith('cell_'));
            // 提取出第一个数字
            const numA = parseInt(keyA.split('_')[1], 10);
            const numB = parseInt(keyB.split('_')[1], 10);
            return numA - numB;
        });
    }

    return output;
}
// 获取事件评价的每行编号ID
function getEventDetailId(productRowElement) {
    const elements = productRowElement?.getElementsByTagName('*');
    const eventRowElements = [];
    // 遍历所有子元素
    for (let j = 0; j < elements?.length; j++) {
        const childElement = elements[j];
        // 如果元素的id包含Event_Row_，添加到数组中
        if (childElement.id.includes('TXT_act_taken_id')) {
            eventRowElements.push(childElement);
        }
    }
    const eventRowId = eventRowElements?.[0]?.id?.match(/TXT_act_taken_id(\d+)/)?.[1]
    return eventRowId
}
// 根据JSON数据给的报告药品名称与页面的评价列表上的药品名称进行匹配后获取该药品对应的行数据
function getProductNameList(record, productNameList, attributeName) {
    if (!record || !productNameList) return null;
    // 1. 先进行完全匹配
    for (let pro_name in productNameList) {
        if (pro_name?.trim() === record?.trim()) {
            if (productNameList[pro_name].every(item => item.getAttribute(attributeName))) continue;
            return productNameList[pro_name];
        }
    }
    // 2. 如果完全匹配失败，进行包含匹配
    for (let pro_name in productNameList) {
        if (pro_name?.trim()?.includes(record?.trim()) || 
            record?.trim()?.includes(pro_name?.trim())) {
            if (productNameList[pro_name].every(item => item.getAttribute(attributeName))) continue;
            return productNameList[pro_name];
        }
    }
    // 3. 如果都没有匹配到，返回null
    return null;
}

async function handleEvent(recordData, eventInfo, reportLocale, reportCountry, taskId, bilingual, originLabtestSet, refExamineTable, isBilingual, isSuppAdmis) {
    let element;
        // 多记录时，不需要新增
    if(eventInfo.path.startsWith('multiple-records@')){
        element = await waitDom(getDocument(), eventInfo.path.match(/multiple-records@(.*)/)[1] + "2']");
    }else if(eventInfo.path.startsWith('multiple@')){
        let relPrefix = eventInfo.path.match(/multiple@(.*)/)[1];
        if(eventInfo.upgrade){
            relPrefix = getPrefix([...eventInfo.upgrade])
        }
        element = await waitDom(getDocument(), relPrefix + "0")
    }else element = await waitDom(getDocument(), eventInfo.path);
    if (element) {
        if (eventInfo.loop && eventInfo.records) {
            let examInfoList = eventInfo.records
            // 实验室检查录入数据
            if(['addlabtest', 'adddate', 'Pat_LabData_Content_Div', 'labcontentheader'].includes(eventInfo.path)){
                const examInfo = await transformData(reportData, refExamineTable, originLabtestSet, isBilingual)
                examInfoList = ['addlabtest', 'Pat_LabData_Content_Div'].includes(eventInfo.path) ?  [...(examInfo.labtest_0 || [])] : [...(examInfo.labdate_0 || [])]
            }
            if(['labcontentheader', 'Pat_LabData_Content_Div'].includes(eventInfo.path)) return;
            for (let index = 0; index < examInfoList.length; index++) {
                let addStudyDrugFlag = true;
                const record = eventInfo.records[index]
                // 处理新建药品，无法创建
                while (true) {// 创建一个超时承诺
                    await waitForPageReload(eventInfo.waitTime || 1000);

                    console.info('----------------', element?.id, eventInfo.path)
                    // 试验用药通过右键新建，如果不存在，则需要通过默认方式
                    if ("//tr[@id='tr_CF_PRD']/td[position()=last()-1]" === eventInfo.path && record['drug_type_0'] === '1' && addStudyDrugFlag && /^\d+$/.test(record?.['pat_exposure'])) {
                        // 如果右键无法创建，则需要按照普通药品的方式添加，普通方式有概率无法添加成功
                        if ((await addStudyDrug())) {
                            addStudyDrugFlag = false;
                            break;
                        }else {
                            throw(`${record['product_name']}药的pat_exposure是数字但无法右键新增, 请修改数据`)
                        }
                    }

                    // 多记录时点击事件需判断，不需要新增
                    if (eventInfo.path.includes('multiple-records@')) {
                        const baseXPath = eventInfo.path.match(/multiple-records@(.*)/)[1];
                        let xPath;
                        const suspectedDrugLength = processDrugRecords(recordData?.drugInfo?.["试验用药"], "//*[@id='td_CF_Tab_3']", bilingual)?.length || 0;
                        switch (eventInfo.type) {
                            case 'Combination medication':
                                xPath = `${baseXPath}${suspectedDrugLength + index + 1}']`;
                                break;
                            case 'Therapeutic medication':
                                const additionalLength = processDrugRecords(recordData?.drugInfo?.["合并用药"], "//*[@id='td_CF_Tab_3']")?.length || 0;
                                xPath = `${baseXPath}${suspectedDrugLength + additionalLength + index + 1}']`;
                                break;
                            default:
                                xPath = `${baseXPath}${index + 2}']`;
                                break;
                        }
                        
                        element = await waitDom(getDocument(), xPath);
                        if (eventInfo.records?.length) {
                            triggerEvent(element, eventInfo.event);
                            if(eventInfo.checkId){
                                await waitForPageReload(2000)
                                let checkCount = 0;
                                while(!await waitDom(getDocument(), eventInfo.checkId, 100)){
                                    checkCount++;
                                    triggerEvent(element, eventInfo.event);
                                    await waitForPageReload(2000)
                                    if(checkCount > 30) throw '新建药品加载失败，请查看网速是否正常'
                                }
                            }
                        }
                    } else if(eventInfo.path.includes('multiple@')){
                        const baseXPath = eventInfo.path.match(/multiple@(.*)/)[1];
                        element = await waitDom(getDocument(), baseXPath + index);
                        triggerEvent(element, eventInfo.event);
                    }else {
                        element = await waitDom(getDocument(), eventInfo.path);
                        if(eventInfo.path.includes('/td[position()=last()-1]')){
                            while(!element.title.includes(reportLocale === 'en' ? 'New' : '新建')){
                                element = await waitDom(getDocument(), eventInfo.path);
                            }
                        }
                        triggerEvent(element, eventInfo.event);
                        
                        if(eventInfo.checkId){
                            await waitForPageReload(2000)
                            let checkCount = 0;
                            while(!await waitDom(getDocument(), eventInfo.checkId, 100)){
                                checkCount++;
                                triggerEvent(element, eventInfo.event);
                                await waitForPageReload(2000)
                                if(checkCount > 30) throw '新建药品加载失败，请查看网速是否正常'
                            }
                        }
                        if (element) element = null; // 清除旧的引用
                    }
                    
                    await waitForPageReload(1000)

                    // 随访报告
                    if (eventInfo.path === 'btnAddFollow') {
                        let followCount = 0;
                        while(isDialog()){
                            await clickFun("//*[@id='btnNo']", reportLocale)
                            followCount++;
                            await waitForPageReload(1000);
                            if(followCount > 10) break;
                        }
                    }

                    // 工作日志
                    if (eventInfo.path === 'CL_Add') {
                        triggerEvent(getDocument().getElementById(`TheContactLog_${index}`), 'click')
                    }

                    if ("//tr[@id='tr_CF_PRD']/td[position()=last()-1]" === eventInfo.path) {
                        await waitForPageReload(3000)
                        let flagElem;
                        while (!flagElem) {
                            const elements = [];
                            const tr_CF_PRD = await waitDom(getDocument(), "tr_CF_PRD")
                            const trCFPRDList = tr_CF_PRD?.getElementsByTagName('*')
                            // 遍历所有子元素
                            for (let j = 0; j < trCFPRDList?.length; j++) {
                                const trCFPRD = trCFPRDList?.[j];
                                // 如果元素的id包含Event_Row_，添加到数组中
                                if (trCFPRD?.id?.includes('tr_CF_PRD')) {
                                    elements.push(trCFPRD);
                                }
                            }
                            const xPath = "//td[contains(@title, '" + (reportLocale === 'cn' ? '产品名称 #' : 'Product #') + (elements?.length-2) + "')]"
                            flagElem = await waitDom(getDocument(), xPath);
                            element = await waitDom(getDocument(), eventInfo.path);
                            triggerEvent(element, eventInfo.event);
                            if (element) element = null; // 清除旧的引用
                            await waitForPageReload(3000)
                            flagElem = await waitDom(getDocument(), xPath);
                        }
                        triggerEvent(flagElem, 'click')
                        break;
                    }else {
                        break
                    }
                }
                
                if((eventInfo.path.includes('tr_CF_PRD') || eventInfo.path.includes('td_CF_PRD_1')) && record['drug_type_0'] === '1' && !bilingual){
                    if(record?.['pat_exposure']){
                        // 数字试验用药
                        if(/^\d+$/.test(record?.['pat_exposure'])) {
                            while(getDocument().getElementById('pat_exposure')?.value != record['pat_exposure']){
                                await fillSingleField('pat_exposure', record['pat_exposure'], getDocument(), bilingual);
                                await waitForPageReload(2000)
                            }
                            // WHODRUG
                        } else if(record?.['pat_exposure']?.toUpperCase() === 'WHODRUG'){
                            if(record['product_name_lltname']){
                                // 如果是怀疑用药且包含whodrug编码字段，则需要进行whodrug编码
                                // 最多尝试3次
                                let product_count = 1;
                                await combineDrugCode(record['product_name_lltname'], reportCountry, record?.['product_name'], product_count)
                            }else {
                                await assignDrugName(record['product_name'])
                            }
                            // pat_exposure其他值
                        } else  {
                            triggerEvent(getDocument().getElementById('btn_ProductSelect'), 'click');
                            await waitForPageReload(1000);
                            await fillField('search_tradename', record['product_name'], getDialog())
                            await waitForPageReload(1000);
                            let searchButton = await waitDom(getDialog(), 'b_Search')
                            while(searchButton && getDialog()){
                                    // 点击搜索
                                    await waitForPageReload(500);
                                    triggerEvent(searchButton, "click");
                                    await waitForPageReload(2000);
                                    const xpath = `//table[@id='table_tradename']//tbody//tr//td//span[contains(@id, 'family') and contains(text(), "${record?.['pat_exposure']}")]`;
                                    let rowElements = getElement(getDialog(), xpath);
                                    // 如果国家存在，则清除国家，再查询一次
                                    if (!rowElements) {
                                        if(getDialog()?.getElementById('search_country')?.value){
                                            await clearSingleField('search_country', getDialog())
                                            continue;
                                        }
                                    }
                                    let tradenamesCount = 0;
                                    while(!rowElements){
                                        rowElements = getElement(getDialog(), xpath);
                                        tradenamesCount++;
                                        if(tradenamesCount > 20) break;
                                        await waitForPageReload(3000);
                                    }
                                    if(rowElements) {
                                        triggerEvent(rowElements, 'click');
                                        await waitForPageReload(1000);
                                        // 确认
                                        const OK = await waitDom(getDialog(), "b_Select")
                                        if(OK) triggerEvent(OK, "click");
                                        await waitForPageReload(2000);
                                        break;
                                    }else{
                                        const cancelbtn = await waitDom(getDialog(), "cancelbtn")
                                        if(cancelbtn) triggerEvent(cancelbtn, "click");
                                        await waitForPageReload(1000);
                                        throw(`怀疑用药${record['product_name']}匹配失败,请检查数据`)
                                    }
                                }
                           
                            
                        }
                    }else{
                        throw('pat_exposure字段为空')
                    }
                }

                await waitForPageReload(1000)

                // 如果是合并用药且包含whodrug编码字段，则需要进行whodrug编码
                if (record && record['drug_type_1'] === '1') {
                    // 最多尝试3次
                    let product_com_count = 1;
                    if(record['product_name_lltname']) await combineDrugCode(record['product_name_lltname'], reportCountry, record?.['product_name'], product_com_count)
                    else {
                        await assignDrugName(record['product_name'])
                    }
                }               

                // 事件-事件评估
                if(eventInfo.path === "//*[@id='td_CF_EVENT_2']"){
                    if(!bilingual){
                        const evaluationForm = getEvaluationFormList()
                        const productNameList = getProductNameList(record?.product_name, evaluationForm, 'hasvalue')
                        if(!productNameList) throw(`未匹配到${Object.keys(evaluationForm || {}) + ''}=>${record?.product_name}药品事件评估`)
                        // 遍历所有productRowElements
                        const rowValue = productNameList.find((productRowElement, index) => {
                            // 获取评价单个行的事件名称
                            const studyName = productRowElement.querySelectorAll(".label-hyp-italic")?.[0]?.textContent
                            const r = studyName.includes(record['desc_reptd'])
                            if(index === productNameList.length - 1 && (!r || productRowElement.getAttribute('hasvalue'))) {
                                throw(`未匹配到${record?.product_name}药品下的${studyName}->${record['desc_reptd']}事件评估`)
                            }
                            if(productRowElement.getAttribute('hasvalue')) return false
                            return r
                        });
                        if(rowValue){
                            const eventRowId = rowValue.getAttribute('event_assess_seq_num')
                            await setDetListId(rowValue, record.det_list_id, reportLocale)
                            for (let [i, item] of record['评价列表'].entries()) {
                                for (let key in item) {
                                    if(['product_name', 'desc_reptd', '评价列表', 'det_list_id'].includes(key)){
                                        continue;
                                    }
                                    const domId = `${evaluationMap.get(i)}${key}_${eventRowId}`;                           
                                    if(checkFieldBilingualInput(domId) || !bilingual){
                                        await simulateInput(getDocument(), domId, item[key], bilingual)
                                        await closeEventAssJust(reportLocale)
                                    };
                                }
                            }
                            rowValue.setAttribute('hasvalue', 'hasvalue')
                            await wait(3000);
                        }else throw(`未匹配到${record?.product_name}药品事件评估`)
                    }
                // 事件-产品-有害事件详情
                }else if(eventInfo.path === "//*[@id='td_CF_EVENT_3']"){
                    if(!bilingual){
                        const evaluationForm = getEvaluationFormList()
                        const productNameList = getProductNameList(record?.product_name, evaluationForm, 'hasvaluedetail')
                        if(!productNameList) throw(`未匹配到${Object.keys(evaluationForm || {})+''}->${record?.product_name}药品事件详细评估`)
                         // 遍历所有productRowElements
                        const rowValue = productNameList.find((productRowElement, index) => {
                            // 获取评价单个行的事件名称
                            const studyName = productRowElement.querySelectorAll(".label-hyp-italic")?.[0]?.textContent
                            const r = studyName.includes(record['desc_reptd'])
                            if(index === productNameList.length - 1 && (!r || productRowElement.getAttribute('hasvaluedetail'))) {
                                throw(`未匹配到${record?.product_name}药品下的${studyName}->${record['desc_reptd']}事件详细评估`)
                            }
                            if(productRowElement.getAttribute('hasvaluedetail')) return false
                            return r
                        });
                        if(rowValue){
                            const eventRowId = getEventDetailId(rowValue)
                            for (let k in record) {
                                if(['product_name', 'desc_reptd', '评价列表', 'det_list_id'].includes(k)){
                                    continue;
                                }
                                if(k.includes('challenge')){
                                    await processFields(recordData, {
                                        [k+eventRowId]: record[k]
                                    }, reportLocale, reportCountry, taskId, bilingual, originLabtestSet, refExamineTable, isBilingual);
                                }else if(checkFieldBilingualInput(k+eventRowId) || !bilingual) {
                                    await simulateInput(getDocument(), `${k}${eventRowId}`, record[k], bilingual, reportLocale)
                                }
                            }
                            rowValue.setAttribute('hasvaluedetail', 'hasvaluedetail')
                        }else throw(`未匹配到${record?.product_name}药品事件详细评估`)
                    }
                }else {
                    await processFields(recordData, record, reportLocale, reportCountry, taskId, bilingual, originLabtestSet, refExamineTable, isBilingual);
                }
                                    
                if(eventInfo.path === "NA_Add" && record.attachmentUrl){
                    await uploadFile(record.attachmentUrl, record.attachmentName, index, reportLocale, taskId)
                }

                if(eventInfo.path.includes('tr_CF_DOSEREG') && record?.['dose_description']){
                    await waitForPageReload(100)
                    await fillField('dose_description', record?.['dose_description'])
                }
                
                await waitForPageReload(1000)
                
                // 判断药品是否有名称，没有则重新赋值product_name
                if("//tr[@id='tr_CF_PRD']/td[position()=last()-1]" === eventInfo.path || eventInfo.path.includes("//*[@id='td_CF_PRD_")){
                    if(!getDocument().getElementById('product_name')?.value){
                        await simulateInput(getDocument(), 'product_name', record?.['product_name'])
                    }
                    if(/^\d+$/.test(record?.['pat_exposure']) && getDocument().getElementById('pat_exposure')?.value != record['pat_exposure']){
                        await fillSingleField('pat_exposure', record['pat_exposure'], getDocument(), bilingual);
                        await waitForPageReload(2000)
                    }
                }

                if("//tr[@id='tr_CF_PRD']/td[position()=last()-1]" === eventInfo.path && eventInfo.type === 'Experimental medication' && isSuppAdmis && !recordData?.['drugInfo']?.['合并用药']?.length){
                    await sortExperMedic(index + 2)
                }

                if(eventInfo.type === 'Combination medication' && record?.coverProductName){
                    await simulateInput(getDocument(), 'product_name', record?.['product_name'])
                }

                while (isDialog()) {
                    await closeDialog(reportLocale);
                }
            }                
        } else {
            triggerEvent(await waitDom(getDocument(), eventInfo.path, 3000), eventInfo.event);
            if(eventInfo.checkId){
                let checkCount = 0;
                while(!await waitDom(getDocument(), eventInfo.checkId, 100)){
                    checkCount++;
                    triggerEvent(await waitDom(getDocument(), eventInfo.path, 3000), eventInfo.event);
                    await waitForPageReload(2000)
                    if(checkCount > 30) throw '新建药品加载失败，请查看网速是否正常'
                }
            }
            await waitForPageReload(2000)
        }
    } else {
        console.log(eventInfo.path, '找不到')
        while (isDialog()) {                
            await closeDialog(reportLocale);
        }
    }
}
function getPrefix(primaryId, secondaryId) {
    // 先使用 getDocument() 方式获取元素
    if (getDocument().getElementById(primaryId)) {
      return primaryId + '_';
    }
    // 如果上面没有，再使用全局 document 获取
    if (getDocument().getElementById(secondaryId)) {
      return secondaryId + '_';
    }
    return null;
  }

async function processFields(recordData, fields, reportLocale, reportCountry, taskId, bilingual, originLabtestSet, refExamineTable, isBilingual, isSuppAdmis) {
    const encodedPattern = [
        /^(?:Rel_Hist_Table|CPH)_\d+_pat_hist_rptd$/,
        /^CSDD_\d+_cause_reptd$/,
        /^Ind_Table_\d+_ind_reptd$/,
        /^desc_coded$/,
        /^desc_reptd$/
    ] // 判断是否是双语报告需要先编码再赋值的字段
    function encodeInputSkip(key) {   
        // 检查字段是否符合动态字段的正则表达式模式
        if(encodedPattern.some(pattern => pattern.test(key))) return true;
    }
    // 解决双语编码后，中文录入编码字段系统自动选择后会影响英文编码好的数据
    // 解决方案：双语报告不录入编码字段，直接点击编码按钮进行编码，编码完成后直接赋值，不触发系统编码自动录入
    async function inputEncodeSkip(key, value) {
        // 检查字段是否符合动态字段的正则表达式模式
        if(encodedPattern.some(pattern => pattern.test(key))) {
            const ele = await waitDom(getDocument(), key);
            if(ele) {
                ele.value = value
                ele.dispatchEvent(new Event('input', { bubbles: true }));
            }
            
            // await fillSingleField(key, value, getDocument(), bilingual);
        };
    }
    let relPrefix = getPrefix('Rel_Hist_Table', 'CPH')
    let racePrefix = getPrefix('Pat_Race_Table', 'CPR')
    for (let key in fields) {
        if (fields.hasOwnProperty(key)) {
            if(encodeInputSkip(key)) continue;
            const value = fields[key];
            if(key.startsWith('Rel_Hist_Table') || key.startsWith('TXT_Rel_Hist_Table')){
                key = key.replace('Rel_Hist_Table_', relPrefix)
            }
            if(key.startsWith('TXT_Pat_Race_Table')){
                key = key.replace('Pat_Race_Table_', racePrefix)
            }
            if (Array.isArray(value)) {
                for (const item of value) {
                    await processFields(recordData, item, reportLocale, reportCountry, taskId, bilingual, originLabtestSet, refExamineTable, isBilingual, isSuppAdmis);
                }
            } else if (typeof value === 'object') {
                await processNode(recordData, value, reportCountry, reportLocale, taskId, bilingual, originLabtestSet, refExamineTable, isBilingual, isSuppAdmis);
            } else if (key.includes('TXT_') || key.includes('country')){
                if(checkFieldBilingualInput(key) || !bilingual) await simulateInput(getDocument(), key, value, bilingual, reportLocale)
            }else {
                if(key === 'pat_exposure') continue;
                if(key === 'TXT_formulation_id') await waitForPageReload(1000)
                await fillSingleField(key, value, getDocument(), bilingual);
                if(['drug_type_1', 'drug_type_0'].includes(key)) await waitForPageReload(3000)
                if(key.includes('amendment') && value === '2'){
                    const justificationKey = Object.keys(fields).find(k => k.endsWith('justification'));
                    if(!fields[justificationKey]) {
                        fields[justificationKey] = reportLocale === 'en' ? 'Not specified' : '确认无说明'
                    }
                    else await clickFun('cancelbtn', reportLocale, undefined, false)
                }
            }
        }
    }

    const meddraField = {
        desc_lltname: {
            // orginName: 'desc_reptd',
            // codeName: 'desc_lltname',
            // encodeButton: 'encode_evt',
            test: (fields, obj) => {
                for (const key in fields) {
                    if (key === 'desc_lltname') {
                        obj.encodeButton = 'encode_evt';
                        obj.codeName = 'desc_lltname';
                        obj.enreptdName = 'desc_reptd';
                        obj.encodeName = 'desc_coded';
                        obj.codeValue = 'inc_term_display';
                        return true;
                    }
                }
                return false;
            }
        },
        [relPrefix + '0_pat_hist_rptd_lltname']: {
            test: (fields, obj) => {
                for (const key in fields) {
                    if (key.endsWith('_pat_hist_rptd_lltname')) {

                        // 使用正则表达式匹配数字
                        const matches = key.match(/\d+/);

                        if (matches) {
                            const number = matches[0];
                            console.log(`提取的数字: ${number}`);
                            const prefix = key.replace(/^(?:TXT_)?(.*_)\d+_.*/, (a, b) => b)
                            obj.currentButton = relPrefix + number;
                            obj.encodeButton = relPrefix + number + '_encode_hist';
                            obj.codeName = prefix + number + '_pat_hist_rptd_lltname';
                            obj.encodeName = relPrefix + number + '_pat_hist_rptd';
                            obj.encodeValueName = prefix + number + '_pat_hist_rptd';
                            obj.codeValue = relPrefix + number + '_pat_hist_llt_code'
                        } else {
                            console.log("未找到数字");
                        }

                        return true;
                    }
                }
                return false;
            }
        },
        Ind_Table_0_ind_reptd: {
            test: (fields, obj) => {
                for (const key in fields) {
                    if (key.endsWith('_ind_reptd_lltname')) {

                        // 使用正则表达式匹配数字
                        const matches = key.match(/\d+/);

                        if (matches) {
                            const number = matches[0];
                            console.log(`提取的数字: ${number}`);

                            obj.currentButton = 'Ind_Table_' + number;
                            obj.encodeButton = 'btnEncodeInd';
                            obj.codeName = 'Ind_Table_' + number + '_ind_reptd_lltname';
                            obj.encodeName = 'Ind_Table_' + number + '_ind_reptd';
                            obj.codeValue = 'Ind_Table_' + number + '_ind_llt_code';
                        } else {
                            console.log("未找到数字");
                        }

                        return true;
                    }
                }
                return false;
            }
        },
        CSDD_0_cause_reptd: {
            test: (fields, obj) => {
                for (const key in fields) {
                    if (key.endsWith('_cause_reptd_lltname')) {

                        // 使用正则表达式匹配数字
                        const matches = key.match(/\d+/);

                        if (matches) {
                            const number = matches[0];
                            console.log(`提取的数字: ${number}`);

                            obj.currentButton = 'CSDD_' + number;
                            obj.encodeButton = 'CSDD_' + number + '_btnEncode';
                            obj.codeName = 'CSDD_' + number + '_cause_reptd_lltname';
                            obj.encodeName = 'CSDD_' + number + '_cause_reptd';
                            obj.codeValue = 'CSDD_' + number + '_cause_llt_code';
                        } else {
                            console.log("未找到数字");
                        }

                        return true;
                    }
                }
                return false;
            }
        },
    };

    let meddraFieldConfig;

    for (const key in meddraField) {
        if (fields && meddraField[key].test(fields, meddraField[key])) {
            meddraFieldConfig = meddraField[key];
            break;
        }
    }    

    // meddra编码
    if (fields && meddraFieldConfig && fields[meddraFieldConfig.codeName]) {
        // 回填原始值
        if (meddraFieldConfig.orginName) {
            await simulateInput(getDocument(), meddraFieldConfig.orginName, fields[meddraFieldConfig.orginName], bilingual, reportLocale)
        }

        await waitForPageReload(2000);

        // 使用正则表达式匹配名称和编码
        const regex = /^(\d+)\((.*)\)$/;
        const matches = fields[meddraFieldConfig.codeName].match(regex);
        
        // 编码除事件外，其他编码在双语中文中不需要录入
        // if(meddraFieldConfig.codeName !== 'desc_lltname' && bilingual){
        //     return;
        // }

        if (matches) {
            let codeCount = 0;
            const name = matches[2];
            const code = matches[1];
            while(!getDocument().getElementById(meddraFieldConfig.codeValue)?.value.includes(code+'')){
                codeCount++;
                console.log(`meddra名称: ${name}`);
                console.log(`meddra编码: ${code}`);
    
                let meddraDialog = getDialog('MedDRA');
                // 选中当前选型
                if (meddraFieldConfig.currentButton) {
                    // 点击编码按钮
                    let element = await waitDom(getDocument(), meddraFieldConfig.currentButton);
                    triggerEvent(element, "click");
                    await waitForPageReload(2000);
                    element = null;
                }
    
                // 如果不存在，则触发meddra弹框
                if (!meddraDialog) {
                    // 点击编码按钮
                    let element = await waitDom(getDocument(), meddraFieldConfig.encodeButton);
                    triggerEvent(element, "click");
                    await waitForPageReload(2000);
                    element = null;
                }
                
                triggerEvent(getElement(getDialog('MedDRA'), 'bt_clear'))
                await waitForPageReload(400);
                while(getDialog('MedDRA') && !getElement(getDialog('MedDRA'), 'search_llt')){
                    await waitForPageReload(2000);
                }
                // 输入标准术语
                await fillField('search_llt', name, getDialog('MedDRA'))
                await waitForPageReload(2000);
    
                if(getDialog('MedDRA')){
                    let lltname = null;
                    let noDrugRecord = null;
                    lltname = await checkIfDataQueried(noDrugRecord, lltname, getDialog('MedDRA'), '//*[@id="TERM_SOC"]', "//tr[starts-with(@id, 'LLT_row_') and .//td[@code='" + code + "']]\n", '/Dictionary/MedDRA_browser_Ajax.asp', code)
                    if (lltname) {
                        lltname = await waitDom(getDialog('MedDRA'), "//tr[starts-with(@id, 'LLT_row_') and .//td[@code='" + code + "']]\n", 300);
                        triggerEvent(lltname, "click");
                        const startTime = Date.now();
                        const maxWaitTime = 5 * 60 * 1000; // 5分钟转换为毫秒
                        while(!getElement(getDialog('MedDRA'), 'CODE_LLT')?.textContent?.includes(code)) {
                            if (Date.now() - startTime >= maxWaitTime) {
                                throw `等待超过5分钟，未找到包含指定 ${code} 的编码`;
                            }
                            await waitForPageReload(2000);
                        }
                        // 确认
                        const OK = await waitDom(getDialog('MedDRA'), "bt_select", 100)
                        triggerEvent(OK, "click");
                        await waitForPageReload(1000);
                    }else {
                        // 取消
                        const cancel = await waitDom(getDialog('MedDRA'), "bt_cancel", 100) || await waitDom(getDialog("MedDRA"), "btnCancel", 10)
                        if(cancel) triggerEvent(cancel, "click");
                        await waitForPageReload(1000);
                        throw(`${fields[meddraFieldConfig.codeName]} meddra编码未找到`)
                    }
                    meddraDialog = null;
                }
                if (codeCount > 3) throw(`${fields[meddraFieldConfig.codeName]} meddra编码未找到`);
            }
            await inputEncodeSkip(meddraFieldConfig.encodeName, fields[meddraFieldConfig.encodeValueName || meddraFieldConfig.encodeName])
            if(meddraFieldConfig.enreptdName) await inputEncodeSkip(meddraFieldConfig.enreptdName, fields[meddraFieldConfig.enreptdName])
        } else {
            console.log("匹配失败");
        }
    }

    // 导致住院，需要在弹出框中赋值，否则会导致时间错乱
    if (fields && fields['sc_hosp'] === '1') {
        await waitForPageReload(500);
        while(isDialog()) await closeDialog();
        const element = await waitDom(getDocument(), 'sc_hosp');

        await element.click();
        await waitForPageReload(500);
        await element.click();
        await waitForPageReload(500);

        if (fields['start_date']) {
            await fillSingleField('start_date', fields['start_date'], getDialog())
        }

        if (fields['end_date']) {
            await fillSingleField('end_date', fields['end_date'], getDialog())
        }

        if (fields['event_caused']) {
            await fillSingleField('event_caused', fields['event_caused'], getDialog())
        }

        if (fields['prolonged']) {
            await fillSingleField('prolonged', fields['prolonged'], getDialog())
        }

        await waitForPageReload(500);
        const OK = await waitDom(getDialog('EventHospitalization'), "btn_OK")
        triggerEvent(OK, "click");
        await waitForPageReload(500);
    }

    // 妊娠数据需要在字段展示出来之后，再赋值
    if (fields && fields['pat_stat_pregnant']) {
        await fillSingleField('pat_stat_pregnant', fields['pat_stat_pregnant'], getDocument())
    }
}

async function processNode(recordData, node, reportCountry, reportLocale = 'cn', taskId, bilingual, originLabtestSet, refExamineTable, isBilingual, isSuppAdmis) {
    if (node?.events) {
        for (const eventInfo of node.events) {
            await handleEvent(recordData, eventInfo, reportLocale, reportCountry, taskId, bilingual, originLabtestSet, refExamineTable, isBilingual, isSuppAdmis);
        }
    }
    if (node?.fields) {
        for (const fieldInfo of node.fields) {
            await processFields(recordData, fieldInfo, reportLocale, reportCountry, taskId, bilingual, originLabtestSet, refExamineTable, isBilingual, isSuppAdmis);
        }
    }
}
// 补录试验用药后，将补录的试验用药排序到合并用药前面
async function sortExperMedic(index) {
    const event = new MouseEvent("contextmenu", {
        bubbles: true,
        cancelable: true,
        view: window,
        button: 2, // 右键是 button 为 2
    });

    // 触发右键
    getElement(getDocument(), "ProdInfo_Table")?.dispatchEvent(event);
    await waitForPageReload(1000);
    let rightClickDeleteButton = getElement( getDocument(), "PopupMenu_prdrearrange" );
    if (rightClickDeleteButton) {
        triggerEvent(rightClickDeleteButton, "click");
        await waitForPageReload(1000);
        rightClickDeleteButton = null;
    }
    await waitForPageReload(2000)

    const Arrange_Table = getDialog().getElementById("Arrange_Table");
    if (!Arrange_Table) {
        console.error("无法找到ID为 'Arrange_Table' 的元素。");
        return;
    }
    // 获取所有数据行，排除表头
    const Arrange_Table_Rows = Arrange_Table.querySelectorAll(
        "tr[id^='Arrange_Table_']"
    );
    const len = Arrange_Table_Rows.length - index;
    let i = 0;
    while (i < len) {
        triggerEvent(getElement(getDialog(), 'b_up'), 'click')
        i++;
    }
    triggerEvent(getElement(getDialog(), 'b_Ok'), 'click')
    await waitForPageReload(2000)
}

/**
 * argus录入
 * @param {*} record 数据 
 * @param {*} isBilingual isBilingual: 是否录入双语第一份数据 bilingual: 是否录入双语第二份数据
 */
async function argus(data, type, originLabtestSet, isSuppAdmis) {
    reportData = data;
    const record = type === 'en' ? reportData.reportDataEn : reportData.reportDataCn
    const taskId = reportData.id;
    const isBilingual = type === 'en' ? (reportData.reportDataCn ? 'isBilingual' : '') : (reportData.reportDataEn ? 'bilingual' : '');
    const reportLocale = record['reportBaseInfo']['reportLanguage'] === 'cn' ? 'cn' : 'en';
    // 报告发生国家
    const reportCountry = record['reportBaseInfo']['country_iso_3'];

    // 是否录入双语第二份数据
    const bilingual = isBilingual === 'bilingual'

    if(!isSuppAdmis){
        await heartbeatRequest(taskId);
        // 修改录入状态
        await request('autoInput/updateStatus', 'post', {taskId: taskId, [reportLocale === 'cn' ? 'inputStatusCn' : 'inputStatusEn']: 1})
    }
    // 实验室检查特殊处理
    async function processLabtest(examInfo) {
        const labDataList = examInfo['data']
        const labtestSet = examInfo['labtest_0']

        if (!labDataList) {
            return;
        }
        let reportIframe = null;

        reportIframe = document.getElementById(mainAppId).contentDocument.getElementById('fm_MainFrame');

        // 向页面注入脚本，以便页面上下文中可以定义函数
        function injectFunction() {
            const script = reportIframe.contentDocument.createElement('script');
            script.src = chrome.runtime.getURL('../js/inject.js');
            (reportIframe.contentDocument.head || reportIframe.contentDocument.documentElement).appendChild(script);
            script.remove();
        }

        injectFunction();

        // 从content script向页面发送消息以调用函数
        function callPageFunction(funcName, args) {
            reportIframe.contentWindow.postMessage({ type: 'INVOKE_PAGE_FUNCTION', funcName: funcName, args: args }, '*');
        }

        function clearLabCache() {
            reportIframe.contentWindow.postMessage({ type: 'clearCache'}, '*');
        }

        async function simulateLeftClick(element) {

            element?.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });

            var event = new MouseEvent('click', {
                bubbles: true,
                cancelable: true,
                view: window
            });
            element.dispatchEvent(event);
        }


        async function fillLabDataField(element, key, value) {
            if(!checkFieldBilingualInput(key) && bilingual) return

            if (!key.includes('labtestlow') && !value) {
                return;
            }

            if (element) {
                if (element.type === 'checkbox') {
                    element.checked = value === '1';
                } else if (element.type === 'radio') {
                    element.click();
                } else {
                    element.value = value;
                    triggerEvent(element, 'input');
                    triggerEvent(element, 'change');
                    triggerEvent(element, 'blur');
                }
                console.log(`Set field ${key} to ${value}`);
                element = null;
            } else {
                console.log(`Element with ID or XPath ${key} not found`);
            }
        }

        async function fillTXTLabassessField(parentElement, key, value) {
            if(!checkFieldBilingualInput(key) && bilingual) return

            // 查找匹配条件的元素
            const elements = parentElement?.querySelectorAll(`[id^="${key}"]`);
            // 遍历找到的元素并赋值
            for (let element of elements) {
                if(element.getAttribute('tabindex'))
                    await multipleSimulateInput(getDocument(), element.id, value, bilingual)
            }
        }


        documentMainFrame = getDocument()

        const labExit = {};

        for (let k = 0; k < labDataList.length; k++) {
            const labData = labDataList[k];
            const i = labData['labtestIndex'];

            if (!labExit[i] && i !== undefined && i !== null) {
                // meddra编码
                // 回填标准值
                // 使用正则表达式匹配名称和编码
                const regex = /^(\d+)\((.*)\)$/;
                const matches = labData['labtest']?.match(regex);

                if (matches && !bilingual) {
                    let codeCount = 0;
                    const name = matches[2];
                    const code = matches[1];
                    while(getElement(getDocument(), `//tr[@id='labtest_${i}']//input[@id='lab_data_llt_code']`)?.value != code){
                        codeCount++;
                        console.log(`名称: ${name}`);
                        console.log(`编码: ${code}`);
                        
                        await waitForPageReload(2000);

                        let meddraDialog = getDialog('MedDRA');
                        // 如果不存在，则触发meddra弹框
                        if (!meddraDialog) {
                            callPageFunction('fn_labtest_select', [i,  1, 'MANUAL']);

                            // 修复实验室检查编码时数据覆盖的问题，本质是因为argus系统的实验室检查的添加按钮没有渲染正确的数据造成的
                            await waitForPageReload(2000);
                        }
                        meddraDialog = null;
                        if(getDialog('MedDRA')){
                            // 输入标准术语
                            while(getDialog('MedDRA') && !getElement(getDialog('MedDRA'), 'search_llt')){
                                await waitForPageReload(2000);
                            }
                            // 输入标准术语
                            await fillField('search_llt', name, getDialog('MedDRA'))
                            await waitForPageReload(2000);
                            try {
                                // 选择标准术语
                                let lltname = getElement(getDialog('MedDRA'), "//tr[starts-with(@id, 'LLT_row_') and .//td[@code='" + code + "']]\n")
                                // let count = 0;
                                let noDrugRecord = null;
                                lltname = await checkIfDataQueried(noDrugRecord, lltname, getDialog('MedDRA'), '//*[@id="TERM_SOC"]', "//tr[starts-with(@id, 'LLT_row_') and .//td[@code='" + code + "']]\n", '/Dictionary/MedDRA_browser_Ajax.asp', code)
                                if (lltname) {
                                    lltname = getElement(getDialog('MedDRA'), "//tr[starts-with(@id, 'LLT_row_') and .//td[@code='" + code + "']]\n")
                                    triggerEvent(lltname, "click");
                                    const startTime = Date.now();
                                    const maxWaitTime = 5 * 60 * 1000; // 5分钟转换为毫秒
                                    while(!getElement(getDialog('MedDRA'), 'CODE_LLT')?.textContent?.includes(code)) {
                                        if (Date.now() - startTime >= maxWaitTime) {
                                            throw `等待超过5分钟，未找到包含指定 ${code} 的编码`;
                                        }
                                        await waitForPageReload(2000);
                                    }
                                    // 确认
                                    const OK = await waitDom(getDialog('MedDRA'), "bt_select", 100)
                                    await simulateLeftClick(OK);
                                    await waitForPageReload(1000);
                                    lltname = null;
                                } else {
                                    // 取消
                                    const cancel = await waitDom(getDialog('MedDRA'), "bt_cancel", 100) || await waitDom(getDialog("MedDRA"), "btnCancel", 10)
                                    if(cancel) triggerEvent(cancel, "click");
                                    await waitForPageReload(1000);
                                }
                                if(getElement(documentMainFrame, `//tr[@id='labtest_${i}']//input[@id='labtest']`))
                                    getElement(documentMainFrame, `//tr[@id='labtest_${i}']//input[@id='labtest']`).value = name
                                // await fillLabDataField(getElement(documentMainFrame, `//tr[@id='labtest_${i}']//input[@id='labtest']`), 'labtest', name)
                            } catch (e) {
                                console.log(e);
                            }
                        }
                        if(codeCount > 3) break;
                    }
                    
                } else if(!matches){
                    console.log("匹配失败");
                }
                if(getElement(getDocument(), `//tr[@id='labtest_${i}']//input[@id='labtestreptd']`))
                    getElement(getDocument(), `//tr[@id='labtest_${i}']//input[@id='labtestreptd']`).value = labData['labtestreptd']
                await fillLabDataField(getElement(getDocument(), `//tr[@id='labtest_${i}']//input[@id='labtestlow']`), 'labtestlow', labData['labtestlow'])
                await fillLabDataField(getElement(getDocument(), `//tr[@id='labtest_${i}']//input[@id='labtesthigh']`), 'labtesthigh', labData['labtesthigh'])

                await waitForPageReload(1000);

                // 判断唯一标识
                function isEntoCn(item, lab, cnItem){
                    if(lab?.labtestreptd === item?.labtestreptd && item?.labtest === lab?.labtest && item?.labtestlow === lab?.labtestlow && item?.labtesthigh === lab?.labtesthigh && item?.TXT_labunit_0 === lab?.TXT_labunit_0 && !cnItem) return true
                    if(lab?.labtestreptdEn === item?.labtestreptd && item?.labtest === lab?.labtestEn && item?.labtesthigh === lab?.labtesthighEn && item?.TXT_labunit_0 === lab?.TXT_labunit_0En && lab?.labtestreptdCn === cnItem?.labtestreptd && cnItem?.labtest === lab?.labtestCn && cnItem?.labtestlow === lab?.labtestlowCn && cnItem?.labtesthigh === lab?.labtesthighCn && cnItem?.TXT_labunit_0 === lab?.TXT_labunit_0Cn) return true
                    return false
                }
               
                const labUnitIndex = labtestSet.findIndex(item => {
                    const TXTLabunitValue = Object.keys(labData).filter(key => key.includes('TXT_labunit_0_')).map(key => labData[key]);
                    labData['TXT_labunit_0'] = TXTLabunitValue?.[0];
                    return isEntoCn(labData, item, isBilingual ? {
                        labtestreptd: labtestlowCn[i + 1]?.labtestreptdCn || labtestlowCn[i + 1]?.labtestreptd,
                        labtest: labtestlowCn[i + 1]?.labtestCn || labtestlowCn[i + 1]?.labtest,
                        labtestlow: labtestlowCn[i + 1]?.labtestlowCn || labtestlowCn[i + 1]?.labtestlow,
                        labtesthigh: labtestlowCn[i + 1]?.labtesthighCn || labtestlowCn[i + 1]?.labtesthigh,
                        TXT_labunit_0: labtestlowCn[i + 1]?.TXT_labunit_0Cn || labtestlowCn[i + 1]?.TXT_labunit_0,
                    }: undefined)
                })
                const labUnit = `TXT_labunit_0_${labUnitIndex}`
                let labUnitElement = getElement(documentMainFrame,`//tr[@id='labtest_${labUnitIndex}']//input[@id='${labUnit}']`)
                if(labUnitElement && !bilingual){
                    await simulateInput(documentMainFrame, `//tr[@id='labtest_${labUnitIndex}']//input[@id='${labUnit}']`, labtestSet[labUnitIndex]['TXT_labunit_0'])
                    await waitForPageReload(3000);
                    triggerEvent(labUnitElement, 'input');
                    triggerEvent(labUnitElement, 'change');
                    triggerEvent(labUnitElement, 'blur');
                    labUnitElement = null;
                }
                labExit[i] = true;
            }

            for (const fieldInfo in labData) {
                if (fieldInfo.toString().indexOf('labdate_') > -1) {
                    const fieldValue = labData[fieldInfo]

                    await fillLabDataField(getElement(documentMainFrame, `//thead[@id='labcontentheader']//input[@id='${fieldInfo}']`), `${fieldInfo}`, fieldValue)
                }

                if (fieldInfo.toString().indexOf('cell_') > -1) {
                    triggerEvent(getElement(documentMainFrame, `//table[@id='crosstab']//td[@id='${fieldInfo}']`), 'click');
                    await waitForPageReload(100, `PopupMenu_EmptyMenuAddData`);
                    triggerEvent(getElement(documentMainFrame, `PopupMenu_EmptyMenuAddData`), 'click');
                    await waitForPageReload(100);

                    await fillLabDataField(getElement(getDocument(), `//table[@id='crosstab']//td[@id='${fieldInfo}']//input[@id='labresult']`), `labresult`, labData['labresult'])
                    await fillTXTLabassessField(getElement(getDocument(), `//table[@id='crosstab']//td[@id='${fieldInfo}']`), `TXT_labassess_`, labData['qualitativeResultType'])

                    await fillLabDataField(getElement(getDocument(), `//table[@id='crosstab']//td[@id='${fieldInfo}']//textarea[@id='labnotes']`), `labnotes`, labData['labnotes'])
                    await fillLabDataField(getElement(getDocument(), `//table[@id='crosstab']//td[@id='${fieldInfo}']//textarea[@id='comments']`), `comments`, labData['comments'])
                }
            }
            while(isDialog()){
                await closeDialog(reportLocale);                
            }
        }

        clearLabCache();
    }

    // Helper function to process drug records
    const processDrugRecords = (drugRecords, type) => {

        if (!drugRecords) {
            return [];
        }
        return drugRecords?.map((drugRecord) => {
            const {
                btnAddInd,
                btnAddIng,
                ...otherDrugInfo
            } = drugRecord;
            const expDoseTable = Array.isArray(drugRecord.expDoseTable) ? [...drugRecord.expDoseTable] : [drugRecord.expDoseTable];
            const firstDose = expDoseTable.length > 0 ? expDoseTable.shift() : undefined;
            
            return {
                ...otherDrugInfo,
                "indication": {
                    "events": [
                        {
                            path: bilingual ? 'multiple@IngredientTable_' : type === '试验用药' ? 'multiple@IngredientTable_' : `//*[@id='btnAddIng']`,
                            event: 'click',
                            waitTime: 2000,
                            loop: true,
                            records: btnAddIng ? btnAddIng : []
                        },
                        {
                            path: bilingual ? 'multiple@Ind_Table_' : `//*[@id='btnAddInd']`,
                            event: 'click',
                            waitTime: 2000,
                            loop: true,
                            records: btnAddInd ? btnAddInd : []
                        },
                    ]
                },
                "expDoseTable": {
                    "events": [{
                            path: `//*[@id='td_CF_DOSEREG_1']`,
                            event: 'click',
                            waitTime: 2000,
                            loop: true,
                            records: firstDose ? [firstDose] : []
                        },
                        {
                            path: bilingual ? "multiple-records@//tr[@id='tr_CF_DOSEREG_" : `//tr[@id='tr_CF_DOSEREG']/td[position()=last()-1]`,
                            event: 'click',
                            waitTime: 2000,
                            loop: true,
                            records: expDoseTable
                        }
                    ]
                }
            };
        });
    };

    // 反向解构对象
    const {
        btnAddClass,
        center_no,
        btnAddFol,
        ...reportBaseInfo
    } = (record.reportBaseInfo || {});
    // 转换中心编号避免由于字段名称造成转换异常
    reportBaseInfo.center_name = center_no;
    delete reportBaseInfo['center_no'];
    const [...reportReporterInfo] = Array.isArray(record.reportReporterInfo) ? record.reportReporterInfo : [record.reportReporterInfo];
    const firstReportInfo = reportReporterInfo?.length > 0 ? reportReporterInfo.shift() : undefined;
    const {
        pat_race_add,
        rel_hist_add,
        refExamineTable,
        ...reportSubjectInfo
    } = (record.reportSubjectInfo || {});
    const deadReasonInfoTable = record?.reportSubjectInfo?.deadReasonInfoTable
    const {
        ...deadInfo
    } = deadReasonInfoTable || {};
    const [...reportSaeDetailInfo] = Array.isArray(record.reportSaeDetailInfo) ? record.reportSaeDetailInfo : [record.reportSaeDetailInfo];
    const firstSae = reportSaeDetailInfo?.length > 0 ? reportSaeDetailInfo.shift() : undefined;
    const evaluateInfo = Array.isArray(record["药品和不良反应评价矩阵"]) ? record["药品和不良反应评价矩阵"] : [record["药品和不良反应评价矩阵"]];
    const {
        ...saeDescribe
    } = (record.saeDescribe || {});
    const {
        // 措施执行日志
        workLog
    } = (record.measure || {});

    const suspectedDrug = processDrugRecords(record.drugInfo?.["试验用药"], '试验用药', "//*[@id='td_CF_Tab_3']", bilingual);
    const firstDrug = suspectedDrug?.length > 0 ? suspectedDrug.shift() : undefined;

    // 附加信息
    const {
        //备注和附件
        reportAttachment,
        //参考信息列表
        referenceInfo
    } = record?.additionalInfo || {}
    

    // 最终生成的data对象
    const execData = {
        "reportBaseInfo": {
            "events": [
                {
                    path: "//*[@id='td_CF_Tab_1']",
                    event: 'click',
                    loop: true,
                    waitTime: 2000,
                    checkId: "//*[@id='Gen_Info_Content_Div']",
                    records: [{
                        ...reportBaseInfo,
                        "reportClass": {
                            "events": [{
                                path: bilingual ? "Class_DataDiv" : "btnAddClass",
                                event: 'click',
                                waitTime: 2000,
                                loop: true,
                                records: btnAddClass ? [...btnAddClass] : []
                            }]
                        },
                        "followClass": {
                            "events": [
                                {
                                    path: bilingual ? "Fol_DataDiv" : "btnAddFollow",
                                    event: 'click',
                                    waitTime: 2000,
                                    loop: true,
                                    records: btnAddFol ? [...btnAddFol] : []
                                }
                            ]
                        }
                    }]
                }
            ]
        },
        "reportReporterInfo": {
            "events": [{
                    path: "//*[@id='td_CF_Tab_1']",
                    event: 'click',
                    waitTime: 2000,
                }, {
                    path: "//*[@id='td_CF_REP_1']",
                    event: 'click',
                    waitTime: 2000,
                    loop: true,
                    records: firstReportInfo ? [firstReportInfo] : []
                },
                {
                    path: bilingual ? "multiple-records@//*[@id='td_CF_REP_" : "//tr[@id='tr_CF_REP']/td[position()=last()-1]",
                    event: 'click',
                    waitTime: 2000,
                    loop: true,
                    records: reportReporterInfo
                }
            ]
        },
        "reportSubjectInfo": {
            "events": [{
                path: "//*[@id='td_CF_Tab_2']",
                event: 'click',
                waitTime: 2000,
                loop: true,
                checkId: "//*[@id='Pat_Info_Content_Div']",
                records: [{
                    ...reportSubjectInfo,
                    ...deadInfo,
                    "raceClass": {
                        "events": [{
                            path: bilingual ? 'Race_Info_Table' : "pat_race_add",
                            event: 'click',
                            waitTime: 2000,
                            loop: true,
                            records: pat_race_add ? [...pat_race_add] : []
                        }]
                    },
                    "deadReasonInfoTable": {
                        "events": [{
                            path: "imgDeathMinimize_det",
                            event: 'click',
                            waitTime: 2000,
                        },{
                            path: bilingual ? "multiple@CSDD_" : "btnAdd",
                            event: 'click',
                            waitTime: 2000,
                            loop: true,
                            records: deadReasonInfoTable?.btnAdd ? [...deadReasonInfoTable?.btnAdd] : []
                        }]
                    },
                    "medicalHistory": {
                        "events": [{
                            path: bilingual ? 'multiple@Rel_Hist_Table_' : "rel_hist_add",
                            upgrade: ['Rel_Hist_Table', 'CPH'],
                            event: 'click',
                            waitTime: 2000,
                            loop: true,
                            records: rel_hist_add ? [...rel_hist_add] : []
                        }]
                    },
                    "labtest_0": {
                        "events": [{
                            path: bilingual ? "Pat_LabData_Content_Div" : "addlabtest",
                            event: 'click',
                            waitTime: 2000,
                            loop: true,
                            records: []
                        }]
                    },
                    "labdate_0": {
                        "events": [{
                            path: bilingual ? "labcontentheader" : "adddate",
                            event: 'click',
                            waitTime: 2000,
                            loop: true,
                            records: []
                        }]
                    }
                }]
            }, ]
        },
        "试验用药": {
            "events": [
                {
                    path: "//*[@id='td_CF_Tab_3']",
                    event: 'click',
                    checkId: "//*[@id='div_tabs_CF_PRD']",
                    waitTime: 2000
                },
                {
                    path: "//*[@id='td_CF_PRD_1']",
                    event: 'click',
                    waitTime: 2000,
                    checkId:"//*[@id='div_tabs_CF_PRD']",
                    loop: true,
                    records: firstDrug ? [firstDrug] : []
                },
                {
                    path: bilingual ? "multiple-records@//*[@id='td_CF_PRD_" : "//tr[@id='tr_CF_PRD']/td[position()=last()-1]",
                    event: 'click',
                    waitTime: 2000,
                    checkId:"//*[@id='div_tabs_CF_PRD']",
                    loop: true,
                    type: 'Experimental medication',
                    records: suspectedDrug
                }
            ]
        },
        "合并用药": {
            "events": [{
                    path: "//*[@id='td_CF_Tab_3']",
                    event: 'click',
                    checkId:"//*[@id='div_tabs_CF_PRD']",
                    waitTime: 2000
                },
                {
                    path: bilingual ? "multiple-records@//*[@id='td_CF_PRD_" : "//tr[@id='tr_CF_PRD']/td[position()=last()-1]",
                    event: 'click',
                    checkId:"//*[@id='div_tabs_CF_PRD']",
                    waitTime: 2000,
                    type: 'Combination medication',
                    loop: true,
                    records: processDrugRecords(record?.drugInfo?.["合并用药"], '合并用药', "//*[@id='td_CF_Tab_3']", bilingual)
                }
            ]
        },
        "治疗用药": {
            "events": [{
                    path: "//*[@id='td_CF_Tab_3']",
                    event: 'click',
                    checkId:"//*[@id='div_tabs_CF_PRD']",
                    waitTime: 2000
                },
                {
                    path: bilingual ? "multiple-records@//*[@id='td_CF_PRD_" : "//tr[@id='tr_CF_PRD']/td[position()=last()-1]",
                    event: 'click',
                    checkId:"//*[@id='div_tabs_CF_PRD']",
                    waitTime: 2000,
                    type: 'Therapeutic medication',
                    loop: true,
                    records: processDrugRecords(record?.drugInfo?.["治疗用药"], '治疗用药', "//*[@id='td_CF_Tab_3']", bilingual)
                }
            ]
        },
        "reportSaeDetailInfo": {
            "events": [{
                    path: "//*[@id='td_CF_Tab_4']",
                    event: 'click',
                    checkId:"//*[@id='tr_CF_EVENT']",
                    waitTime: 2000
                },
                {
                    path: "//*[@id='td_CF_EVENT_1']",
                    event: 'click',
                    checkId:"//*[@id='div_tabs_CF_EVT']",
                    waitTime: 2000,
                },
                {
                    path: "//*[@id='td_CF_EVT_1']",
                    event: 'click',
                    waitTime: 2000,
                    checkId:"//*[@id='div_tabs_CF_EVT']",
                    loop: true,
                    records: firstSae ? [firstSae] : []
                },
                {
                    path: bilingual ? "multiple-records@//*[@id='td_CF_EVT_" : "//tr[@id='tr_CF_EVT']/td[position()=last()-1]",
                    event: 'click',
                    waitTime: 2000,
                    checkId:"//*[@id='div_tabs_CF_EVT']",
                    loop: true,
                    records: reportSaeDetailInfo
                }
            ]
        },
        "药品和不良反应评价矩阵":{
            "events": [
                {
                    path: "//*[@id='td_CF_Tab_4']",
                    event: 'click',
                    checkId:"//*[@id='tr_CF_EVENT']",
                    waitTime: 2000
                },
                {
                    path: "//*[@id='td_CF_EVENT_2']",
                    event: 'click',
                    waitTime: 2000
                },
                {
                    path: "//*[@id='td_CF_EVENT_2']",
                    event: 'click',
                    checkId:"//*[@id='Event_Assess_Table_Main']",
                    loop: true,
                    waitTime: 2000,
                    records: evaluateInfo
                },
                {
                    path: "//*[@id='td_CF_EVENT_3']",
                    event: 'click',
                    waitTime: 2000,
                },
                {
                    path: "//*[@id='td_CF_EVENT_3']",
                    event: 'click',
                    checkId:"//*[@id='Event_Assess_Table_Main']",
                    loop: true,
                    waitTime: 2000,
                    records: evaluateInfo
                }
            ]
        },
        "saeDescribe": {
            "events": [
                {
                    path: "//*[@id='td_CF_Tab_5']",
                    event: 'click',
                    checkId:"//*[@id='tr_CF_ANALYSIS']",
                    waitTime: 2000
                },
                {
                    path: "//*[@id='td_CF_ANALYSIS_1']",
                    event: 'click',
                    loop: true,
                    checkId:"//*[@id='Narr_Content_Div']",
                    waitTime: 2000,
                    records: saeDescribe ? [saeDescribe] : []
                }
            ]
        },
        "measure": {
            "events": [
                {
                    path: "//*[@id='td_CF_Tab_6']",
                    event: 'click',
                    checkId:"//*[@id='Table_Outer_Box']",
                    waitTime: 2000
                },
                {
                    path: bilingual ? 'TheContactLog' : "CL_Add",
                    event: 'click',
                    loop: true,
                    checkId:"//*[@id='TheContactLog']",
                    waitTime: 2000,
                    records: workLog ? [...workLog] : []
                }
            ]
        },
        "additionalInfo": {
            "events": [
                {
                    path: "//*[@id='td_CF_Tab_7']",
                    event: 'click',
                    checkId:"//*[@id='TableNotesAttachDiv']",
                    waitTime: 2000
                },
                {
                    path: bilingual ? 'TableNotesAttachDiv' : "NA_Add",
                    event: 'click',
                    loop: true,
                    waitTime: 2000,
                    records: reportAttachment ? [...reportAttachment] : []
                },
                {
                    path: bilingual ? 'TableReferenceDiv' : "REF_Add",
                    event: 'click',
                    loop: true,
                    waitTime: 2000,
                    records: referenceInfo ? [...referenceInfo] : []
                }
            ]
        }
    };

    async function moduleLogs(logContent, depleteTime) {
        await heartbeatRequest(taskId);
        // 日志
        await request('autoInput/addLog', 'post', {taskId: taskId, depleteTime: depleteTime, logContent: (isSuppAdmis ? '补录：' : '') + reportLocale + '-' + logContent})
    }

    async function executModule(type) {
        await waitForPageReload(1000);
       
        if (type === 'examInfo') {
            const examInfo = await transformData(reportData, refExamineTable, originLabtestSet, isBilingual);
            await processLabtest(examInfo);
        } else {
            await processNode(record, execData[type], reportCountry, reportLocale, taskId, bilingual, originLabtestSet, refExamineTable, isBilingual, isSuppAdmis);
        }
        await waitForPageReload(1000);
        // 每个模块执行完成之后，临时清除一次
        await clearMemory();
    }    

    const moduleMap = new Map([
        ['reportBaseInfo', () => executModule('reportBaseInfo')],
        ['reportReporterInfo', () => executModule('reportReporterInfo')],
        ['reportSubjectInfo', () => executModule('reportSubjectInfo')],
        ['examInfo', () => executModule('examInfo')],
        ['试验用药', () => executModule('试验用药')],
        ['合并用药', () => executModule('合并用药')],
        // 治疗用药不需要录入
        // ['治疗用药', () => executModule('治疗用药')],
        ['reportSaeDetailInfo', () => executModule('reportSaeDetailInfo')],
        ['药品和不良反应评价矩阵', () => executModule('药品和不良反应评价矩阵')],
        ['saeDescribe', () => executModule('saeDescribe')],
        ['measure', () => executModule('measure')],
        ['additionalInfo', () => executModule('additionalInfo')],
    ])

    function hasKey(obj, targetKey) {
        // 处理基础类型和空值
        if (obj === null || typeof obj !== 'object') {
            return false;
        }
    
        // 检查当前对象的直接属性
        if (targetKey in obj) {
            return true;
        }
    
        // 递归检查所有嵌套的对象和数组
        return Object.values(obj).some(value => {
            if (Array.isArray(value)) {
                // 如果是数组，检查数组中的每个元素
                return value.some(item => hasKey(item, targetKey));
            } else if (typeof value === 'object' && value !== null) {
                // 如果是对象，递归检查
                return hasKey(value, targetKey);
            }
            return false;
        });
    }

    async function executeModulesBasedOnLog() {
        // 定义模块的执行顺序
        const moduleKeys = [
            '试验用药',
            '合并用药',
            // '治疗用药',
            'reportSaeDetailInfo',
            '药品和不良反应评价矩阵',
            'reportBaseInfo',
            'reportReporterInfo',
            'reportSubjectInfo',
            'examInfo',
            'saeDescribe',
            'measure',
            'additionalInfo',
        ];
    
        // 从起始索引开始执行模块
        for (let i = 0; i < moduleKeys.length; i++) {
            const key = moduleKeys[i];
            const moduleFunction = moduleMap.get(key);
    
            if (moduleFunction) {
                if(hasKey(record, key) || (['reportSubjectInfo', 'examInfo'].includes(key))) {
                    let timerId = null;
                    let totalElapsed = 0;
                    let startTime = 0;
                    let isPaused = false;
                    let depleteTime = 0;
                    function startTimer() {
                        console.log('开始计时', new Date());
                        if (isPaused) {
                            startTime = performance.now() - totalElapsed;
                        } else {
                            startTime = performance.now();
                        }
                        timerId = requestAnimationFrame(updateTimer);
                        isPaused = false;
                    }
            
                    function stopTimer() {
                        cancelAnimationFrame(timerId);
                        isPaused = true;
                    }
            
                    function updateTimer() {
                        if (!isPaused) {
                            const currentTime = performance.now();
                            totalElapsed = currentTime - startTime;
                            depleteTime = Math.floor(totalElapsed / 1000);
                            timerId = requestAnimationFrame(updateTimer);
                        }
                    }
                    const handleVisibilitychange = () => {
                        if (document.visibilityState === 'hidden') {
                            stopTimer();
                        } else if (document.visibilityState === 'visible' && isPaused) {
                            startTimer();
                        }
                    }
                    startTimer()
                    document.addEventListener('visibilitychange', handleVisibilitychange);
                    // let error = null;
                    // try{
                    //     await moduleFunction();
                    // }catch(e){
                    //     error = e instanceof ReferenceError ? e.message : e;
                    //     throw error
                    // }finally{
                    //     await moduleLogs(key + (error || ''), depleteTime);
                    // }    
                    await moduleFunction();
                    await moduleLogs(key, depleteTime);
                    console.log('结束计时', new Date(), depleteTime);
                    document.removeEventListener('visibilitychange', handleVisibilitychange)
                }
            } else {
                console.log(`No function found for module key "${key}".`);
            }
        }
    }

    triggerEvent(await waitDom(getDocument(), reportLocale === 'cn' ? "TranslateDiv_imgNone_JP" : "TranslateDiv_imgNone_EN"), 'click')
    await waitForPageReload(2000);

    await executeModulesBasedOnLog();
    await waitForPageReload(1000);
}


/**
 * 点击新建报告
 * @returns {Promise<void>}
 */
async function clickNewButton(firstReport){
    // 4. 获取父级 document

    // 5. 定义 MutationObserver 回调函数
    function mutationCallback(mutationsList, observer) {
        for (var mutation of mutationsList) {
            if (mutation.type === 'childList') {
                // 检查是否有目标 div 元素被添加
                let targetDivs = document.getElementById(mainAppId).contentDocument.getElementById('menuPopup');
                // 创建一个数组来存储id包含Event_Row_的元素
                const rowElements = [];
                // 获取productRowElement的所有子元素
                const elements = targetDivs.getElementsByTagName('*');
    
                // 遍历所有子元素
                for (let j = 0; j < elements.length; j++) {
                    const element = elements[j];
                    // 如果元素的id包含Event_Row_，添加到数组中
                    if (element.getAttribute('menu-child')) {
                        rowElements.push(element);
                    }
                }
                let targetDiv = firstReport ? rowElements?.[1] : rowElements?.[0]
                if (targetDiv) {
                    // 找到目标元素，触发点击事件
                    var clickEvent = new MouseEvent('click', {
                        view: window.parent,
                        bubbles: true,
                        cancelable: true
                    });
                    targetDiv.dispatchEvent(clickEvent);

                    // 停止观察
                    targetDivs = null;
                    observer.disconnect();
                    break;
                }
            }
        }
    }

    // 6. 创建 MutationObserver 实例并传入回调函数
    var observer = new MutationObserver(mutationCallback);

    // 7. 开始观察父级 document 的子树变化
    observer.observe(document.getElementById(mainAppId).contentDocument, {
        childList: true,
        subtree: true
    });
    var button = getDocumentByName("fm_TopFrame").querySelector('#td_da');

    // 2. 创建并初始化 MouseEvent 对象
    var mouseOverEvent = new MouseEvent('mouseover', {
        view: window,
        bubbles: true,
        cancelable: true
    });

    // 3. 触发事件
    button?.dispatchEvent(mouseOverEvent);
    button = null;
}

/**
 * 查询报告是否存在
 */
async function searchReport(data, click, isQC){
    console.info('begin searchReport');
    await clickNewButton()
    await waitForPageReload(2000);

    const search_field_type = getDocument().getElementsByName('search_field_type') 
    if(search_field_type?.[0]) {
        search_field_type[0].value = isQC ? 'CASE NUM' : 'Event Verbatim'
        triggerEvent(search_field_type[0], 'change');
    }

    await fillSingleField('search_field_data', isQC ? data.argusNum : data.reportId)

    await fillSingleField('search_date_range', '18')
    
    await wait(1000);

    triggerEvent(getDocument().getElementById('Btn_Search'), 'click')

    await waitForPageReload(2000);
    
    const result_table = await waitDom(getDocument(), 'result_table');
    const trs = result_table.getElementsByTagName('tr')
    // 报告存在这一份
    if(trs[0].id && trs.length === 1){
        if(!isQC) await request('autoInput/updateStatus', 'post', {taskId: data.id, buildStatus: 2, argusNum: getDocument().getElementById('cs_case_number_1')?.textContent?.trim()})
        // 通知后端报告存在
        if(click) {
            triggerEvent(getDocument().getElementById('cs_case_number_1'), 'click');
            // 判断当前报告是否被别人正在查看
            await waitForPageReload(2000);
            if(isDialog()) return 'viewing';
        }
        return 'exist'
    }
    // 报告不存在
    if(!trs[0].id){
        return 'notExist'
    }
    if(trs.length > 1){
        // 通知后端该报告id存在两份报告
        return 'more'
    }    
    return
}

function getTodayDate() {
    // 创建一个新的Date对象，表示当前日期和时间
    let today = new Date();

    // 获取年、月、日
    const year = today.getFullYear();
    const month = today.getMonth() + 1; // 月份从0开始，所以需要加1
    const day = today.getDate();

    // 格式化日期为YYYY-MM-DD
    const formattedDate = `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
    today = null;
    return formattedDate
}

/**
 * 自动新建报告
 * @param data
 * @returns {Promise<void>}
 */
async function createReport(data) {
    try{
        const reportBaseInfo = data.reportDataCn ? data.reportDataCn.reportBaseInfo : {...data.reportDataEn.reportBaseInfoCn, study_num: data.reportDataEn.reportBaseInfo.study_num}
        console.info('begin createReport', reportBaseInfo);
        // 请求修改新建状态的接口
        await request('autoInput/updateStatus', 'post', {taskId: data.id, buildStatus: 1})
        await clickNewButton('createReport');
        await waitForPageReload(2000);
    
        const ids = ["search_init_rept_date", "search_country_text", "search_rpt_event"]
        for (const idsKey of ids) {
            //将idKey根据截取search_后面的字符串
            const key = idsKey.replace("search_", "");
            var value = reportBaseInfo[key]
            if (key === "rpt_event") {
                // 报告术语设置为报告唯一识别编码，方便后期创建成功未给后端返回
                value = data.reportId
            }
            console.log(" reportBaseInfo["+key+"]"+ value)
            if(idsKey === 'search_init_rept_date' && !value){
                await fillSingleField(idsKey, getTodayDate())
            }else {
                await fillSingleField(idsKey, value)
            }
        }
        await waitForPageReload(2000);
        await closeDialog('cn');
    
        //选择报告类型 TXT_report_type
        await fillSingleField("TXT_search_report_type", reportBaseInfo['TXT_report_type'])
        const study_num = reportBaseInfo['study_num'];

        async function StudySearch() {
            //选择 - 检索 - 选择项目id
            const btn_StudySearch = "btn_StudySearch"
                    
            triggerEvent(await waitDom(getDocument(), btn_StudySearch), 'click');
            await waitForPageReload(1000);
            console.log("开始赋值项目id检索")
            await fillSingleField("search_study", study_num, getDialog())
            await waitForPageReload(1000);
            console.log("开始点击检索")
            const btnSearch = "btnSearch"
            triggerEvent(await waitDom(getDialog(), btnSearch), 'click');
            await waitForPageReload(2000);
            const btnSelect = "btnSelect"
            console.log("开始点击选择")
            triggerEvent(await waitDom(getDialog(), btnSelect), 'click');
        }

        if(study_num){
            await StudySearch()
        }else {
            const search_product = data.reportDataEn ? data?.reportDataEn?.drugInfo?.['试验用药']?.[0]?.['product_name'] : data.reportDataCn.drugInfo?.['试验用药']?.[0]?.['product_name']
            await fillSingleField('search_product', search_product)
            await waitForPageReload(200);
        }
        //点击继续
        triggerEvent(await waitDom(getDocument(), 'btn_Continue'), 'click');
        await waitForPageReload(1000);
        //选择等级标准：导致住院或住院时间延长 sc_int_req
        console.log("选择等级标准")
        triggerEvent(await waitDom(getDocument(), 'sc_int_req'), 'click');
    
        await waitForPageReload(1000);
        if(isDialog()){
            await closeDialog();
            console.log('临床试验/研究选择未找到')
        }
    
        // 选择：报告的严重标准
        const reportSaeDetailInfo = data.reportDataCn ? data.reportDataCn['reportSaeDetailInfo']?.[0] : data.reportDataEn['reportSaeDetailInfo']?.[0]
        // 筛选出以 'sc_' 为前缀的为严重标准的字段
        const scFields = Object.entries(reportSaeDetailInfo).filter(([key, _value]) => key.startsWith('sc_'));
        // 将结果转换回对象
        const scData = Object.fromEntries([...scFields, {"med_serious":reportSaeDetailInfo.med_serious}]);
        console.log('报告的严重标准', JSON.stringify(scData));
        for (const key in scData) {
            if (scData.hasOwnProperty(key)) {
                await fillSingleField(key, scData[key])
                triggerEvent(await waitDom(getDocument(), key), 'click');
                await closeDialog('cn')
            }
        }
    
        //选择：报告来源的因果关系（无关）
        // console.log("报告来源的因果关系")
        // await fillSingleField("reporter_causality", "5")
    
        triggerEvent(getElement(getDocument(), "btn_BookInCase"), 'click');
        await waitForPageReload(3000);
    }catch(e){
        console.log(e);
        // 请求修改新建状态的接口
        await request('autoInput/updateStatus', 'post', {taskId: data.id, buildStatus: 3, message: e instanceof ReferenceError ? e.message : e})
    }
    
    await waitForPageReload(4000);
    let txtMessage = null;
    // 获取报告编号
    let caseId = null;
    try {
        // 处理弹出窗口没有关闭
        while (isDialog()) {
            txtMessage = await waitDom(getDialog(), 'txtMessage')
            caseId = caseId || txtMessage?.innerHTML?.match(/New Case (.*) has been successfully created/)?.[1]
            console.log('txtMessage', txtMessage?.innerHTML, caseId);
            // 判断弹窗提示，如果是提示新建成功，则关闭弹窗，否则则确认
            if(/New Case (.*) has been successfully created/.test(txtMessage?.innerHTML)){
                await cancelDialog('cn')
            }else{
                await okDialog('cn');
            }
            await waitForPageReload(2000);
        }

        // 说明未新建成功
        if(!caseId){
            // 请求修改新建状态的接口
            await request('autoInput/updateStatus', 'post', {taskId: data.id, buildStatus: 3, message: txtMessage?.innerHTML})
        }else{
            // 请求修改新建状态的接口
            await request('autoInput/updateStatus', 'post', {taskId: data.id, buildStatus: 2, argusNum: caseId})
        }
    }finally {
        await waitForPageReload(5000);
        while (isDialog()) {                
            await closeDialog('cn');
        }
        // 确保在finally块中清理资源
        caseId = null;
        txtMessage = null;
        console.info('complete createReport');
    }
}

/**
 * 自动关闭报告
 * @param record
 * @returns {Promise<void>}
 */
async function closeReport(record) {
    console.info('closeReport createReport');

    console.info('closeReport createReport');
}

function request(url, method, data) {   
    return new Promise((resolve, reject) => {
        if(!isExtensionValid){
            reject('插件需要刷新');
        }
        // 如果是 GET 请求，将数据转换为查询参数
        if (method.toUpperCase() === 'GET') {
            const queryParams = new URLSearchParams(data).toString();
            url += `?${queryParams}`;
        }
        if(localStorage.getItem('tenantId'))
            chrome.runtime.sendMessage({
                action: 'makeHttpRequest',
                url: `${baseUrl}/base-server/${url}`,
                options: {method: method},
                headers: {
                    argusUsername: localStorage.getItem('argusUsername'), 
                    tenantId: localStorage.getItem('tenantId'),
                    accessAddress: window.location.origin,
                    userId: localStorage.getItem('userId'),
                },
                body: method.toUpperCase() === 'GET' ? null : JSON.stringify(data) // 仅在非 GET 请求时设置 body
            }, response => {
                if (!url.includes('autoInput/clientHeart')) {console.log(url, JSON.stringify(data), response.success);}
                if (response.data) {
                    resolve(response.data.data);
                } else {
                    reject(response.error);
                }
            });
    })
  
}

async function heartbeatRequest(taskId) {
    await request('autoInput/clientHeart', 'get', {clientId: localStorage.getItem('userId'), taskId: taskId })
}
// 录入报告报错处理
async function errorCase(type, taskId, error, isSingleInput){
    while (isDialog()){
        await closeDialog()
    }
    let txtMessage = '';
    if(isSingleInput !== 'isSingleInput'){
        triggerEvent(getDocument().getElementById('Header:imgClose'), 'click')
        await waitForPageReload(3000)
        
        while (isDialog()) {
            txtMessage = await waitDom(getDialog(), 'txtMessage')
            await cancelDialog()
            await waitForPageReload(2000)
            await waitForPageReload(2000)
        }
    }
    try {
        if(isSingleInput === 'suppAdmis'){
            // 修改补录的状态
            await request('autoInput/updateReplenishTaskStatus', 'post', {qcTaskId: taskId, 'replenishStatus': '补录失败', replenishLog: [error || txtMessage?.innerHTML]})
        }else {
            // 修改录入状态
            if(type.includes('en')) await request('autoInput/updateStatus', 'post', {taskId: taskId, 'inputStatusEn': 3, message: error || txtMessage?.innerHTML})
            if(type.includes('cn')) await request('autoInput/updateStatus', 'post', {taskId: taskId, 'inputStatusCn': 3, message: error || txtMessage?.innerHTML})
        }
    }catch(e){
        console.log('请求失败', e);
    }
    if(isSingleInput !== 'isSingleInput') await buttonNotSaved()
    while (isDialog()){
        await closeDialog()
    }
}
// 报告录完保存
async function saveCase(reportLocale, type, taskId, isSingleInput) {
    await closeDialog();
    const successMsg = 'Case has been saved successfully';
    const toolTipsMsg = 'Do you want to save this case'
    const studyDrugMsg = 'Study is not blinded and study drug has not been selected'
    let txtMessage = '';

    triggerEvent(getDocument().getElementById('Header:imgSave'), 'click')
    await waitForPageReload(2000)
    if(isDialog()){
        txtMessage = await waitDom(getDialog(), 'txtMessage') || 'error'
        await okDialog(reportLocale)
        await waitForPageReload(2000)
        if(!txtMessage || txtMessage?.innerHTML?.includes(toolTipsMsg)){
            triggerEvent(getDocument().getElementById('Header:imgClose'), 'click')
            await waitForPageReload(3000)
            
            while (isDialog()) {
                if(!txtMessage?.innerHTML?.includes(successMsg)) txtMessage = await waitDom(getDialog(), 'txtMessage') || txtMessage
                if(await waitDom(getDialog(), 'CaseRouting', 100) || txtMessage?.innerHTML?.includes(studyDrugMsg)){
                    await cancelDialog()
                    await waitForPageReload(2000)
                }else {
                    await okDialog(reportLocale);
                    await waitForPageReload(2000)
                }
                if(txtMessage?.innerHTML?.includes('验证错误') || txtMessage?.innerHTML?.includes('Error')){
                    await cancelDialog()
                }
                await waitForPageReload(2000)
            }
        }
    
        // 将报告编号传递给后端
        if(!(txtMessage?.innerHTML?.includes(successMsg) || txtMessage?.innerHTML?.includes(toolTipsMsg))){ 
            if(isSingleInput === 'suppAdmis'){
                // 修改补录的状态
                await request('autoInput/updateReplenishTaskStatus', 'post', {qcTaskId: taskId, 'replenishStatus': '补录失败', replenishLog: [txtMessage?.innerHTML]})
            }else{
                // 修改录入状态
                if(type.includes('en')) await request('autoInput/updateStatus', 'post', {taskId: taskId, 'inputStatusEn': 3, message: txtMessage?.innerHTML})
                if(type.includes('cn')) await request('autoInput/updateStatus', 'post', {taskId: taskId, 'inputStatusCn': 3, message: txtMessage?.innerHTML})
            }
            if(isSingleInput !== 'isSingleInput') await buttonNotSaved()
        }
    }else {
        // 修改录入状态
        await heartbeatRequest(taskId);
        if(isSingleInput === 'suppAdmis'){
            // 修改补录的状态
            await request('autoInput/updateReplenishTaskStatus', 'post', {qcTaskId: taskId, 'replenishStatus': '补录成功', replenishLog: []})
        }else{
            if(type.includes('en')) await request('autoInput/updateStatus', 'post', {taskId: taskId, 'inputStatusEn': 2})
            if(type.includes('cn')) await request('autoInput/updateStatus', 'post', {taskId: taskId, 'inputStatusCn': 2})
            await waitForPageReload(1000)
            const inputStatus = await request('autoInput/getTaskInfoById', 'post', {id: taskId})
            if(type.includes('en') && inputStatus){
                if(inputStatus?.inputStatusEn != '2') await request('autoInput/updateStatus', 'post', {taskId: taskId, 'inputStatusEn': 2})
            }
            if(type.includes('cn') && inputStatus){
                if(inputStatus?.inputStatusCn == '2') await request('autoInput/updateStatus', 'post', {taskId: taskId, 'inputStatusCn': 2})
            }
        }
        while(getDocument().getElementById('cfNav_lblPageTitle')){
            await buttonNotSaved()
        }
    } 

    while (isDialog()){
        await closeDialog()
    }
    await waitForPageReload(2000)
}

async function clickEventFun(id){
    triggerEvent(await waitDom(getDocument(), id), 'click')
    await waitForPageReload(2000)
}

async function cutReportTerminology(){
    await clickEventFun("TranslateDiv_imgNone_JP")
    await clickEventFun("//*[@id='td_CF_Tab_4']")
    await clickEventFun("//*[@id='td_CF_EVENT_1']")
    await clickEventFun("//*[@id='td_CF_EVT_1']")
}

function findAllWithLltname(array) {
    return array.reduce((acc, curr, index) => {
      if (curr['product_name_lltname']) {
        acc.push({
          index: index,
          object: curr
        });
      }
      return acc;
    }, []);
  }
// 单中文录取报告后，需要切换到英文补录药品的lltname编码
async function RewriteReportDrugCode(data) {
    await waitForPageReload(2000)
    await clickEventFun("TranslateDiv_imgNone_EN")
    await clickEventFun("//*[@id='td_CF_Tab_3']")
    await clickEventFun("td_CF_PRD_1")
    if(!data?.drugInfo) return
    const reportCountry = data['reportBaseInfo']['country_text']
    async function execRewriteDrug(index, record, type){
        await clickEventFun(`//*[@id='td_CF_PRD_${index}']`)
        // 如果包含whodrug编码字段，则需要进行whodrug编码
        if (record && record[type] === '1') {
            if(record['product_name_lltname']) await combineDrugCode(record['product_name_lltname'], reportCountry, record?.['product_name'])
            else {
                await assignDrugName(record['product_name'])
            }
        }
        while(isDialog()){
            await cancelDialog('en')
        }
        await waitForPageReload(1000)
    }
    // 试验用药数量
    const SuspectLen = data?.drugInfo?.['试验用药']?.length || 0
    const SuspectLLtNameList = findAllWithLltname(data?.drugInfo?.['试验用药'])
    if(SuspectLLtNameList.length){
        for(let i = 0; i< SuspectLLtNameList.length; i++){
            const record = SuspectLLtNameList[i]
            await execRewriteDrug(record['index'] + 1, record['object'], 'drug_type_0')
        }
    }
    // 合并用药
    const ConcomitantList = data?.drugInfo?.['合并用药']
    if(ConcomitantList?.length){
        for(let i = 0; i< ConcomitantList.length; i++){
            const record = ConcomitantList[i]
            await execRewriteDrug(i + SuspectLen + 1, record, 'drug_type_1')
        }
    }
    // 治疗用药
}
// 实验室检查避免中英文分类不统一，则将中英文数据都加上进行分类
function getOriginLabtestSet(data){
    const enData = data?.reportDataEn?.reportSubjectInfo?.refExamineTable
    const cnData = data?.reportDataCn?.reportSubjectInfo?.refExamineTable
    return Array.from(
        new Set((enData || cnData)?.map((item, index) => ({
            labtestreptd: item?.labtestreptd,
            labtest: item?.labtest,
            labtestlow: item?.labtestlow,
            labtesthigh: item?.labtesthigh,
            TXT_labunit_0: item?.TXT_labunit_0,
            labtestreptdEn: enData?.[index]?.labtestreptd,
            labtestEn: enData?.[index]?.labtest,
            labtestlowEn: enData?.[index]?.labtestlow,
            labtesthighEn: enData?.[index]?.labtesthigh,
            TXT_labunit_0En: enData?.[index]?.TXT_labunit_0,
            labtestreptdCn: cnData?.[index]?.labtestreptd,
            labtestCn: cnData?.[index]?.labtest,
            labtestlowCn: cnData?.[index]?.labtestlow,
            labtesthighCn: cnData?.[index]?.labtesthigh,
            TXT_labunit_0Cn: cnData?.[index]?.TXT_labunit_0,
        })).map(item => JSON.stringify(item)))
    ).map(item => JSON.parse(item));
}

let heartbeatInterval = null;
// 设置心跳函数
const startHeartbeat = (taskId) => {
    console.log('=========心跳开始=============');
    let failureCount = 0;
    const maxFailures = 3; // 连续失败次数上限
    stopHeartbeat()// 确保开始新心跳前停止旧的心跳
    heartbeatInterval = setInterval(async () => {
        try {
            console.log('=========心跳=============');
            await heartbeatRequest(taskId);
            failureCount = 0;
        } catch (error) {
            console.log('心跳接口错误', error);
            failureCount++;
            if (failureCount >= maxFailures) {
                stopHeartbeat(); // 达到最大失败次数才停止心跳
            }
        }
    }, 10000); // 每5秒执行一次
};

const stopHeartbeat = () => {
    // 确保在循环结束或出现异常时清除心跳定时器
    if (heartbeatInterval) {
        clearInterval(heartbeatInterval);
        heartbeatInterval = null;
    }   
};
// 【自动录入、自动QC、自动补录】一份报告后需要刷新页面清除缓存，避免多份报告录入导致的内存泄漏
(async () => {
    console.log('重新加载页面');
    if(localStorage.getItem('refreshType')){        
        await wait(3000)
        triggerEvent(getDialog()?.getElementById('btnOkYes'), 'click')
        await waitForPageReload(5000)
        if(localStorage.getItem('refreshType') !== 'refreshType'){
            let refreshCount = 0;
            while(!document.getElementById(localStorage.getItem('refreshType'))){
                refreshCount++;
                await waitForPageReload(3000)
                if(refreshCount > 3) break;
            }
            triggerEvent(document.getElementById(localStorage.getItem('refreshType')), 'click')
        }
        if(!document.getElementById(localStorage.getItem('refreshType'))) console.log('编号ID为' + localStorage.getItem('refreshType') + '元素未找到')
        localStorage.removeItem('refreshType')
    }
})()
// 清除缓存
async function clearMemory(type, forceRefresh = false) {
    // 获取iframe元素
    let iframe = document.getElementById(mainAppId)?.contentDocument?.getElementsByName('fm_TopFrame')?.[0];

    async function injectFunction() {
        const script = iframe.contentDocument.createElement('script');
        script.src = chrome.runtime.getURL('../js/inject.js');
        (iframe.contentDocument.head || iframe.contentDocument.documentElement).appendChild(script);
        script.remove();
    }
   
    await injectFunction();
    // [自动录入、自动QC、自动补录]需要刷新页面
    if(forceRefresh){
        console.log('发起刷新页面任务');
        await wait(1000);
        iframe?.contentWindow?.postMessage({ type: 'refreshCache' }, '*');
        await waitForPageReload(2000)
        localStorage.setItem('refreshType', type || 'refreshType')
        await waitForPageReload(2000)
        window.location.reload();
    }
}
// 自动创建
async function automaticallyCreate() {
    if(isExecing) {
        console.log('正在进行中......')
    }
    // 创建增加loading
    loadingReady('show', 'create', loadingStatusColor.get('ongoing'))
    
    let taskId = '';
    isExecing = true;
    let data = null;
    console.log('开始=============');
    while (true) {
        try{
            data = await request('autoInput/getNextReport', 'get', { type: 'build', clientId: localStorage.getItem('userId') });
            if (!data) break;
            taskId = data.id
            // 设置一个间隔执行的心跳函数
            startHeartbeat(taskId);

            const searchRes = await searchReport(data);
            if (searchRes === 'notExist') {
                // 请求接口进行新建该份报告
                await createReport(data);
            }else {
                console.log('暂无需要新建的报告');
            }
        }catch(e){
            console.log(e);
            while(isDialog()){
                await closeDialog();
            }
            await request('autoInput/updateStatus', 'post', {taskId: taskId, buildStatus: 3, message: e instanceof ReferenceError ? e.message : e})
        }finally{
            data = null;
            triggerEvent(getDocumentByName('fm_TopFrame')?.getElementById('Header1_imbHome'), 'click')
            await waitForPageReload(2000)
            stopHeartbeat();
        }           
    }
    console.log('结束创建');
    isExecing = false
    // 创建完成之后，完整清除一次
    await clearMemory(undefined, true);
    // 创建结束loading
    loadingFinish('hide', 'create', loadingStatusColor.get('finish'))
}
// 自动录入
async function automaticallyEntry() {
    if(isExecing) {
        console.log('正在进行中......')
    }
    let taskId = '';
    // 录入增加loading
    loadingReady('show', 'entry', loadingStatusColor.get('ongoing'))

    isExecing = true;
    let data = null;
    console.log('开始=============', heartbeatInterval);
    while(true){
        try{
            data = await request('autoInput/getNextReport', 'get', {type: 'input', clientId: localStorage.getItem('userId')})
            if(!data) break;
            taskId = data.id
            // 设置一个间隔执行的心跳函数
            startHeartbeat(taskId);
            const searchRes = await searchReport(data, 'click')
            if(searchRes === 'exist'){
                await waitForPageReload(3000)
                // 请求接口进行新建该份报告
                if(data.reportDataEn && data.inputStatusEn != 2) {
                    await argus(data, 'en', getOriginLabtestSet(data))
                }
                if(data.reportDataCn && data.inputStatusCn != 2) {
                    await argus(data, 'cn', getOriginLabtestSet(data))
                    if(!data.reportDataEn){ //单语中语-重写报告药物编码
                        await RewriteReportDrugCode(data.reportDataCn)
                    }
                }else {
                    await cutReportTerminology()
                    await fillSingleField('desc_reptd', data.reportDataEn?.reportSaeDetailInfo?.[0]?.desc_reptd)
                    await waitForPageReload(2000)
                }
                await saveCase('cn', `${(data.reportDataCn ? 'cn' : '') + (data.reportDataEn ? 'en' : '')}`, taskId)
                await waitForPageReload(5000)
            }else if(searchRes === 'viewing'){
                console.log('报告正在占用中。。。');// 请求修改录入状态的接口
                await request('autoInput/updateStatus', 'post', {taskId: taskId, 'inputStatusEn': 3, message: '报告正在占用中...'})
                await request('autoInput/updateStatus', 'post', {taskId: taskId, 'inputStatusCn': 3, message: '报告正在占用中...'})
            }else {
                console.log('该报告不存在');
            }
        }catch(e){
            console.log(e);
            // 请求修改录入状态的接口
            await errorCase(`${(data.reportDataCn ? 'cn' : '') + (data.reportDataEn ? 'en' : '')}`, data.id, e instanceof ReferenceError ? e.message : e)
        }finally{
            if(getDocument().getElementById('cfNav_lblPageTitle')){
                await buttonNotSaved()
            }
            while(isDialog()){
                await closeDialog()
            }
            stopHeartbeat()
            // 录入完成之后，完整清除一次
            if(data) await clearMemory('automaticallyEntry', true);
            await wait(10000)
            data = null;
        }
    }
    console.log('结束录入');
    // 录入结束loading
    loadingFinish('hide', 'entry', loadingStatusColor.get('finish'))
    isExecing = false
}

// 单份录入
async function singleEntry(){
    if(isExecing) {
        console.log('正在进行中');
    }
    isExecing = true;
    let data = null;
    let error = null;
    // 录入loading
    loadingReady('show', 'entry', loadingStatusColor.get('ongoing'))

    console.log('开始=============', heartbeatInterval);
    try{
        await cutReportTerminology()
        const desc_reptd = await waitDom(getDocument(), 'desc_reptd')
        if(!desc_reptd) return;
        // 单份报告录入    // todo: 单份报告录入
        data = await request('autoInput/getNextReport', 'get', {type: 'input', clientId: localStorage.getItem('userId'), reportId: desc_reptd?.value})
        await waitForPageReload(1000)
        if(data){
            taskId = data.id;
            // 设置一个间隔执行的心跳函数
            startHeartbeat(taskId) // 每5秒执行一次
            if(data.reportDataEn && data.inputStatusEn != 2) {
                await argus(data, 'en', getOriginLabtestSet(data))
                await waitForPageReload(3000)
            }
            if(data.reportDataCn && data.inputStatusCn != 2) {
                await argus(data, 'cn', getOriginLabtestSet(data))
                if(!data.reportDataEn){ //单语中语-重写报告药物编码
                    await RewriteReportDrugCode(data.reportDataCn)
                }
            }else {
                await cutReportTerminology()
                await fillSingleField('desc_reptd', data.reportDataEn?.reportSaeDetailInfo?.[0]?.desc_reptd)
                await waitForPageReload(1000)
            }
            await saveCase('cn', `${(data.reportDataCn ? 'cn' : '') + (data.reportDataEn ? 'en' : '')}`, taskId, 'isSingleInput')
            await waitForPageReload(3000)
        }
        console.log('结束=============');
        
        // await argus({reportDataEn: dataAAA }, 'en', getOriginLabtestSet(dataAAA?.reportSubjectInfo?.refExamineTable)) 
        // await argus({reportDataEn: dataEn, reportDataCn: dataCn}, 'cn', getOriginLabtestSet(dataEn?.reportSubjectInfo?.refExamineTable))
    }catch(e){
        console.error('执行过程中发生错误:', e);
        error = e instanceof ReferenceError ? e.message : e
        loadingFinish('error', 'entry', loadingStatusColor.get('error'), error)
        await errorCase(`${(data?.reportDataCn ? 'cn' : '') + (data?.reportDataEn ? 'en' : '')}`, data?.id, error, 'isSingleInput')
        // 请求修改录入状态的接口
    }finally{
        console.log('结束录入');
        stopHeartbeat()
        data = null;
        reportData = null;
        isExecing = false
        // 单份录入完成之后，完整清除一次
        await clearMemory();
        // 录入结束loading
        if(!error) loadingFinish('hide', 'entry', loadingStatusColor.get('finish'), error)
        error = null;
    }  
}
// 获取QC数据
function getQCData(){
    return new Promise((resolve) => {
        window.postMessage(
            { type: "reportQCDataExec" },
            window.location.origin
          );

        function handleMessage(event) {
            if (event.data.type === 'reportQCData') {
                window.removeEventListener('message', handleMessage);
                resolve(event.data.data);
            }
        }
        window.addEventListener('message', handleMessage);
    });
}
// 根据后端返回的数据，判断是否需要切换语言进行获取QC数据
async function qcFun(data) {
    return new Promise(async(resolve) => {
        let qcResultCn;
        let qcResultEn;
        await waitForPageReload(3000)
        // QC中文报告
        if(data.typeList.includes(1)){
            triggerEvent(await waitDom(getDocument(), "TranslateDiv_imgNone_JP"), 'click')
            await waitForPageReload(2000)
            qcResultCn = await getQCData();
            await waitForPageReload(3000)
        }
        // QC英文报告
        if(data.typeList.includes(2)){
            triggerEvent(await waitDom(getDocument(), "TranslateDiv_imgNone_EN"), 'click')
            await waitForPageReload(2000)   
            qcResultEn = await getQCData()
            await waitForPageReload(3000)
        }                
        await waitForPageReload(3000)
        resolve({
            qcResultCn: qcResultCn,
            qcResultEn: qcResultEn
        })
    })
    
}
// 自动QC
async function automaticallyQc() {
    if(isExecing) {
        console.log('正在进行中......')
    }
    let inputTaskId = '';
    let qcTaskId = '';
    let reportId = '';
    // 录入增加loading
    loadingReady('show', 'qc', loadingStatusColor.get('ongoing'))

    isExecing = true;
    let data = null;
    while(true){
        try{
            data = await request('autoInput/getQcTask', 'get')
            if(!data) break;
            inputTaskId = data.inputTaskId
            qcTaskId = data.qcTaskId
            reportId = data.reportId
            const searchRes = await searchReport(data, 'click', 'qc')
            if(searchRes === 'exist'){
                const res = await qcFun(data)
                await request('autoInput/saveQcResult', 'post', {
                    "qcTaskId": qcTaskId, // qc任务id
                    "inputTaskId": inputTaskId,// 录入任务id
                    "reportId": reportId,//报告id
                    "qcResultCn": res?.qcResultCn,//前端qc中文数据
                    "qcResultEn": res?.qcResultEn//前端qc英文数据
                })
            }else if(searchRes === 'viewing'){
                console.log('报告正在占用中。。。');// 请求修改录入状态的接口
            }else {
                console.log('该报告不存在');
            }
        }catch(error){
            // 请求修改录入状态的接口
            console.log(error);
            while (isDialog()){
                await closeDialog()
            }
        }finally{
            if(getDocument().getElementById('cfNav_lblPageTitle')){
                await buttonNotSaved()
            }
            while(isDialog()){
                await closeDialog()
            }
            // QC完成之后，完整清除一次
            if(data) await clearMemory('automaticallyQc', true);
            await wait(10000)
            data = null;
        }
    }
    console.log('结束QC');
    // 录入结束loading
    loadingFinish('hide', 'qc', loadingStatusColor.get('finish'))
    isExecing = false
}
// 单份QC
async function singleQc() {
    if(isExecing) {
        console.log('正在进行中');
    }
    let inputTaskId = '';
    let qcTaskId = '';
    let reportId = '';
    isExecing = true;
    let data = null;
    let error = null;
    // 录入loading
    loadingReady('show', 'qc', loadingStatusColor.get('ongoing'))

    try{
        const projectNum = localStorage.getItem('projectNum')
        if(!projectNum) return;
        await waitForPageReload(1000)
        data = await request('autoInput/getQcTask', 'get', {argusNum: projectNum})
        if(data){
            inputTaskId = data.inputTaskId
            qcTaskId = data.qcTaskId
            reportId = data.reportId
            const res = await qcFun(data)
            await request('autoInput/saveQcResult', 'post', {
                "qcTaskId": qcTaskId, // qc任务id
                "inputTaskId": inputTaskId,// 录入任务id
                "reportId": reportId,//报告id
                "qcResultCn": res?.qcResultCn,//前端qc中文数据
                "qcResultEn": res?.qcResultEn//前端qc英文数据
            })
        }else {
            console.log('该报告不存在');
        }
        
    }catch(error){
        // 请求修改录入状态的接口
        console.log(error);
        error = e instanceof ReferenceError ? e.message : e
        loadingFinish('error', 'qc', loadingStatusColor.get('error'), error)
        while (isDialog()){
            await closeDialog()
        }
    }finally{
        data = null;
        inputTaskId = null;
        qcTaskId = null;
        reportId = null;
        if(getDocument().getElementById('cfNav_lblPageTitle')){
            await buttonNotSaved()
        }
        while(isDialog()){
            await closeDialog()
        }
        // 录入完成之后，完整清除一次
        await clearMemory();
        if(!error) loadingFinish('hide', 'qc', loadingStatusColor.get('finish'), error)
        error = null;
    }
}

// 定义一个递归函数来检查键是否存在且值为数组
function hasArrayKey(obj, key) {
    if(Array.isArray(obj)) return false
    if (obj && typeof obj === 'object') {
        if (Array.isArray(obj[key]) && obj[key]?.length) {
            return true;
        }
        // 遍历对象的所有键
        for (let k in obj) {
            if (obj.hasOwnProperty(k) && typeof obj[k] === 'object') {
                // 递归检查子对象
                if (hasArrayKey(obj[k], key)) {
                    return true;
                }
            }
        }
    }
    return false;
}
// 补录前删除录入出错的多记录数据
async function deleteSuppAdmisData(suppAdmisData) {
    if(!suppAdmisData) return;
    const suppAdmisLocale = suppAdmisData?.['reportBaseInfo']?.['reportLanguage'] === 'cn' ? 'cn' : 'en';
    if(suppAdmisLocale === 'en'){
        triggerEvent(await waitDom(getDocument(), "TranslateDiv_imgNone_EN"), 'click')
    }else {
        triggerEvent(await waitDom(getDocument(), "TranslateDiv_imgNone_JP"), 'click')
    }
    await waitForPageReload(2000)
    return new Promise(async (resolve) => {
        const moduleKeys = SuppAdmisModuleKeys.filter(mod => {
            return hasArrayKey(suppAdmisData, mod)
        })        
        await waitForPageReload(3000)
        if(!isExtensionValid){
            console.log('插件更新，请刷新页面');
            return;
        }
        window.postMessage(
            { type: "supplementaryInputModuleData", data: moduleKeys },
            window.location.origin
          );
        function handleMessage(event) {
            if (event.data.type === 'supplementaryInputData') {
                window.removeEventListener('message', handleMessage);
                resolve(true);
            }
        }
        window.addEventListener('message', handleMessage);
    });
}
// 根据后端返回的数据，判断是否需要切换语言进行删除需要补录的数据
async function SuppAdmis(data) {
    const suppAdmisData = { id: data.qcTaskId }
    // 删除要补录的多记录数据
    await deleteSuppAdmisData(data?.enReplenishData || data.cnReplenishData)
    await waitForPageReload(2000)
    if(data.enReplenishData) {
        suppAdmisData.reportDataEn = data.enReplenishData
        if(data.cnReplenishData) suppAdmisData.reportDataCn = data.cnReplenishData
        await argus(suppAdmisData, 'en', getOriginLabtestSet({...data, reportDataEn: data?.enReplenishData, reportDataCn: data?.cnReplenishData}), 'suppAdmis')
        await waitForPageReload(3000)
    }
    if(data.cnReplenishData) {
        suppAdmisData.reportDataCn = data.cnReplenishData
        await argus(suppAdmisData, 'cn', getOriginLabtestSet({...data, reportDataEn: data?.enReplenishData, reportDataCn: data?.cnReplenishData }), 'suppAdmis')
        if(!data.enReplenishData){ //单语中语-重写报告药物编码
            await RewriteReportDrugCode(data.cnReplenishData)
        }
    }else if(data.enReplenishData?.reportSaeDetailInfo){
        await cutReportTerminology()
        await fillSingleField('desc_reptd', data.enReplenishData?.reportSaeDetailInfo?.[0]?.desc_reptd)
        await waitForPageReload(1000)
    }
}

// 单份补录
async function singleSuppAdmis() {
    if(isExecing) {
        console.log('正在进行中');
    }
    isExecing = true;
    let data = null;
    let error = null;
    // 录入loading
    loadingReady('show', 'suppAdmis', loadingStatusColor.get('ongoing'))

    try{        
        const projectNum = localStorage.getItem("projectNum")
        if(!projectNum) return;
        console.log('argusNum', projectNum);
        
        await waitForPageReload(1000)
        // 单份补录
        data = await request('autoInput/getReplenishTask', 'get', {argusNum: projectNum, timestamp: new Date().getTime()})
        await waitForPageReload(1000)
        if(data){
            taskId = data.qcTaskId;
            await SuppAdmis(data)
            await waitForPageReload(2000)
            await saveCase('cn', `${(data.cnReplenishData ? 'cn' : '') + (data.enReplenishData ? 'en' : '')}`, taskId, 'suppAdmis')
            await waitForPageReload(3000)
        }
        console.log('结束=============');
    }catch(e){
        console.error('执行过程中发生错误:', e);
        error = e instanceof ReferenceError ? e.message : e
        loadingFinish('error', 'suppAdmis', loadingStatusColor.get('error'), error)
        await errorCase(`${(data?.cnReplenishData ? 'cn' : '') + (data?.enReplenishData ? 'en' : '')}`, data?.qcTaskId, error, 'suppAdmis')
        // 请求修改录入状态的接口
    }finally{
        console.log('结束录入');
        data = null;
        reportData = null;
        isExecing = false
        // 单份录入完成之后，完整清除一次
        await clearMemory();
        // 录入结束loading
        if(!error) loadingFinish('hide', 'suppAdmis', loadingStatusColor.get('finish'), error)
        error = null;
    }  
}

// 自动补录
async function automaticallySuppAdmis() {
    if(isExecing) {
        console.log('正在进行中');
    }
    isExecing = true;
    let data = null;
    // 录入loading
    loadingReady('show', 'suppAdmis', loadingStatusColor.get('ongoing'))

    while(true){
        try{
            console.log('补录开始');
            // 报告补录
            data = await request('autoInput/getReplenishTask', 'get', {timestamp: new Date().getTime()})
            if(!data) break;
            const searchRes = await searchReport({argusNum: data.argusNum, id: data.qcTaskId}, 'click', 'qc')
            await waitForPageReload(1000)
            if(searchRes === 'exist'){
                taskId = data.qcTaskId;
                await SuppAdmis(data)
                await waitForPageReload(2000)
                await saveCase('cn', `${(data.cnReplenishData ? 'cn' : '') + (data.enReplenishData ? 'en' : '')}`, taskId, 'suppAdmis')
                await waitForPageReload(3000)
            }else if(searchRes === 'viewing'){
                console.log('报告正在占用中。。。');// 请求修改录入状态的接口
            }else {
                console.log('该报告不存在');
            }
            console.log('结束=============');
        }catch(e){
            console.error('执行过程中发生错误:', e);
            await errorCase(`${(data?.cnReplenishData ? 'cn' : '') + (data?.enReplenishData ? 'en' : '')}`, data?.qcTaskId, e instanceof ReferenceError ? e.message : e, 'suppAdmis')
            // 请求修改录入状态的接口
        }finally{
            console.log('补录一份结束');
            if(getDocument().getElementById('cfNav_lblPageTitle')){
                await buttonNotSaved()
            }
            while (isDialog()){
                await closeDialog()
            }
            // 自动补录完成之后，完整清除一次
            if(data) await clearMemory('automaticallySuppAdmis', true);
            await wait(10000)
            data = null;
        }  
    }
    isExecing = false
    // 录入结束loading
    loadingFinish('hide', 'suppAdmis', loadingStatusColor.get('finish'))
}
// 录入开始loading的状态
function loadingReady(data, type, background){
    chrome.runtime.sendMessage({ action: 'loadingTransform', data: {
        data: data, 
        type: type, 
        background: background
    } });
    document.getElementById('loading').style.display = 'block';
    document.getElementById('loading').style.background = background;
}
// 录入结束loading的状态
chrome.runtime.onMessage.addListener(function (request) {
    if (request.action === "loadingClose") {
        document.getElementById('loading').style.display = 'none';
    }
});
// 结束关闭loading
function loadingFinish(data, type, background, error){
    document.getElementById('loading').addEventListener('click', () => {
        document.getElementById('loading').style.display = 'none';
    })
    document.getElementById('loading').style.background = background;
    chrome.runtime.sendMessage({ action: 'loadingTransform', data: {data: data, type: type, background: background, error: error} });
}
// 随访数据
async function followUpInput() {
    if(getDocument().getElementById('Rel_Hist_Table_followup_button')) return
    triggerEvent(await waitDom(getDocument(), 'td_CF_Tab_2'), 'click');
    await waitForPageReload(1000);
    // 选择要在其后面添加按钮的元素
    let element = await waitDom(getDocument(), 'number_rows', 10);
    if(!element){
        triggerEvent(await waitDom(getDocument(), 'td_CF_PATIENT_1'), 'click');
        await waitForPageReload(1000);
    }
        
    element = await waitDom(getDocument(), 'number_rows', 10)

    async function followUpFun() {
        if(getDocument().getElementById('Rel_Hist_Table_FOLLOWUP_dataTable')) return
        followUpCompareContent(await waitDom(getDocument(),'Pat_RelHist_Content_Div'))
    }
    // 创建一个新的按钮元素
    const button = document.createElement('span');
    button.id = 'Rel_Hist_Table_followup_button';
    // 设置按钮的基本样式
    Object.assign(button.style, {
        width: '80px',
        color: 'white',
        border: '2px solid orange',
        display: 'inline-block',
        marginLeft: '10px',
        cursor: 'pointer',
        textAlign: 'center',
        verticalAlign: 'middle', // 设置垂直对齐方式，保证与其他内容对齐
        position: 'relative',     // 使伪元素相对于按钮定位
        overflow: 'hidden',
        transition: 'transform 0.3s ease' // 添加平滑的缩放过渡
    });
    button.textContent = '随访录入';
    // 创建一个伪动画元素，用于光线滑动效果
    const animationElement = document.createElement('div');
     Object.assign(animationElement.style, {
        position: 'absolute',
        top: '0',
        left: '-150%',// 初始位置稍远一些以便更好的展示光线效果
        width: '300%',// 加大渐变的宽度，让效果更明显
        height: '100%',
        background: 'linear-gradient(120deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.6) 50%, rgba(255, 255, 255, 0) 100%)',// 增加透明度，使效果更亮
        transition: 'left 0.8s ease'// 缩短动画时间，使光线滑过更快速
      });
    // 将动画元素添加到按钮中
    button.appendChild(animationElement);
    // 添加鼠标事件
    button.addEventListener('mouseenter', () => {
        // 当鼠标移入时触发动画，并略微放大按钮
        animationElement.style.left = '100%';
        button.style.transform = 'scale(1.05)'; // 略微放大
    });
    button.addEventListener('mouseleave', () => {
        // 当鼠标移出时将动画元素重置，并恢复按钮大小
        animationElement.style.left = '-150%';
        button.style.transform = 'scale(1)'; // 恢复原始大小
    });
    // 将按钮添加到文档的某个位置（例如某个容器元素）
    appendToBodySafely(button);
    // 添加点击事件处理程序
    // 调用随访功能
    button.addEventListener('click', followUpFun);
    // 在选定元素后面插入按钮
    element.parentNode.insertBefore(button, element.nextSibling);

}
// 获取患者随访数据的行
function getPatRelHistContentRows(){
    const Pat_RelHist_Content_Div = getDocument().getElementById('Pat_RelHist_Content_Div')
    const elements = Pat_RelHist_Content_Div?.getElementsByTagName('*')
    const rowElements = []
    // 遍历所有子元素
    for (let j = 0; j < elements.length; j++) {
        const element = elements[j];
        // 如果元素的id包含Event_Row_，添加到数组中
        if (element.id && /^(?:Rel_Hist_Table|CPH)_\d$/.test(element.id) && element.style.display !== 'none') {
            rowElements.push(element)
        }
    }
    return rowElements
}
// 删除原有随访数据
async function batchDelete(deleteBtnId){
    const rowElements = getPatRelHistContentRows()    
    triggerEvent(rowElements?.[0], 'click');
    await waitForPageReload(200)
    const deleteBtn = getDocument().getElementById(deleteBtnId)
    let deleteNums = rowElements.length - 2
    while(deleteNums > 0){
        triggerEvent(deleteBtn, 'click');
        await waitForPageReload(300)
        if(getDialog()) triggerEvent(getDialog()?.getElementById('btnOkYes'), 'click');
        await waitForPageReload(300)
        deleteNums--
    }
}
// 删除随访数据新增后，随访数据角标和数据的角标不一致，需要增加上删除的随访数据的数量形成新的随访角标
function updateKeysWithOffset(arr, offset) {
    return arr.map(obj => {
        const updatedObj = {};
        for (let key in obj) {
            if (obj.hasOwnProperty(key)) {
                // 找到数字部分
                const newKey = key.replace(/(\d+)/, (match) => parseInt(match) + offset);
                updatedObj[newKey] = obj[key];
            }
        }
        return updatedObj;
    });
}

// 处理数据，将字段名中的索引去除，并保留映射关系
function processData(dataArray) {
    return dataArray.filter(item => {
        for (let key in item) {
            if(key.includes('pat_hist_rptd_lltname')){
                if(item[key] === '未知编码(未知名称)') return false
            }
        }
        return item;
    });
}
// 展示随访对比结果数据
function followUpCompareContent(parentDiv) {
    if(parentDiv){
        // 创建一个新的 div 元素来包含要插入的内容
        const newContent = getDocument().createElement('iframe');
        newContent.id = 'Rel_Hist_Table_FOLLOWUP_dataTable'
        newContent.style.width = '100%';
        newContent.style.height = '300px';
        // 在目标元素后面插入新内容
        parentDiv.parentNode.insertBefore(newContent, parentDiv.nextSibling);
        newContent.src = chrome.runtime.getURL('../html/followup.html');

        const script = getDocument().createElement('script');
        script.src = chrome.runtime.getURL('../js/followUpOperationMedicalHistory.js');
        (document.head || document.documentElement).appendChild(script);
        // 获取病史列表数据
        let sourceData = [];
        window.addEventListener('message', function(event) {        
            // 处理接收到的数据
            console.log('病史原有数据列表:', event.data);
            const receivedData = event.data;
            if(receivedData.type === 'followUpOperationMedicalHistory'){
                newContent.onload = function() {
                    sourceData = processData(receivedData?.data); 
                    // 传递给合并表格的原始数据
                    newContent.contentWindow.postMessage({
                        type: 'MedicalHistoryList',
                        data: {oldData: sourceData, newData: followUpData?.["reportSubjectInfo"]?.["rel_hist_add"]}
                    }, '*');
                };
            }
            
            chrome.runtime.onMessage.addListener(async (message, sender, sendResponse) => {
                if (message.action === 'MergeCompleted') {
                    console.log('MergeCompleted -> ', message.data, sourceData);                    
                    // 将对比展示的表格取消
                    const dataTable = getDocument().getElementById('Rel_Hist_Table_FOLLOWUP_dataTable');
                    if(dataTable) dataTable.parentNode.removeChild(dataTable);
                    // 删除原有数据
                    await batchDelete('rel_hist_del');
                    if(message?.data?.length){
                        console.log('----开始录入随访数据----');                           
                        const rowElements = getPatRelHistContentRows()       
                        const oldLen = rowElements?.[0]?.id?.match(/^(?:Rel_Hist_Table|CPH)_(\d)$/)?.[1]              
                        console.log('原始下标：', oldLen, rowElements?.[0]);                        
                        // 新增合并后的病史数据
                        await processNode({}, {
                            "events": [{path: "rel_hist_add",
                                event: 'click',
                                waitTime: 2000,
                                loop: true,
                                records: updateKeysWithOffset(message?.data, +oldLen)
                            }]
                        })
                    }
                }
            });
        });
    }
}
// 观察页面元素是否包含设置的字段，判断是否需要监听是否开始随访
async function observeElementAttribute(elementId, attributeName, callback) {    
    let targetNode = getDocument().getElementById(elementId)
    if (!targetNode) {
        console.log('未找到指定的元素');
        return;
    }    
    while(targetNode?.getAttribute('data-unique-id')){
        await wait(3000)
        targetNode = getDocument().getElementById(elementId)
    }
    targetNode?.setAttribute('data-unique-id', 'unique');
    // 观察器的配置（即要观察什么变动）
    const config = { attributes: true, attributeFilter: [attributeName] };
    // 当观察到变动时执行的回调函数
    const mutationCallback = async function(mutationsList, observer) {
        for(let mutation of mutationsList) {
            if (mutation.type === 'attributes') {
                const newValue = targetNode.getAttribute(attributeName);
                callback(newValue);
                await waitForPageReload(1000)
                // 元素变动后，相当于切换tab,当前项目切换tab后页面元素全部更新，故重新监听新页面的tab
                await observeElementAttribute(elementId, attributeName, callback)
            }
        }
    };
    // 创建一个观察器实例并传入回调函数
    const observer = new MutationObserver(mutationCallback);
    // 开始观察目标节点
    observer.observe(targetNode, config);
    // 返回观察器实例，以便之后可以停止观察
    return observer;
}
// 请求接口需要的租户等信息
const tenantInfoName = ['tenantName', 'tenantId', 'tenantEnv', 'studyNum', 'argusUsername', 'userId', 'accessAddress']
// 迁移配置
async function migrationConfiguration() {
    if(!isExtensionValid){
        console.log('插件更新，请刷新页面');
        return;
    }
    const search = appendLocalStorageToURL(tenantInfoName);    
    chrome.runtime.sendMessage({
        action: 'migrationConfiguration',
        search: search,
        baseUrl: baseUrl,
    })
}
// Aose
function AosePinchWriting() {
    if(!isExtensionValid){
        console.log('插件更新，请刷新页面');
        return;
    }
    const search = appendLocalStorageToURL(tenantInfoName);
    chrome.runtime.sendMessage({
        action: 'AosePinchWriting',
        search: search
    })
}
// 展示历史数据
async function ActionItemMainTableHistoryContent() {
     // 创建一个容器 div 来包装 iframe
     const container = document.createElement('div');
     container.id = 'ActionItemMainTable_history_container';
     Object.assign(container.style, {
        width: '100%',
        height: '0',
        transition: 'height 0.3s ease-out',
        backgroundColor: '#fff',
        boxShadow: '0 -2px 10px rgba(0, 0, 0, 0.1)'
      }); 
     // 创建 iframe
     const iframe = document.createElement('iframe');
     iframe.id = 'ActionItemMainTable_history_table';
     Object.assign(iframe.style, {
        width: '100%', 
        height: '100%',
        border: 'none'
     }); 
     // 将 iframe 添加到容器中
     container.appendChild(iframe);
     iframe.src = chrome.runtime.getURL('../html/question.html'); 
     // 将容器添加到 body
     document.getElementById('fm_MainApp').contentDocument.body.appendChild(container);
    // 更改主体内容高度
     const mainFrame = document.getElementById(mainAppId).contentDocument.getElementById(mainFrameId)
     mainFrame.style.height = 'calc(100vh - 430px)';

     // 添加脚本
     const script = document.createElement('script');
     script.src = chrome.runtime.getURL('../js/question.js');
     (document.head || document.documentElement).appendChild(script);
    let timeoutId = null;
     // 使用 setTimeout 来确保 DOM 更新后再显示容器
     timeoutId = setTimeout(() => {
         container.style.height = '378px';
     }, 10);
    
    const element = getDocument()?.getElementById('cfNav_lblPageTitle');
    const argusNum = element?.textContent?.match(/ - (.+) .+?/)?.[1];
    const questionData = await request('autoInput/getConcerns', 'get', {argusNum: argusNum});
    console.log('questionData', questionData);
    if(!isExtensionValid){
        console.log('插件更新，请刷新页面');
        return;
    }
    chrome.runtime.sendMessage({
        action: 'questionHistoryData',
        data: questionData
    })
     
    chrome.runtime.onMessage.addListener(
        function(message, sender, sendResponse) {            
            if (message.action === "questionHistory"){
                const container = document.getElementById('fm_MainApp')?.contentDocument?.getElementById('ActionItemMainTable_history_container')
                container.style.height = '0';
                if(timeoutId) clearTimeout(timeoutId)
                timeoutId = setTimeout(() => {
                    container.remove();
                    mainFrame.style.height = 'calc(100vh - 52px)';
                }, 300);
            }
        }
    );
}
// 历史数据
async function historyData() {
    if(getDocument().getElementById('ActionItemMainTable_history_btn')) return
    triggerEvent(await waitDom(getDocument(), 'td_CF_Tab_6'), 'click');
    await waitForPageReload(1000);
    // 选择要在其后面添加按钮的元素
    let element = await waitDom(getDocument(), 'Span3', 10);

    async function ActionItemMainTableHistoryFun() {
        if(document.getElementById('fm_MainApp')?.contentDocument?.getElementById('ActionItemMainTable_history_table')) return
        ActionItemMainTableHistoryContent()
    }

    // 创建一个新的按钮元素
    const button = document.createElement('span');
    button.id = 'ActionItemMainTable_history_btn';

    // 设置按钮的基本样式
    Object.assign(button.style, {
        width: '80px',
        color: 'white',
        border: '2px solid orange',
        display: 'inline-block',
        marginLeft: '10px',
        cursor: 'pointer',
        textAlign: 'center',
        verticalAlign: 'middle',  // 设置垂直对齐方式，保证与其他内容对齐
        position: 'relative',      // 使伪元素相对于按钮定位
        overflow: 'hidden',
        transition: 'transform 0.3s ease' // 添加平滑的缩放过渡
      });
    button.textContent = '质疑';

    // 创建一个伪动画元素，用于光线滑动效果
    const animationElement = document.createElement('div');
    Object.assign(animationElement.style, {
        position: 'absolute',
        top: '0',
        left: '-150%',// 初始位置稍远一些以便更好的展示光线效果
        width: '300%',// 加大渐变的宽度，让效果更明显
        height: '100%',
        background: 'linear-gradient(120deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.6) 50%, rgba(255, 255, 255, 0) 100%)',// 增加透明度，使效果更亮
        transition: 'left 0.8s ease'// 缩短动画时间，使光线滑过更快速
    });
    // 将动画元素添加到按钮中
    button.appendChild(animationElement);
    // 添加鼠标事件
    button.addEventListener('mouseenter', () => {
        // 当鼠标移入时触发动画，并略微放大按钮
        animationElement.style.left = '100%';
        button.style.transform = 'scale(1.05)'; // 略微放大
    });
    button.addEventListener('mouseleave', () => {
        // 当鼠标移出时将动画元素重置，并恢复按钮大小
        animationElement.style.left = '-150%';
        button.style.transform = 'scale(1)'; // 恢复原始大小
    });
    // 将按钮添加到文档的某个位置（例如某个容器元素）
    appendToBodySafely(button);
    // 添加点击事件处理程序
    // 调用随访功能
    button.addEventListener('click', ActionItemMainTableHistoryFun);
    // 在选定元素后面插入按钮
    element.parentNode.insertBefore(button, element.nextSibling);
}
// 报告识别工具
const openRecognitionButton = () => { 
    if(!isExtensionValid){
        console.log('插件更新，请刷新页面');
        return;
    }
    const search = appendLocalStorageToURL(tenantInfoName);
    chrome.runtime.sendMessage({
        action: 'openRecognition',
        search: search
    })
};
// 住蹙RPA客户端
function registerRpaButton() {
    if(statusRecogRegis.registerRpaButton) return;
    statusRecogRegis.registerRpaButton = 1;

    console.info('Tab页通信建立完成')

    chrome.runtime.onMessage.addListener(async (message, sender, sendResponse) => {
        if (message.action === 'strucutrueData') {
            console.log('strucutrueData -> ', message.data);
            let error = null;
            // 开始录入RPA
            loadingReady('show', 'entry', loadingStatusColor.get('ongoing'))
            let taskId;
            let locale;
            try{
                const followupData = message.data;
                if(followupData?.['drugInfo']?.['合并用药'])
                    followupData['drugInfo']['合并用药'] = followupData?.['drugInfo']?.['合并用药']?.map(item => {
                        return {
                            ...item,
                            coverProductName: true
                        }
                    })
                const projectNum = localStorage.getItem("projectNum") || ''
                taskId = await request('/autoInput/createCopilotInputTask', 'post', {reportMd5: message.md5, reportData: followupData, argusNum: projectNum})
                console.log(message, taskId, 'taskId');
                if(!taskId) throw '任务编号不存在'
                locale = followupData?.['reportBaseInfo']?.['reportLanguage']
                // 开始录入RPA
                loadingReady('show', 'entry', loadingStatusColor.get('ongoing'))
                await argus(
                    {[locale === 'en' ? "reportDataEn" : "reportDataCn"]: followupData, id: taskId}, 
                    locale, 
                    getOriginLabtestSet({[locale === 'en' ? "reportDataEn" : "reportDataCn"] :followupData})
                )
                await request('autoInput/updateStatus', 'post', {taskId: taskId, [locale === 'cn' ? 'inputStatusCn' : 'inputStatusEn']: 2})
            } catch (e){
                console.error(e);
                error = e instanceof ReferenceError ? e.message : e
                await request('autoInput/updateStatus', 'post', {taskId: taskId, [locale === 'cn' ? 'inputStatusCn' : 'inputStatusEn']: 3, message: error})
                loadingFinish('error', 'entry', loadingStatusColor.get('error'), error)
            } finally{
                if(!error) loadingFinish('hide', 'entry', loadingStatusColor.get('finish'), error)
                error = null;
            }
        }

        if (message.action === 'followUpstrucutrueData') {
            followUpData = message.data
            followUpInput()
            await observeElementAttribute('table_nav_CF_Tab', 'on_section', (newValue, oldValue) => {
                console.log(`属性值为 ${newValue}`);
                // 在这里添加您想要执行的代码
                if(newValue === '2'){
                    followUpInput()
                }
            });
        }        
    });
}
// 判断新开的页面是否在浏览器存在，避免出现多个页面
function shouldExecuteFeature(data, url) {
    return !data.find(item => item.includes(url));
}   
// 菜单的点击事件，需要根据判断是否有租户和项目编号
function handleFeature(feature, data) {
    const event = featureEventMap.get(feature.name)
    if (SCHEME_FEATURES.includes(feature.name) && localStorage.getItem(('projectNum'))) {
        if (feature.name === 'openRecognitionButton') {
            if (shouldExecuteFeature(data, featureUrlMap[feature.name]) && !statusRecogRegis.openRecognitionButton) {
                event && event();
            }
        }else {
            event && event();
        }
    } else if (!SCHEME_FEATURES.includes(feature.name)) {
        if (localStorage.getItem('tenantId')) {
            if (featureUrlMap[feature.name]) {
                if (shouldExecuteFeature(data, featureUrlMap[feature.name])) {
                    event && event();
                }
            } else {
                event && event();
            }
        }
    }
}
// 获取所有菜单
async function getFeatures(){
    try{
        features = await request('api/menus/byTenantId', 'get')
        console.log('权限按钮', JSON.stringify(features));
    }catch(e){
        console.error(e)
    }
        
    (features || []).forEach(feature => {
        const button = document.createElement('button');
        button.innerText = feature.text;
        button.id = feature.name;
        Object.assign(button.style, {
            padding: '10px',
            border: '1px solid #ccc',
            borderRadius: '5px',
            backgroundColor: '#F0F0F0',
            display: 'flex',
            color: '#00000040',
            justifyContent: 'space-between',
            cursor: 'not-allowed'
        });
        const statusIcon = document.createElement('span');
        statusIcon.id = feature.name + 'Status';
        statusIcon.innerHTML = '&#x25CB;'; // 默认状态图标 (圆圈)
        statusIcon.style.fontSize = '16px';
        statusIcon.style.marginLeft = '10px';
    
        button.appendChild(statusIcon);
    
        let isActive = false; 
        button.addEventListener('click', () => {
            if(!localStorage.getItem('tenantId')) return;
            if(!localStorage.getItem('projectNum') && SCHEME_FEATURES.includes(feature.name)) return
            isActive = !isActive;
            if (isActive) {
                statusIcon.innerHTML = '●'; // 激活状态图标 (实心圆) 
            } else { 
                statusIcon.innerHTML = '○'; // 关闭状态图标 (空心圆) 
            }
            if(['openRecognitionButton', 'migrationConfiguration', 'AosePinchWriting'].includes(feature.name)){
                try{
                    if(isExtensionValid){                        
                        chrome.runtime.sendMessage({ action: 'getManagementList', data:feature }); 
                    }
                }catch(e){
                    console.log(e)
                }
                
            }else{
                featureEventMap.get(feature.name)?.()
            }
        });
        
        featureList.appendChild(button);
    });
}
// 获取最新版本号
async function getLastVersion(){
    try{
        const latestVersionData = await request('plugin/checkUpdate', 'get', {currentVersion: chrome.runtime.getManifest().version})
        document.getElementById('latestVersion').textContent = latestVersionData.latestVersion
        // if(!features) await getFeatures() 
        if(!latestVersionData?.isMandatory){
            if(!features) await getFeatures() 
        }else {
            const style = document.createElement('style');
            style.textContent = `
                #downloadButton::after {
                    content: '';
                    position: absolute;
                    top: 0;
                    right: 0;
                    width: 8px;
                    height: 8px;
                    background-color: red;
                    border-radius: 50%;
                }
            `;
            document.head.appendChild(style);
        }
    }catch(e){
        await getLastVersion()
        console.error(e)
    }
}

let timeoutEnvTenantId = null;
// 获取【获取租户的脚本】
function requestEnvTenant() {   
    return new Promise((resolve, reject) => {
        if(!isExtensionValid){
            reject('插件需要刷新');
        }
        chrome.runtime.sendMessage({
            action: 'makeHttpRequest',
            url: `${baseUrl}/base-server/plugin/getTenantInfoScript?accessAddress=${window.location.origin}`,
            options: {method: 'GET'},
        }, response => {
            console.log(JSON.stringify(response.data?.data));
            if (response.data) {
                resolve(response.data.data);
            } else {
                reject(response.error);
            }
        });
    })
  
}
// 检查页面是否展示环境租户，获取租户后展示，并获取菜单；没有租户不能获取菜单
async function checkElementEnvTenant() {
    try{
        if(!getEnvTenantScript){
            getEnvTenantScript = await requestEnvTenant()
        }
        if(getEnvTenantScript) {
            // 使用消息传递执行脚本
            chrome.runtime.sendMessage({ 
                action: "executeEnvTenantScript", 
                script: getEnvTenantScript,
                currentUrl: window.location.href 
            });
        }
    }catch(e){
        console.log(e);
        timeoutEnvTenantId = setTimeout(() => requestIdleCallback(checkElementEnvTenant), 1000);
    }
    
    if(timeoutEnvTenantId) clearTimeout(timeoutEnvTenantId); 
    
    if(!localStorage.getItem('tenantEnv') || !localStorage.getItem('tenantId')){
        timeoutEnvTenantId = setTimeout(() => requestIdleCallback(checkElementEnvTenant), 1000);
        return;
    }
    // 客户端使用录入的uuid;用于新建接口与心跳接口
    if(!document.getElementById('userId')?.textContent || document.getElementById('userId')?.textContent === '-'){
        chrome.storage.local.get(['userId'], (result) => {
            console.log('用户ID', result.userId);
            document.getElementById('userId').textContent = result.userId
            localStorage.setItem('userId', result.userId);
        })
    }
    if(!document.getElementById('latestVersion')?.textContent) await getLastVersion()
    document.getElementById('tenantNumber').textContent = localStorage.getItem('tenantId');
    document.getElementById('tenantName').textContent = localStorage.getItem('tenantName');
    document.getElementById('environment').textContent = localStorage.getItem('tenantEnv');
    document.getElementById('schemeNumber').textContent = localStorage.getItem('studyNum');
    if(['-', 'undefined', '', undefined].includes(document.getElementById('userName')?.textContent))
       document.getElementById('userName').textContent = localStorage.getItem('argusUsername');

    if(localStorage.getItem('accessAddress') !== window.location.origin)
        localStorage.setItem('accessAddress', window.location.origin);

    if(localStorage.getItem('isChangeTenantId') || localStorage.getItem('isChangeStudyNum'))
        try{
            await request('argus/addRecord', 'post', {
                env: localStorage.getItem('tenantEnv'),
                tenantName: localStorage.getItem('tenantName'),
                tenantId: localStorage.getItem('tenantId'),
                username: localStorage.getItem('argusUsername'),
                studyNum: localStorage.getItem('studyNum'),
            })
        }catch(e){
            console.error(e)
        }finally{
            if(timeoutEnvTenantId) clearTimeout(timeoutEnvTenantId); 
            localStorage.removeItem("isChangeTenantId")
            localStorage.removeItem("isChangeStudyNum")
        }

    (features|| []).forEach(item => { 
        let btn;
        try{
            if(!SCHEME_FEATURES.includes(item.name)){ 
                btn = document.getElementById(item.name); 
                btn.style.background = '#EBE9F2'; 
                btn.style.color = '#000000E0';
                btn.style.cursor = 'pointer';
                const mouseOverHandle = () => { 
                    btn.style.backgroundColor = '#e0e0e0'; 
                }
                const mouseOutHandle = () => { 
                    btn.style.backgroundColor = '#EBE9F2'; 
                }
                btn.addEventListener('mouseover', mouseOverHandle); 
                btn.addEventListener('mouseout', mouseOutHandle); 
                btn.removeEventListener('mouseover', mouseOverHandle); 
                btn.removeEventListener('mouseout', mouseOutHandle); 
            } 
        }finally{
            btn = null;
        }
        
    }) 
    updateFeatureButtons(features, localStorage.getItem('projectNum'));
    timeoutEnvTenantId = setTimeout(() => requestIdleCallback(checkElementEnvTenant), 2000);
}

// 定义样式配置
const STYLES = {
    enabled: {
        background: '#EBE9F2',
        color: '#000000E0',
        cursor: 'pointer',
        hover: '#e0e0e0'
    },
    disabled: {
        background: '#F0F0F0',
        color: '#00000040',
        cursor: 'not-allowed'
    }
};

// 应用样式的函数
function applyButtonStyles(button, styles) {
    Object.assign(button.style, {
        background: styles.background,
        color: styles.color,
        cursor: styles.cursor
    });
}

// 添加悬停效果
function addHoverEffects(button, styles) {
    button.addEventListener('mouseover', () => {
        button.style.backgroundColor = styles.hover;
    });
    button.addEventListener('mouseout', () => {
        button.style.backgroundColor = styles.background;
    });
    button.removeEventListener('mouseover', () => {
        button.style.backgroundColor = styles.hover;
    });
    button.removeEventListener('mouseout', () => {
        button.style.backgroundColor = styles.background;
    });
}

// 主函数
function updateFeatureButtons(features, projectNum) {    
    const styles = projectNum ? STYLES.enabled : STYLES.disabled;
    (features || []).forEach(item => {
        if (SCHEME_FEATURES.includes(item.name)) {
            let btn;
            try{
                btn = document.getElementById(item.name);
                if (!btn) return;
                applyButtonStyles(btn, styles);
                addHoverEffects(btn, styles);
            }finally{
                btn = null;
            }
            
        }
    });
}

function formatDate(timestamp) {
    let date = new Date(timestamp);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    date = null;

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

function getLocalStorage(keys) {
    return keys.map(key => localStorage.getItem(key));
}

function appendLocalStorageToURL(keys) {
    // 获取 localStorage 中的值
    const values = getLocalStorage(keys);
    let search = '?'
    // 遍历 keys 和 values，将它们添加为查询参数
    keys.forEach((key, index) => {
        if (values[index] !== null) {
            if(index === 0) search += `${key}=${values[index]}`
            else search += `&${key}=${values[index]}`
        }
    });
    
    // 返回新的 URL 字符串
    return search;
}

function getLastThousandOrAll(arr) {
    if (arr.length <= 1000) {
        return arr;
    } else {
        return arr.slice(-1000);
    }
}
// 下载插件
async function downloadPlugins() {
    chrome.runtime.sendMessage({
        action: 'makeFileHttpRequest',
        url: `${baseUrl}/base-server/plugin/download`,
        options: {method: 'get'},
        headers: {
            argusUsername: localStorage.getItem('userName'), 
            tenantId: localStorage.getItem('tenantId'),
            accessAddress: window.location.origin,
            userId: localStorage.getItem('userId'),
        },
    }, async (response) => {
        if (response.success) {
            let a = document.createElement('a')
            try{
                // 使用 Data URL 创建 Blob
                blob = dataURLtoBlob(response.data);   
                
                a.href = response.data
                a.download = response.fileName
                a.click()
                console.log('插件下载成功！');
            }catch(e){
                console.log(e);
            }finally{
                blob = null;
                a = null;
            }
        } else {
            console.log('插件下载失败');
        }
    });
}
// 菜单Map
const featureEventMap = new Map([
    ['openRecognitionButton', openRecognitionButton],
    ['registerRpaButton', registerRpaButton],
    ['automaticallyCreate', automaticallyCreate],
    ['automaticallyEntry', automaticallyEntry],
    ['singleEntry', singleEntry],
    ['historyData', historyData],
    ['migrationConfiguration', migrationConfiguration],
    ['AosePinchWriting', AosePinchWriting],
    ['automaticallyQc', automaticallyQc],
    ['singleQc', singleQc],
    ['automaticallySuppAdmis', automaticallySuppAdmis],
    ['singleSuppAdmis', singleSuppAdmis],
])
// 清除LocalStorage
function clearLocalStorageExcept() {
    const keysToKeep = ['refreshType', 'credentials'];
    for (let i = localStorage.length - 1; i >= 0; i--) {
        const key = localStorage.key(i);
        if (!keysToKeep.includes(key)) {
            localStorage.removeItem(key);
        }
    }
}
// 调用函数清除 localStorage
clearLocalStorageExcept();

// 创建 PV Copilot 面板
const copilotPanel = document.createElement('div');
copilotPanel.id = 'pvCopilotPanel';
Object.assign(copilotPanel.style, {
    position: 'fixed',
    top: '0',
    right: '0',
    width: '300px',// 将宽度设置为300px
    height: '100%',
    backgroundColor: 'white',
    borderLeft: '1px solid #ccc',
    boxShadow: '-2px 0 5px rgba(0, 0, 0, 0.1)',
    zIndex: '1000',
    overflowY: 'auto',
    padding: '20px',
    display: 'none'// 初始隐藏
  });
appendToBodySafely(copilotPanel);

// 创建 iframe
const iframe = document.createElement('iframe');
iframe.id = 'navigation';
iframe.height = '0px'
iframe.width = '0px'

iframe.src = chrome.runtime.getURL('../html/navigation.html');

// 将容器添加到 body
appendToBodySafely(iframe);

function isScriptLoaded(scriptUrl) {
    const scripts = document.getElementsByTagName('script');
    for (let i = 0; i < scripts.length; i++) {
        if (scripts[i].src === scriptUrl) {
            return true;
        }
    }
    return false;
}
// 补录操作的脚本
const supplementaryInputScriptUrl = chrome.runtime.getURL('../js/supplementaryInput.js');
if (!isScriptLoaded(supplementaryInputScriptUrl)) {
    const script = document.createElement('script');
    script.src = supplementaryInputScriptUrl;
    (document.head || document.documentElement).appendChild(script);
}
// QC操作的脚本
const report_QCScriptUrl = chrome.runtime.getURL('../js/report_QC.js')
if (!isScriptLoaded(report_QCScriptUrl)) {
    const script = document.createElement('script');
    script.src = report_QCScriptUrl;
    (document.head || document.documentElement).appendChild(script);
}

function appendToBodySafely(element) {
    // 检查是否是 frameset
    if (document.body instanceof HTMLFrameSetElement) {
      // 修改页面结构
      try {
        // 1. 创建一个实际的 body 元素
        const actualBody = document.createElement('body');
        
        // 2. 保存 frameset 的引用
        const frameSet = document.body;
        
        // 3. 将新的 body 添加到 html
        document.documentElement.replaceChild(actualBody, frameSet);
        
        // 4. 将 frameset 添加回 body
        actualBody.appendChild(frameSet);
        
        // 5. 将 div 添加到新的 body
        actualBody.appendChild(element);
        return true;
      } catch (e) {
        console.error("修改页面结构失败:", e);
      }
      
      // 最后的方法：创建固定定位的元素，直接添加到 HTML
      document.documentElement.appendChild(element);
      return true;
    } else {
      // 正常情况：直接添加到 body
      document.body.appendChild(element);
      return true;
    }
}
     

// 创建 Loading iframe
const loadingIframe = document.createElement('iframe');
loadingIframe.id = 'loading';
loadingIframe.style = `display: none;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 300px; /* 增加宽度 */
    height: 370px; /* 高度根据内容自动调整 */
    max-height: 70vh; /* 设置最大高度，避免内容过多时撑破页面 */
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 9999;
    text-align: center;
    padding: 20px;
    border-radius: 10px;
    opacity: 0.6;
    background-color: #716DCC; /* 初始背景颜色 */
    color: white;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);`

loadingIframe.src = chrome.runtime.getURL('../html/loading.html');

// 将容器添加到 body
appendToBodySafely(loadingIframe);

// 添加标题和关闭按钮
const header = document.createElement('div');
header.style.display = 'flex';
header.style.justifyContent = 'space-between';
header.style.alignItems = 'center';
header.style.marginBottom = '20px';

const title = document.createElement('h2');
title.innerText = 'PV Copilot';
title.style.margin = '0';

const OperationButton = document.createElement('div');
OperationButton.innerHTML = `
    <button id="downloadButton" style="font-size: 24px;border: none; background: none;cursor: pointer;position: relative;">
        <svg t="1732172180732" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4571" width="20" height="20"><path d="M1024 645.248v330.752a48 48 0 0 1-48 48H48a48 48 0 0 1-48-48v-330.752a48 48 0 0 1 96 0V928h832v-282.752a48 48 0 0 1 96 0z m-545.152 145.984a47.936 47.936 0 0 0 67.904 0l299.904-299.84a48 48 0 1 0-67.968-67.904l-217.792 217.856V48a48.064 48.064 0 0 0-96.064 0v593.472L246.912 423.552a48 48 0 1 0-67.904 67.904l299.84 299.776z" fill="#000000" p-id="4572"></path></svg>
    </button>
    <button id="closeButton" style="font-size: 24px;border: none; background: none;cursor: pointer;">&#10006;</button>
`

header.appendChild(title);
header.appendChild(OperationButton);
copilotPanel.appendChild(header);

const closeButton = document.getElementById('closeButton');
closeButton.addEventListener('click', () => {
    copilotPanel.style.display = 'none';
    toggleButton.style.display = 'block';
    document.body.style.marginRight = '0';
    toggleButton.style.right = '10px'; // 调整按钮位置
});

const downloadButton = document.getElementById('downloadButton');
downloadButton.addEventListener('click', downloadPlugins);

function createVersionDiv(titleText, versionText) {
    const versionDiv = document.createElement('div');
    versionDiv.style.display = 'flex';
    versionDiv.style.justifyContent = 'space-between';
    versionDiv.style.alignItems = 'center';

    const versionTitle = document.createElement('span');
    versionTitle.textContent = titleText;
    versionTitle.style.marginRight = '10px';
    versionDiv.appendChild(versionTitle);

    const version = document.createElement('span');
    version.textContent = versionText;
    version.style.color = '#000000E0';
    version.style.fontSize = '14px';
    versionDiv.appendChild(version);

    return versionDiv;
}
// 当前版本
const currentVersionDiv = createVersionDiv('当前版本', chrome.runtime.getManifest().version);
copilotPanel.appendChild(currentVersionDiv);

// 最新版本
const latestVersionDiv = createVersionDiv('最新版本', '');
latestVersionDiv.querySelector('span:last-child').id = 'latestVersion';
copilotPanel.appendChild(latestVersionDiv);

const baseInfo = document.createElement('div');
Object.assign(baseInfo.style, {
    background: '#F7F8FC',
    padding: '12px',
    borderRadius: '8px',
    margin: '10px 0',
})

const baseInfoItems = {
    'userName': '用户名称',
    'userId': '用户ID',
    'tenantName': '租户名称',
    'tenantNumber': '租户编号',
    'environment' : '环境',
    'schemeNumber': '方案编号',
}

for(let key in baseInfoItems){
    const item = document.createElement('div')
    item.style.display = 'flex';
    item.style.justifyContent = 'space-between';
    // item.style.alignItems = 'center';
    item.style.fontSize = '14px'
    item.style.color = '#00000099';
    if(key !== 'schemeNumber') item.style.marginBottom = '10px'

    const name = document.createElement('span')
    name.style.minWidth = '64px'
    name.textContent = baseInfoItems[key];
    item.appendChild(name)

    const value = document.createElement('span')
    value.id = key;
    value.textContent = '-';
    Object.assign(value.style, {
        color: '000000E0',
        fontSize: '14px',
        wordBreak: 'break-all',
        maxWidth: '215px',
        textAlign: 'right',
    })
    item.appendChild(value);
    baseInfo.appendChild(item);
}

try{
    checkElementEnvTenant();
}catch(e){
    console.error(e)
}

copilotPanel.appendChild(baseInfo);

// 添加功能列表
const featureList = document.createElement('div');
featureList.style.display = 'flex';
featureList.style.flexDirection = 'column';
featureList.style.gap = '10px';

// 判断页面是否点击多个其他窗口
chrome.runtime.onMessage.addListener(function (request){
    if(request.action=='managementList'){        
        if(request.feature.name === 'openRecognitionButton'){
            if(localStorage.getItem('projectNum')) handleFeature(request.feature, request.data);
        }else{
            handleFeature(request.feature, request.data);
        }
    }
})

copilotPanel.appendChild(featureList);

// 增加板块，展示录入的打印日志
const logPanel = document.createElement('div');
logPanel.id = 'log-container';
logPanel.style.cssText = `
    background-color: #F7F8FC;
    border: 1px solid #ccc;
    padding: 10px;
    max-height: 536px;
    overflow-y: auto;
    margin-top: 15px;
`;
copilotPanel.appendChild(logPanel);

// 保存原始的 console.log 方法
const originalConsoleLog = console.log;
// 重写 console.log 方法
console.log = function(...args) {

    // UAT环境不再打印日志，避免内存溢出
    // if (uatPatterns.some(pattern => pattern.test(currentUrl))) {
    //     return
    // }

    // 调用原始的 console.log 方法
    originalConsoleLog.apply(console, args);

    // 将日志内容添加到页面
    const logEntry = document.createElement('div');
    logEntry.innerHTML = getLastThousandOrAll(args).map(arg => {
        const nowDate = new Date().getTime();
        return formatDate(nowDate) + " : " + (typeof arg === 'object' ? JSON.stringify(arg) : arg);
    }).join('<br /><br />');
    logPanel.appendChild(logEntry);

    // 自动滚动到最新日志
    logPanel.scrollTop = logPanel.scrollHeight;
};


// 创建显示/隐藏面板的图标按钮
const toggleButton = document.createElement('button');
toggleButton.innerHTML = '&#9776;'; // 使用汉堡菜单图标
Object.assign(toggleButton.style, {
    position: 'fixed',
    top: '80px',
    right: '0px',
    zIndex: '1001',
    padding: '5px',
    border: '1px solid #ccc',
    borderRadius: '5px',
    backgroundColor: '#f9f9f9',
    cursor: 'pointer'
  });
toggleButton.addEventListener('mouseover', () => {
    toggleButton.style.backgroundColor = '#e0e0e0';
});
toggleButton.addEventListener('mouseout', () => {
    toggleButton.style.backgroundColor = '#f9f9f9';
});
toggleButton.addEventListener('click', () => {
    copilotPanel.style.display = 'block';
    document.body.style.marginRight = '340px'; // 调整内容右移宽度
    toggleButton.style.right = '340px'; // 调整按钮位置
    toggleButton.style.display = 'none';
});

appendToBodySafely(toggleButton);

// 模拟检测登录页面，这里简单判断是否有用户名和密码输入框
function isLoginPage() {
    const usernameInput = document.querySelector('input[type="text"], input[id="username"]') || document.getElementById(mainAppId)?.contentDocument?.querySelector('input[type="text"], input[id="loginId"]') || document.querySelector('input[type="text"], input[name="userId"]');
    const passwordInput = document.querySelector('input[type="password"]')|| document.getElementById(mainAppId)?.contentDocument?.querySelector('input[type="password"], input[id="password"]') || document.querySelector('input[type="password"], input[name="password"]');
    return usernameInput && passwordInput;
}

function credentialInitialStyle (popup){
    if(!popup) return
    Object.assign(popup.style, {
        position: 'absolute',
        width: '240px',
        height: '150px',
        backgroundColor: 'white',
        zIndex: '9',
        border: '1px solid #ccc',
        boxShadow: '0 0 10px rgba(0, 0, 0, 0.1)',
        transform: ''
    });
    // 获取 id 为 username 的 input 元素
    const usernameInput = document.getElementById("username") || document.getElementById(mainAppId)?.contentDocument?.getElementById("loginId") || document.querySelector('input[name="userId"]');
    if (!usernameInput) return;
    // 获取 input 的位置信息
    const rect = usernameInput.getBoundingClientRect();
    // 考虑滚动条位置，计算 offsetTop
    const inputBottom = rect.top + usernameInput.offsetHeight + window.scrollY;
    const inputLeft = rect.left + window.scrollX;
    
    // 设置 iframe 的位置：左侧与 input 对齐，顶部放在 input 下方
    popup.style.left = inputLeft + "px";
    popup.style.top = inputBottom + "px";
}

// 显示悬浮框
async function showCredentialPopup() {
    if (isLoginPage()) {
        const credentials = getCredentials();

        if (credentials.length > 0) {
            const popup = document.createElement('iframe');
            popup.id = 'credentials'
            // 设置初始化样式
            credentialInitialStyle(popup)

            popup.src = chrome.runtime.getURL('../html/quickLogin.html');
            appendToBodySafely(popup);

            // 快捷登录框加载成功后，将缓存的账户数据传递给quickLogin页面进行展示
            popup.onload = function() {
                popup.contentWindow.postMessage({
                    action: "getAllCredentials",
                    data: credentials
                }, '*');
            };

            const usernameInput = document.getElementById("username") || document.getElementById(mainAppId)?.contentDocument?.getElementById("loginId") || document.querySelector('input[name="userId"]');
            // 添加聚焦事件：输入框获得焦点时触发
            usernameInput?.addEventListener('click', function() {
                popup.style.display = 'block';
            });
            // 监听点击事件，判断点击是否在 quickLogin 窗口外并且不在输入框内
            // document.getElementById('fm_MainApp')?.contentDocument是由于kelun登录页面的登录框在子iframe里面
            (document.getElementById('fm_MainApp')?.contentDocument || document).addEventListener('click', () => {
                if ((document.getElementById('fm_MainApp')?.contentDocument || document).activeElement !== iframe 
                && (document.getElementById('fm_MainApp')?.contentDocument || document).activeElement !== usernameInput) {
                    popup.style.display = 'none';
                }
            });
        }
        function setElementValue(id, value, doc) {
            const element = (doc || document).getElementById(id);
            if (element && value) element.value = value;
        }
        chrome.runtime.onMessage.addListener(function (message, sender, sendResponse) {
            // quickLogin子页面的点击触发的设置账户的缓存值
            if (message.action === "setCredentialValue") {
                const data = message.data;
                const credentialsBox = document.getElementById('credentials')
                // 触发编辑账户信息弹出框
                if(data === 'edit'){
                    credentialsBox.style.width = '100%';
                    credentialsBox.style.height = '100%';
                    credentialsBox.style.backgroundColor = 'transparent';
                    credentialsBox.style.position = 'fixed';
                    credentialsBox.style.zIndex = '999';
                    credentialsBox.style.top = '50%';
                    credentialsBox.style.left = '50%';
                    credentialsBox.style.transform = 'translate(-50%, -50%)';
                // 编辑成功后进行更改缓存数据
                }else if(Array.isArray(data)){
                    localStorage.setItem('credentials', JSON.stringify(data));
                // 点击所选账户进行赋值
                }else {
                    // argus页面赋值
                    if(loginArgusUrls.some(pattern => pattern.test(currentUrl))){
                        setElementValue('username', data.username);
                        setElementValue('password', data.password);
                    // AG 页面进行数据赋值
                    }else if(loginLssUrls.some(pattern => pattern.test(currentUrl))){
                        const companyId = document.querySelector('input[name="companyId"]')
                        const userId = document.querySelector('input[name="userId"]')
                        const password = document.querySelector('input[type="password"], input[name="password"]')
                        const dbnames = document.querySelector('#dbnames')
                        if(companyId) companyId.value = data.companyId
                        if(userId) userId.value = data.username
                        if(password) password.value = data.password
                        if(dbnames) dbnames.value = data.dbnames
                    // kelun页面进行赋值
                    } else {
                        setElementValue('loginId', data.username, document.getElementById(mainAppId)?.contentDocument);
                        setElementValue('password', data.password, document.getElementById(mainAppId)?.contentDocument);
                    }
                    // 赋值后隐藏快捷登录窗口
                    credentialsBox.style.display = 'none'
                }
            }
            // 编辑账户信息点击保存或取消，将快捷登录窗口还原到初始样式
            if (message.action === "restoreCredentialPop") {
                const credentialsBox = document.getElementById('credentials')
                credentialInitialStyle(credentialsBox)
            }
        });
    }else{
        const credentials = getCredentials();
        const now = Date.now();
        // 假设 updateTime 为时间戳（毫秒单位），如不是，请根据情况进行转换
        const filteredCredentials = credentials.map(item => {
            if(item.isLogin === 'no' && Math.abs(now - item.updateTime) < 60000) {   
                return {
                    ...item,
                    isLogin: 'yes'
                }
            }else {
                return item;
            }
        }).filter(item => item.isLogin === 'yes');
        localStorage.setItem('credentials', JSON.stringify(filteredCredentials));
    }
}

// 从 localStorage 中获取账号密码信息
function getCredentials() {
    const credentials = localStorage.getItem('credentials');
    return credentials ? JSON.parse(credentials) : [];
}

/**
 * 当 DOM 加载完成时执行回调函数
 * @param {Function} callback - DOM 就绪后要执行的回调函数
 */
function ready(callback) {
    // 如果文档已经完成加载，直接执行回调
    if (document.readyState === 'complete' || 
        document.readyState === 'interactive') {
      // 将回调函数推入事件循环末尾执行，确保执行顺序
      setTimeout(callback, 1);
      return;
    }
    // 如果文档尚未加载完成，监听 DOMContentLoaded 事件
    document.addEventListener('load', function() {
      callback();
    });
}
  
ready(function() { 
    showCredentialPopup();
});


let password = '';
// 监听来自背景脚本的消息
chrome.runtime.onMessage.addListener(function (message, sender, sendResponse) {
    if (message.type === 'requestInfo') {
        // 在这里可以添加你在 content.js 中处理请求信息的逻辑
        // 登录成功，获取当前输入的用户名和密码
        let usernameInput;
        let passwordInput;
        let companyIdInput;
        let dbnamesInput;
        // argus地址获取用户名和密码
        if(loginArgusUrls.some(pattern => pattern.test(currentUrl))){
            usernameInput = document.querySelector('input[type="text"], input[id="username"]');
            passwordInput = document.querySelector('input[type="password"]');
        // AG地址获取登录数据
        }else if(loginLssUrls.some(pattern => pattern.test(currentUrl))){
            usernameInput = document.querySelector('input[name="userId"]');
            passwordInput = document.querySelector('input[type="password"], input[name="password"]');
            companyIdInput = document.querySelector('input[name="companyId"]');
            dbnamesInput = document.querySelector('#dbnames');
        // kelun地址获取登录数据
        }else {
            usernameInput = document.getElementById(mainAppId)?.contentDocument?.querySelector('input[id="loginId"]');
            passwordInput = document.getElementById(mainAppId)?.contentDocument?.querySelector('input[type="password"]');
        }
        const username = usernameInput ? usernameInput.value : '';
        // kelun密码会加密，避免获取加密数据进行登录
        password = passwordInput ? (passwordInput.value?.length > 32 ? password : passwordInput.value) : '';
        const companyId = companyIdInput ? companyIdInput.value : '';
        const dbnames = dbnamesInput ? dbnamesInput.value : '';
       
        if (!username || !password) {
            console.log('用户名或密码为空');
            return;
        }
        console.log(username, password);
        let credentials = [];
        try {
            const existingCredentials = localStorage.getItem('credentials');
            credentials = existingCredentials ? JSON.parse(existingCredentials) : [];
        } catch (e) {
            console.error('解析现有凭证失败:', e);
            credentials = [];
        }
        // 准备新的凭证数据
        const newCredential = {
            username: username,
            password: password,
            url: window.location.origin,
            name: window.location.hostname,
            companyId: companyId,
            dbnames: dbnames,
            updateTime: new Date().getTime(),
            isLogin: 'no',
        };
         // 检查是否已存在
         const existingIndex = credentials.findIndex(
            cred => cred.username === username
        );
        // ? 更新现有凭证 : 添加新凭证
        existingIndex !== -1 ? credentials[existingIndex] = newCredential : credentials.push(newCredential);;
        // 保存到 localStorage
        try {
            credentials.sort((a, b) => new Date(b.updateTime) - new Date(a.updateTime));
            localStorage.setItem('credentials', JSON.stringify(credentials));
            console.log('凭证保存成功', credentials)
        } catch (e) {
            console.error('保存凭证失败:', e);
        }
    }
});