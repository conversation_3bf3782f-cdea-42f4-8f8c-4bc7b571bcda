import {
    BalloonEditor,
    ClassicEditor,
    AccessibilityHelp,
    Autoformat,
    AutoLink,
    Autosave,
    Bold,
    BalloonToolbar,
    Code,
    CodeBlock,
    Essentials,
    Heading,
    Italic,
    Link,
    Markdown,
    Paragraph,
    PasteFromMarkdownExperimental,
    SelectAll,
    SourceEditing,
    Table,
    TableCaption,
    TableCellProperties,
    TableColumnResize,
    TableProperties,
    TableToolbar,
    TextTransformation,
    Undo
} from '../js/ckeditor5.js';

window.BalloonEditor = BalloonEditor;
window.ClassicEditor = ClassicEditor;
window.AccessibilityHelp = AccessibilityHelp;
window.Autoformat = Autoformat;
window.AutoLink = AutoLink;
window.Autosave = Autosave;
window.Bold = Bold;
window.BalloonToolbar = BalloonToolbar;
window.Code = Code;
window.CodeBlock = CodeBlock;
window.Essentials = Essentials;
window.Heading = Heading;
window.Italic = Italic;
window.Link = Link;
window.Markdown = Markdown;
window.Paragraph = Paragraph;
window.PasteFromMarkdownExperimental = PasteFromMarkdownExperimental;
window.SelectAll = SelectAll;
window.SourceEditing = SourceEditing;
window.Table = Table;
window.TableCaption = TableCaption;
window.TableCellProperties = TableCellProperties;
window.TableColumnResize = TableColumnResize;
window.TableProperties = TableProperties;
window.TableToolbar = TableToolbar;
window.TextTransformation = TextTransformation;
window.Undo = Undo;