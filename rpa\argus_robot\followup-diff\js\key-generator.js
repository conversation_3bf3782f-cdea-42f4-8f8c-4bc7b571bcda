/**
 * 主键生成器模块
 * 为列表数据生成智能主键配置
 */

class KeyGenerator {
    constructor() {
        // 默认主键配置
        this.defaultConfig = {
            "SAE/ECI/AESI的详细情况": {
                keyFields: ["不良事件名称", "发生日期"],
                description: "基于不良事件名称和发生日期生成主键"
            },
            "与事件相关的实验室检查": {
                keyFields: ["检查日期", "检查项目名称"],
                description: "基于检查日期和检查项目名称生成主键"
            },
            "合并用药信息": {
                keyFields: ["合并用药品名称", "合并用药开始日期"],
                description: "基于药品名称和开始日期生成主键"
            },
            "治疗用药信息": {
                keyFields: ["治疗用药品名称", "治疗用药开始日期"],
                description: "基于药品名称和开始日期生成主键"
            },
            "与事件相关的现病史": {
                keyFields: ["疾病名称", "开始日期"],
                description: "基于疾病名称和开始日期生成主键"
            },
            "与事件相关的既往病史": {
                keyFields: ["疾病名称", "开始日期"],
                description: "基于疾病名称和开始日期生成主键"
            },
            "其他导致该SAE/ECI/AESI的可能因素": {
                keyFields: ["事件名称", "可能因素的分类"],
                description: "基于事件名称和可能因素分类生成主键"
            },
            "试验用药列表信息": {
                keyFields: ["试验用药名称", "首次用药日期"],
                description: "基于试验用药名称和首次用药日期生成主键"
            },
            "剂量信息": {
                keyFields: ["该剂量用药开始日期", "试验用药剂量数值", "试验用药剂量单位"],
                description: "基于用药开始日期、剂量数值和单位生成主键"
            },
            "药物信息": {
                keyFields: ["试验用药品名称"],
                description: "基于试验用药品名称生成主键"
            },
            "英文缩写": {
                keyFields: ["once", "tid", "bid"],
                description: "基于缩写内容生成主键"
            },
            "试验用药批号": {
                keyFields: [],
                description: "数组项为字符串，使用值本身作为主键"
            }
        };
    }

    /**
     * 获取默认配置
     * @returns {Object} 默认配置
     */
    getDefaultConfig() {
        return JSON.parse(JSON.stringify(this.defaultConfig));
    }

    /**
     * 分析数据结构并生成主键配置建议
     * @param {Object} data - 数据对象
     * @returns {Object} 配置建议
     */
    analyzeDataStructure(data) {
        const suggestions = {};
        this.traverseObject(data, '', suggestions);
        return suggestions;
    }

    /**
     * 遍历对象结构
     * @param {*} obj - 对象
     * @param {string} path - 路径
     * @param {Object} suggestions - 建议配置
     */
    traverseObject(obj, path, suggestions) {
        if (Array.isArray(obj) && obj.length > 0) {
            const moduleName = this.getModuleName(path);
            if (moduleName && !suggestions[moduleName]) {
                suggestions[moduleName] = this.generateKeyFieldSuggestions(obj);
            }
            
            // 递归处理数组中的对象
            obj.forEach(item => {
                if (typeof item === 'object' && item !== null) {
                    this.traverseObject(item, path, suggestions);
                }
            });
        } else if (typeof obj === 'object' && obj !== null) {
            Object.keys(obj).forEach(key => {
                const newPath = path ? `${path}.${key}` : key;
                this.traverseObject(obj[key], newPath, suggestions);
            });
        }
    }

    /**
     * 为数组生成主键字段建议
     * @param {Array} array - 数组
     * @returns {Object} 主键配置
     */
    generateKeyFieldSuggestions(array) {
        if (array.length === 0) {
            return { keyFields: [], description: "空数组" };
        }

        const firstItem = array[0];
        
        // 如果数组项是字符串或基础类型
        if (typeof firstItem !== 'object') {
            return { 
                keyFields: [], 
                description: "基础类型数组，使用值本身作为主键" 
            };
        }

        // 分析对象字段
        const fields = Object.keys(firstItem);
        const keyFields = this.selectBestKeyFields(fields, array);
        
        return {
            keyFields,
            description: `基于 ${keyFields.join(', ')} 生成主键`
        };
    }

    /**
     * 选择最佳主键字段
     * @param {Array} fields - 字段列表
     * @param {Array} array - 数组数据
     * @returns {Array} 主键字段
     */
    selectBestKeyFields(fields, array) {
        // 优先级规则
        const priorityPatterns = [
            /名称$/,
            /^.*名称.*$/,
            /日期$/,
            /^.*日期.*$/,
            /编号$/,
            /^.*编号.*$/,
            /ID$/i,
            /^.*ID.*$/i
        ];

        const candidates = [];
        
        // 按优先级评分
        fields.forEach(field => {
            let score = 0;
            
            // 检查优先级模式
            priorityPatterns.forEach((pattern, index) => {
                if (pattern.test(field)) {
                    score += (priorityPatterns.length - index) * 10;
                }
            });

            // 检查字段值的唯一性
            const uniqueness = this.calculateUniqueness(array, field);
            score += uniqueness * 5;

            // 检查字段值的完整性（非空率）
            const completeness = this.calculateCompleteness(array, field);
            score += completeness * 3;

            candidates.push({ field, score });
        });

        // 排序并选择最佳字段
        candidates.sort((a, b) => b.score - a.score);
        
        // 选择前2个最佳字段作为组合主键
        const selectedFields = candidates.slice(0, 2).map(c => c.field);
        
        // 如果只有一个高分字段，只返回一个
        if (candidates.length > 1 && candidates[0].score > candidates[1].score * 2) {
            return [candidates[0].field];
        }
        
        return selectedFields;
    }

    /**
     * 计算字段值的唯一性
     * @param {Array} array - 数组
     * @param {string} field - 字段名
     * @returns {number} 唯一性分数 (0-1)
     */
    calculateUniqueness(array, field) {
        const values = array.map(item => item[field]).filter(v => v !== undefined && v !== null && v !== '');
        const uniqueValues = new Set(values);
        return values.length > 0 ? uniqueValues.size / values.length : 0;
    }

    /**
     * 计算字段值的完整性
     * @param {Array} array - 数组
     * @param {string} field - 字段名
     * @returns {number} 完整性分数 (0-1)
     */
    calculateCompleteness(array, field) {
        const nonEmptyValues = array.filter(item => {
            const value = item[field];
            return value !== undefined && value !== null && value !== '';
        });
        return array.length > 0 ? nonEmptyValues.length / array.length : 0;
    }

    /**
     * 获取模块名称
     * @param {string} path - 路径
     * @returns {string} 模块名称
     */
    getModuleName(path) {
        const parts = path.split('.');
        return parts[0] || '';
    }

    /**
     * 验证主键配置
     * @param {Object} config - 配置
     * @param {Object} data - 数据
     * @returns {Object} 验证结果
     */
    validateConfig(config, data) {
        const results = {};
        
        Object.keys(config).forEach(moduleName => {
            const moduleConfig = config[moduleName];
            const moduleData = this.getModuleData(data, moduleName);
            
            if (!moduleData || !Array.isArray(moduleData)) {
                results[moduleName] = {
                    valid: false,
                    message: "模块数据不存在或不是数组"
                };
                return;
            }

            const validation = this.validateModuleConfig(moduleConfig, moduleData);
            results[moduleName] = validation;
        });

        return results;
    }

    /**
     * 验证模块配置
     * @param {Object} config - 模块配置
     * @param {Array} data - 模块数据
     * @returns {Object} 验证结果
     */
    validateModuleConfig(config, data) {
        const { keyFields } = config;
        
        if (!keyFields || keyFields.length === 0) {
            return { valid: true, message: "使用索引作为主键" };
        }

        // 检查字段是否存在
        const missingFields = [];
        const firstItem = data[0] || {};
        
        keyFields.forEach(field => {
            if (!(field in firstItem)) {
                missingFields.push(field);
            }
        });

        if (missingFields.length > 0) {
            return {
                valid: false,
                message: `字段不存在: ${missingFields.join(', ')}`
            };
        }

        // 检查主键唯一性
        const keys = new Set();
        let duplicates = 0;
        
        data.forEach(item => {
            const key = keyFields.map(field => item[field] || '').join('|');
            if (keys.has(key)) {
                duplicates++;
            } else {
                keys.add(key);
            }
        });

        if (duplicates > 0) {
            return {
                valid: false,
                message: `发现 ${duplicates} 个重复主键`
            };
        }

        return { valid: true, message: "配置有效" };
    }

    /**
     * 获取模块数据
     * @param {Object} data - 完整数据
     * @param {string} moduleName - 模块名称
     * @returns {Array} 模块数据
     */
    getModuleData(data, moduleName) {
        return data[moduleName];
    }

    /**
     * 合并配置
     * @param {Object} defaultConfig - 默认配置
     * @param {Object} userConfig - 用户配置
     * @returns {Object} 合并后的配置
     */
    mergeConfig(defaultConfig, userConfig) {
        const merged = { ...defaultConfig };
        
        Object.keys(userConfig).forEach(key => {
            merged[key] = {
                ...defaultConfig[key],
                ...userConfig[key]
            };
        });

        return merged;
    }
}

// 导出到全局
window.KeyGenerator = KeyGenerator;
