/* 病例随访数据对比工具样式 */

/* 全局样式 */
body {
    background-color: #f8f9fa;
    font-family: 'Microsoft YaHei', sans-serif;
}

/* 数据阶段容器 */
.data-stage-container {
    max-height: 600px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    background-color: #fff;
    padding: 1rem;
}

/* 数据模块卡片 */
.data-module {
    border: 1px solid #e9ecef;
    border-radius: 0.375rem;
    margin-bottom: 1rem;
    background-color: #fff;
}

.data-module-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    padding: 0.75rem 1rem;
    font-weight: 600;
    font-size: 0.9rem;
}

.data-module-body {
    padding: 1rem;
}

/* 字段样式 */
.field-item {
    padding: 0.25rem 0;
    border-bottom: 1px solid #f1f3f4;
}

.field-item:last-child {
    border-bottom: none;
}

.field-label {
    font-weight: 500;
    color: #495057;
    font-size: 0.8rem;
    display: block;
    margin-bottom: 0.125rem;
}

.field-value {
    font-size: 0.8rem;
    word-break: break-word;
    color: #6c757d;
}

/* 基础字段样式（单行显示） */
.field-item.basic-field {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.field-item.basic-field .field-label {
    min-width: 100px;
    margin-bottom: 0;
    margin-right: 0.5rem;
    flex-shrink: 0;
}

.field-item.basic-field .field-value {
    text-align: right;
    flex: 1;
}

/* 复杂字段样式（多行显示） */
.field-item.complex-field .field-label {
    font-weight: 600;
    color: #495057;
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 0.25rem;
    margin-bottom: 0.5rem;
}

.field-item.complex-field .field-value {
    margin-left: 0.5rem;
}

/* 变更高亮样式 */
.field-changed {
    background-color: #fff3cd;
    border-left: 4px solid #ffc107;
    padding-left: 0.75rem;
    margin-left: -0.75rem;
}

.field-added {
    background-color: #d1edff;
    border-left: 4px solid #0d6efd;
    padding-left: 0.75rem;
    margin-left: -0.75rem;
}

.field-deleted {
    background-color: #f8d7da;
    border-left: 4px solid #dc3545;
    padding-left: 0.75rem;
    margin-left: -0.75rem;
    text-decoration: line-through;
    opacity: 0.7;
}

/* 数组数据样式 */
.array-container {
    border: 1px solid #e9ecef;
    border-radius: 0.25rem;
    margin-top: 0.5rem;
}

.array-item {
    border-bottom: 1px solid #f1f3f4;
    padding: 0.75rem;
}

.array-item:last-child {
    border-bottom: none;
}

.array-item-header {
    font-weight: 600;
    color: #6c757d;
    font-size: 0.8rem;
    margin-bottom: 0.5rem;
}

/* 嵌套对象和数组样式 */
.nested-object-container {
    border-left: 2px solid #e9ecef;
    padding-left: 0.75rem;
    margin-top: 0.25rem;
}

.nested-object-label {
    font-weight: 700;
    color: #495057;
}

.nested-array-container {
    margin-top: 0.25rem;
}

.nested-array-item {
    border: 1px solid #e9ecef;
    border-radius: 0.25rem;
    margin-bottom: 0.375rem;
    background-color: #fafbfc;
}

.nested-array-item:last-child {
    margin-bottom: 0;
}

.nested-array-item-header {
    background-color: #f1f3f4;
    border-bottom: 1px solid #dee2e6;
    padding: 0.25rem 0.5rem;
    font-weight: 600;
    color: #6c757d;
    font-size: 0.75rem;
}

.nested-array-item-content {
    padding: 0.5rem;
}

.nested-array-item-content .field-item {
    padding: 0.125rem 0;
    border-bottom: 1px solid #f1f3f4;
}

.nested-array-item-content .field-item:last-child {
    border-bottom: none;
}

/* 嵌套层级样式优化 */
.field-item.nested-level-1 {
    margin-left: 0.75rem;
}

.field-item.nested-level-2 {
    margin-left: 1.5rem;
}

.field-item.nested-level-3 {
    margin-left: 2.25rem;
}

.field-item.nested-level-1 .field-label,
.field-item.nested-level-2 .field-label,
.field-item.nested-level-3 .field-label {
    font-size: 0.75rem;
    color: #6c757d;
}

.field-item.nested-level-1 .field-value,
.field-item.nested-level-2 .field-value,
.field-item.nested-level-3 .field-value {
    font-size: 0.75rem;
}

/* 简单数组样式 */
.simple-array-container {
    line-height: 1.4;
}

.simple-array-container .badge {
    border: 1px solid #dee2e6;
    margin-right: 0.25rem;
    margin-bottom: 0.125rem;
    font-size: 0.7rem;
    padding: 0.125rem 0.375rem;
    display: inline-block;
}

.badge.bg-light.text-dark {
    border: 1px solid #dee2e6;
    margin-right: 0.25rem;
    margin-bottom: 0.125rem;
    font-size: 0.7rem;
    padding: 0.125rem 0.375rem;
}

/* 变更列表样式 */
.change-list-container {
    /* max-height: 500px; */
    height: calc(100vh - 170px);
    overflow-y: auto;
}

.change-item {
    transition: all 0.2s ease;
}

.change-item:hover {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.change-item.confirmed {
    border-left: 4px solid #198754;
}

.change-item.pending {
    border-left: 4px solid #6c757d;
}

/* 模块变更区域样式 */
.module-changes-section {
    border: 1px solid #e9ecef;
    border-radius: 0.5rem;
    overflow: hidden;
    background-color: #fff;
}

.module-header .card-header {
    border-bottom: 1px solid #e9ecef;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.module-stats .badge {
    font-size: 0.7rem;
}

.module-changes-list {
    padding: 1rem;
    background-color: #fafbfc;
}

.module-changes-list .change-item {
    margin-left: 1rem;
    border-left: 2px solid #dee2e6;
}

.field-path {
    color: #495057;
    font-size: 0.9rem;
}

/* 进度条样式 */
.progress {
    background-color: #e9ecef;
}

.progress-bar {
    font-size: 0.7rem;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
}

/* 模块操作按钮样式 */
.module-actions .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    border-radius: 0.25rem;
}

.module-actions .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* 响应式调整 */
@media (max-width: 768px) {
    .module-stats {
        display: none;
    }

    .module-actions .btn {
        padding: 0.2rem 0.4rem;
        font-size: 0.7rem;
    }

    .module-header h6 {
        font-size: 0.9rem;
    }
}

/* 变更类型徽章样式 */
.badge.change-add {
    background-color: #198754;
}

.badge.change-modify {
    background-color: #ffc107;
    color: #000;
}

.badge.change-delete {
    background-color: #dc3545;
}

/* 统计信息样式 */
.stat-item {
    padding: 0.5rem;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    line-height: 1;
}

.stat-label {
    font-size: 0.75rem;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* 人工干预样式 */
.manual-intervention-panel {
    background-color: #fff;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
}

.mapping-item {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    border: 1px solid #e9ecef;
    border-radius: 0.25rem;
    margin-bottom: 0.5rem;
    background-color: #f8f9fa;
}

.mapping-source, .mapping-target {
    flex: 1;
    padding: 0.5rem;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    background-color: #fff;
    font-size: 0.875rem;
}

.mapping-arrow {
    margin: 0 1rem;
    color: #6c757d;
}

/* 拖拽样式 */
.draggable {
    cursor: move;
}

.draggable:hover {
    background-color: #e9ecef;
}

.drag-over {
    background-color: #cff4fc;
    border-color: #0dcaf0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .data-stage-container {
        max-height: 400px;
    }
    
    .field-item {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .field-label {
        min-width: auto;
        margin-bottom: 0.25rem;
    }
    
    .field-value {
        text-align: left;
    }
}

/* 加载动画 */
.loading-spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 工具提示样式 */
.tooltip-custom {
    position: relative;
    cursor: help;
}

.tooltip-custom:hover::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: #333;
    color: #fff;
    padding: 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    white-space: nowrap;
    z-index: 1000;
}

/* 滚动条样式 */
.data-stage-container::-webkit-scrollbar,
.change-list-container::-webkit-scrollbar {
    width: 6px;
}

.data-stage-container::-webkit-scrollbar-track,
.change-list-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.data-stage-container::-webkit-scrollbar-thumb,
.change-list-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.data-stage-container::-webkit-scrollbar-thumb:hover,
.change-list-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 配置错误气泡样式 */
.btn.position-relative .badge {
    font-size: 0.6rem;
    padding: 0.25rem 0.4rem;
    min-width: 1.2rem;
    height: 1.2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: pulse-error 2s infinite;
}

@keyframes pulse-error {
    0% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.1);
        opacity: 0.8;
    }
    100% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
    }
}

.btn.position-relative:hover .badge {
    animation: none;
    transform: translate(-50%, -50%) scale(1.1);
}

/* 配置错误列表样式 */
.alert .text-danger {
    margin-bottom: 0.25rem;
    font-size: 0.875rem;
}

.alert .text-danger:last-child {
    margin-bottom: 0;
}

.alert .text-danger i {
    width: 12px;
    text-align: center;
}

/* 模态框样式增强 */
.modal-content {
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.modal-header {
    border-bottom: 1px solid #e9ecef;
    background-color: #f8f9fa;
}

/* 按钮样式增强 */
.btn {
    border-radius: 0.375rem;
    font-weight: 500;
    transition: all 0.15s ease-in-out;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* 卡片样式增强 */
.card {
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    transition: box-shadow 0.15s ease-in-out;
}

.card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

/* 导航标签样式 */
.nav-tabs .nav-link {
    border: none;
    color: #6c757d;
    font-weight: 500;
}

.nav-tabs .nav-link.active {
    background-color: #fff;
    border-bottom: 2px solid #0d6efd;
    color: #0d6efd;
}

/* 表单控件样式 */
.form-control:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* 警告样式 */
.alert {
    border: none;
    border-radius: 0.5rem;
}

/* 代码样式 */
code {
    background-color: #f8f9fa;
    color: #e83e8c;
    padding: 0.125rem 0.25rem;
    border-radius: 0.25rem;
    font-size: 0.875em;
}

/* 数组和对象预览样式 */
.simple-array {
    max-width: 300px;
    overflow: hidden;
}

.simple-array .badge {
    margin-bottom: 0.25rem;
    font-size: 0.75rem;
}

.object-array, .object-value {
    position: relative;
}

.array-preview, .object-preview {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 0.25rem;
    padding: 0.75rem;
    max-height: 200px;
    overflow-y: auto;
    font-size: 0.875rem;
}

.array-item-preview {
    background-color: #fff;
    border: 1px solid #dee2e6 !important;
    font-size: 0.8rem;
    line-height: 1.3;
}

.array-item-preview:last-child {
    margin-bottom: 0 !important;
}

.object-field-preview {
    padding: 0.25rem 0;
    border-bottom: 1px solid #f1f3f4;
    font-size: 0.8rem;
    line-height: 1.3;
}

.object-field-preview:last-child {
    border-bottom: none;
}

.object-field-preview strong {
    color: #495057;
    font-weight: 600;
}

/* 预览按钮样式 */
.btn-outline-info.btn-sm {
    padding: 0.125rem 0.375rem;
    font-size: 0.7rem;
    line-height: 1.2;
}

/* 滚动条样式 - 预览容器 */
.array-preview::-webkit-scrollbar,
.object-preview::-webkit-scrollbar {
    width: 4px;
}

.array-preview::-webkit-scrollbar-track,
.object-preview::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 2px;
}

.array-preview::-webkit-scrollbar-thumb,
.object-preview::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 2px;
}

/* 变更值显示样式 */
.change-value {
    display: inline-block;
    max-width: 400px;
    word-break: break-word;
    font-family: 'Courier New', monospace;
    font-size: 0.8rem;
    line-height: 1.3;
}

.complex-value {
    color: #6f42c1;
    font-weight: 500;
}

.truncated-value {
    position: relative;
}

.array-value {
    color: #198754;
    font-weight: 500;
}

.object-array-value {
    color: #0d6efd;
    font-weight: 500;
}

.object-value {
    color: #fd7e14;
    font-weight: 500;
}

/* 列表项详情样式 */
.list-item-detail {
    border: 1px solid #e9ecef;
    border-radius: 0.25rem;
    padding: 0.5rem;
    background-color: #f8f9fa;
    margin-bottom: 0.5rem;
}

.list-item-header {
    font-weight: 600;
    color: #495057;
    font-size: 0.8rem;
    margin-bottom: 0.5rem;
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 0.25rem;
}

.list-item-fields {
    font-size: 0.75rem;
}

.object-detail {
    border: 1px solid #e9ecef;
    border-radius: 0.25rem;
    padding: 0.5rem;
    background-color: #f8f9fa;
}

.field-row {
    display: flex;
    margin-bottom: 0.25rem;
    align-items: flex-start;
}

.field-row:last-child {
    margin-bottom: 0;
}

.field-name {
    font-weight: 600;
    color: #495057;
    min-width: 120px;
    margin-right: 0.5rem;
    font-size: 0.75rem;
}

.field-value {
    flex: 1;
    color: #6c757d;
    font-size: 0.75rem;
    word-break: break-word;
}

.field-value .badge {
    font-size: 0.65rem;
    margin-bottom: 0.125rem;
}

/* 空值显示样式 */
.empty-value {
    font-style: italic;
    background-color: #fff3cd;
    border: 1px dashed #ffc107;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    color: #856404;
}

.text-muted:not(.empty-value) {
    background-color: #f8f9fa;
    border: 1px dashed #6c757d;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .simple-array {
        max-width: 200px;
    }

    .array-preview, .object-preview {
        max-height: 150px;
        padding: 0.5rem;
        font-size: 0.8rem;
    }

    .array-item-preview, .object-field-preview {
        font-size: 0.75rem;
    }

    .change-value {
        max-width: 200px;
        font-size: 0.75rem;
    }
}
