<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>病例随访数据对比工具</title>
    
    <!-- Bootstrap CSS -->
    <link href="../css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="../css/all.min.css" rel="stylesheet">
    <!-- 自定义样式 -->
    <link href="css/followup-diff.css" rel="stylesheet">
</head>
<body>
    <div id="app" x-data="followupDiffApp" x-init="init()">
        <!-- 顶部导航 -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container-fluid">
                <span class="navbar-brand">
                    <i class="fas fa-chart-line me-2"></i>
                    病例随访数据对比工具
                </span>
                <div class="navbar-nav ms-auto">
                    <button class="btn btn-outline-light btn-sm" @click="loadSampleData()" :disabled="loading">
                        <i class="fas fa-download me-1"></i>
                        <span x-text="loading ? '加载中...' : '加载示例数据'"></span>
                    </button>
                </div>
            </div>
        </nav>

        <!-- 主内容区 -->
        <div class="container-fluid mt-3">
            <!-- 加载状态 -->
            <div x-show="loading" class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <p class="mt-2">正在加载数据...</p>
            </div>

            <!-- 错误提示 -->
            <div x-show="error" class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <span x-text="error"></span>
                <button type="button" class="btn-close" @click="error = null"></button>
            </div>

            <!-- 数据对比界面 -->
            <div x-show="!loading && !error && dataLoaded" class="row">
                <!-- 左侧：三阶段数据展示 -->
                <div class="col-lg-10">
                    <div class="card">
                        <div class="card-header">
                            <ul class="nav nav-tabs card-header-tabs" role="tablist">
                                <!-- 映射配置tab -->
                                <li class="nav-item" role="presentation" x-show="listModules.length > 0">
                                    <button class="nav-link"
                                            :class="currentStage === 'mapping' ? 'active' : ''"
                                            data-bs-toggle="tab"
                                            data-bs-target="#mapping-tab"
                                            type="button"
                                            role="tab">
                                        <i class="fas fa-project-diagram me-1"></i>
                                        连线映射
                                        <span class="badge bg-info ms-1" x-text="listModules.length + '个模块'" x-show="listModules.length > 0"></span>
                                        <i class="fas fa-check-circle text-success ms-1" x-show="mappingCompleted" title="映射完成"></i>
                                        <i class="fas fa-exclamation-triangle text-warning ms-1" x-show="!mappingCompleted && currentStage !== 'mapping'" title="需要完成映射"></i>
                                    </button>
                                </li>
                                <!-- 第一阶段tab -->
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link"
                                            :class="currentStage === 'stage1' ? 'active' : ''"
                                            data-bs-toggle="tab"
                                            data-bs-target="#stage1-tab"
                                            type="button"
                                            role="tab"
                                            :disabled="!canEnterStage1()"
                                            :class="canEnterStage1() ? (currentStage === 'stage1' ? 'active' : '') : 'disabled'">
                                        <i class="fas fa-file-medical me-1"></i>
                                        第一阶段对比
                                        <span class="badge bg-primary ms-1" x-text="stage1Changes.length" x-show="stage1Changes.length > 0"></span>
                                        <i class="fas fa-check-circle text-success ms-1" x-show="stage1Confirmed" title="已确认"></i>
                                        <i class="fas fa-lock text-muted ms-1" x-show="!canEnterStage1()" title="需要先完成映射"></i>
                                    </button>
                                </li>
                                <!-- 第二阶段tab -->
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link"
                                            :class="currentStage === 'stage2' ? 'active' : ''"
                                            data-bs-toggle="tab"
                                            data-bs-target="#stage2-tab"
                                            type="button"
                                            role="tab"
                                            :disabled="!canEnterStage2()"
                                            :class="canEnterStage2() ? (currentStage === 'stage2' ? 'active' : '') : 'disabled'">
                                        <i class="fas fa-database me-1"></i>
                                        第二阶段对比
                                        <span class="badge bg-warning ms-1" x-text="stage2Changes.length" x-show="stage2Changes.length > 0"></span>
                                        <i class="fas fa-check-circle text-success ms-1" x-show="stage2Confirmed" title="已确认"></i>
                                        <i class="fas fa-lock text-muted ms-1" x-show="!canEnterStage2()" title="需要先确认第一阶段"></i>
                                    </button>
                                </li>
                            </ul>
                        </div>
                        <div class="card-body">
                            <div class="tab-content">
                                <!-- 连线映射配置 -->
                                <div class="tab-pane fade"
                                     :class="currentStage === 'mapping' ? 'show active' : ''"
                                     id="mapping-tab"
                                     role="tabpanel"
                                     x-show="listModules.length > 0">
                                    <div class="mapping-header mb-3">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <h5 class="mb-0">
                                                <i class="fas fa-project-diagram text-info me-2"></i>
                                                数据连线映射配置
                                            </h5>
                                            <div class="mapping-actions">
                                                <span class="badge bg-info me-2" x-text="listModules.length + ' 个模块需要映射'"></span>
                                                <button class="btn btn-success btn-sm"
                                                        @click="completeMappingConfig()"
                                                        :disabled="!isMappingComplete()"
                                                        x-show="!mappingCompleted">
                                                    <i class="fas fa-check me-1"></i>
                                                    完成映射配置
                                                </button>
                                                <span class="badge bg-success" x-show="mappingCompleted">
                                                    <i class="fas fa-check-circle me-1"></i>
                                                    映射配置已完成
                                                </span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 映射说明 -->
                                    <div class="alert alert-info mb-4">
                                        <div class="d-flex align-items-start">
                                            <i class="fas fa-info-circle me-2 mt-1"></i>
                                            <div>
                                                <strong>连线映射说明：</strong>
                                                <p class="mb-1">系统检测到数据中包含列表类型的字段，需要手动建立对应关系以确保对比准确性。</p>
                                                <p class="mb-0 small">请为每个模块的列表项目建立连线关系，拖拽左侧项目到右侧对应项目即可建立连接。</p>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 模块选择器 -->
                                    <div class="module-selector mb-4" x-show="listModules.length > 1">
                                        <label class="form-label fw-bold">选择要配置的模块：</label>
                                        <div class="btn-group" role="group">
                                            <template x-for="module in listModules" :key="module.name">
                                                <button type="button"
                                                        class="btn btn-outline-primary"
                                                        :class="currentMappingModule === module.name ? 'active' : ''"
                                                        @click="setCurrentMappingModule(module.name)"
                                                        x-text="module.name">
                                                </button>
                                            </template>
                                        </div>
                                    </div>

                                    <!-- 映射配置区域 -->
                                    <div class="mapping-container" x-show="currentMappingModule">
                                        <template x-for="module in listModules.filter(m => m.name === currentMappingModule)" :key="module.name">
                                            <div class="mapping-module">
                                                <h6 class="module-title mb-3">
                                                    <i class="fas fa-folder me-1"></i>
                                                    <span x-text="module.name"></span>
                                                    <small class="text-muted ms-2">
                                                        (初次: <span x-text="module.initialData.length"></span>项,
                                                         随访: <span x-text="module.followupData.length"></span>项,
                                                         录入: <span x-text="module.finalData.length"></span>项)
                                                    </small>
                                                </h6>

                                                <!-- 第一阶段映射：初次 -> 随访 -->
                                                <div class="mapping-stage mb-4">
                                                    <h6 class="stage-title">
                                                        <i class="fas fa-arrow-right text-primary me-1"></i>
                                                        第一阶段映射：初次报告 → 随访报告
                                                    </h6>
                                                    <div class="mapping-area" id="mapping-stage1">
                                                        <div class="row">
                                                            <div class="col-md-5">
                                                                <div class="mapping-column source-column">
                                                                    <h6 class="column-title">初次报告</h6>
                                                                    <div class="items-container">
                                                                        <template x-for="(item, index) in module.initialData" :key="'initial-' + index">
                                                                            <div class="mapping-item source-item"
                                                                                 :data-index="index"
                                                                                 :id="'initial-' + index"
                                                                                 draggable="true"
                                                                                 @dragstart="startDrag($event, 'initial', index, module.name)">
                                                                                <div class="item-header">
                                                                                    <span class="item-number">#<span x-text="index + 1"></span></span>
                                                                                    <span class="item-type">源</span>
                                                                                </div>
                                                                                <div class="item-content" x-html="renderListItem(item, index, 'source')"></div>
                                                                            </div>
                                                                        </template>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-2">
                                                                <div class="mapping-lines">
                                                                    <svg class="connection-svg" width="100%" height="400">
                                                                        <!-- 连线将通过JavaScript动态绘制 -->
                                                                    </svg>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-5">
                                                                <div class="mapping-column target-column">
                                                                    <h6 class="column-title">随访报告</h6>
                                                                    <div class="items-container"
                                                                         @dragover.prevent
                                                                         @drop="handleDrop($event, 'followup', module.name, 1)">
                                                                        <template x-for="(item, index) in module.followupData" :key="'followup-' + index">
                                                                            <div class="mapping-item target-item"
                                                                                 :data-index="index"
                                                                                 :id="'followup-' + index">
                                                                                <div class="item-header">
                                                                                    <span class="item-number">#<span x-text="index + 1"></span></span>
                                                                                    <span class="item-type">目标</span>
                                                                                </div>
                                                                                <div class="item-content" x-html="renderListItem(item, index, 'target')"></div>
                                                                            </div>
                                                                        </template>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- 第二阶段映射：随访 -> 录入 -->
                                                <div class="mapping-stage">
                                                    <h6 class="stage-title">
                                                        <i class="fas fa-arrow-right text-warning me-1"></i>
                                                        第二阶段映射：随访报告 → 已录入数据
                                                    </h6>
                                                    <div class="mapping-area" id="mapping-stage2">
                                                        <div class="row">
                                                            <div class="col-md-5">
                                                                <div class="mapping-column source-column">
                                                                    <h6 class="column-title">随访报告</h6>
                                                                    <div class="items-container">
                                                                        <template x-for="(item, index) in module.followupData" :key="'followup2-' + index">
                                                                            <div class="mapping-item source-item"
                                                                                 :data-index="index"
                                                                                 :id="'followup2-' + index"
                                                                                 draggable="true"
                                                                                 @dragstart="startDrag($event, 'followup', index, module.name)">
                                                                                <div class="item-header">
                                                                                    <span class="item-number">#<span x-text="index + 1"></span></span>
                                                                                    <span class="item-type">源</span>
                                                                                </div>
                                                                                <div class="item-content" x-html="renderListItem(item, index, 'source')"></div>
                                                                            </div>
                                                                        </template>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-2">
                                                                <div class="mapping-lines">
                                                                    <svg class="connection-svg" width="100%" height="400">
                                                                        <!-- 连线将通过JavaScript动态绘制 -->
                                                                    </svg>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-5">
                                                                <div class="mapping-column target-column">
                                                                    <h6 class="column-title">已录入数据</h6>
                                                                    <div class="items-container"
                                                                         @dragover.prevent
                                                                         @drop="handleDrop($event, 'final', module.name, 2)">
                                                                        <template x-for="(item, index) in module.finalData" :key="'final-' + index">
                                                                            <div class="mapping-item target-item"
                                                                                 :data-index="index"
                                                                                 :id="'final-' + index">
                                                                                <div class="item-header">
                                                                                    <span class="item-number">#<span x-text="index + 1"></span></span>
                                                                                    <span class="item-type">目标</span>
                                                                                </div>
                                                                                <div class="item-content" x-html="renderListItem(item, index, 'target')"></div>
                                                                            </div>
                                                                        </template>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </template>
                                    </div>

                                    <!-- 无需映射提示 -->
                                    <div x-show="listModules.length === 0" class="text-center py-5">
                                        <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                                        <p class="text-muted">数据中没有需要映射的列表字段</p>
                                        <p class="small text-muted">可以直接进行数据对比</p>
                                    </div>
                                </div>

                                <!-- 第一阶段对比：初次报告 vs 随访报告 -->
                                <div class="tab-pane fade show active" id="stage1-tab" role="tabpanel">
                                    <div class="stage-header mb-3">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <h5 class="mb-0">
                                                <i class="fas fa-arrow-right text-primary me-2"></i>
                                                初次报告 → 随访报告
                                            </h5>
                                            <div class="stage-actions">
                                                <span class="badge bg-info me-2" x-text="stage1Changes.length + ' 项变更'"></span>
                                                <button class="btn btn-success btn-sm"
                                                        @click="confirmStage1()"
                                                        :disabled="stage1Confirmed || stage1Changes.filter(c => c.confirmed).length === 0"
                                                        x-show="!stage1Confirmed">
                                                    <i class="fas fa-check me-1"></i>
                                                    确认第一阶段
                                                </button>
                                                <span class="badge bg-success" x-show="stage1Confirmed">
                                                    <i class="fas fa-check-circle me-1"></i>
                                                    第一阶段已确认
                                                </span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 数据对比视图 -->
                                    <div class="row mb-4">
                                        <div class="col-md-6">
                                            <h6 class="text-primary">
                                                <i class="fas fa-file-medical me-1"></i>
                                                初次报告
                                            </h6>
                                            <div class="data-stage-container" x-html="renderDataStage(initialReport, 'initial')"></div>
                                        </div>
                                        <div class="col-md-6">
                                            <h6 class="text-warning">
                                                <i class="fas fa-file-medical-alt me-1"></i>
                                                随访报告
                                            </h6>
                                            <div class="data-stage-container" x-html="renderDataStage(followupReport, 'followup')"></div>
                                        </div>
                                    </div>

                                    <!-- 第一阶段变更列表 -->
                                    <div class="stage-changes">
                                        <h6 class="mb-3">
                                            <i class="fas fa-list-ul me-1"></i>
                                            第一阶段变更详情
                                        </h6>
                                        <div class="change-list-container">
                                            <template x-for="(moduleChanges, moduleName) in getChangesByModule(stage1Changes)" :key="'stage1-' + moduleName">
                                                <div class="module-changes-section mb-4">
                                                    <!-- 模块标题 -->
                                                    <div class="module-header card">
                                                        <div class="card-header bg-light">
                                                            <div class="d-flex justify-content-between align-items-center">
                                                                <h6 class="mb-0">
                                                                    <i class="fas fa-folder me-2"></i>
                                                                    <span x-text="moduleName"></span>
                                                                </h6>
                                                                <div class="d-flex align-items-center">
                                                                    <div class="module-stats me-3">
                                                                        <span class="badge bg-primary me-1" x-text="getModuleChangeStats(moduleChanges).total + ' 项'"></span>
                                                                        <span class="badge bg-success me-1" x-text="getModuleChangeStats(moduleChanges).byType.ADD + ' 新增'"></span>
                                                                        <span class="badge bg-warning text-dark me-1" x-text="getModuleChangeStats(moduleChanges).byType.MODIFY + ' 修改'"></span>
                                                                        <span class="badge bg-danger" x-text="getModuleChangeStats(moduleChanges).byType.DELETE + ' 删除'"></span>
                                                                    </div>
                                                                    <div class="module-actions">
                                                                        <button class="btn btn-sm btn-outline-success me-1"
                                                                                @click="confirmAllModuleChanges(moduleChanges)"
                                                                                title="确认所有变更">
                                                                            <i class="fas fa-check-double"></i>
                                                                        </button>
                                                                        <button class="btn btn-sm btn-outline-secondary me-1"
                                                                                @click="cancelAllModuleChanges(moduleChanges)"
                                                                                title="取消所有确认">
                                                                            <i class="fas fa-times"></i>
                                                                        </button>
                                                                        <button class="btn btn-sm btn-outline-primary"
                                                                                @click="toggleAllModuleChanges(moduleChanges)"
                                                                                title="切换所有状态">
                                                                            <i class="fas fa-exchange-alt"></i>
                                                                        </button>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="mt-2">
                                                                <div class="progress" style="height: 4px;">
                                                                    <div class="progress-bar bg-success"
                                                                         :style="'width: ' + (getModuleChangeStats(moduleChanges).confirmed / getModuleChangeStats(moduleChanges).total * 100) + '%'"
                                                                         x-text="getModuleChangeStats(moduleChanges).confirmed + '/' + getModuleChangeStats(moduleChanges).total + ' 已确认'"></div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <!-- 模块变更列表 -->
                                                    <div class="module-changes-list">
                                                        <template x-for="change in moduleChanges" :key="change.id">
                                                            <div class="change-item card mb-2" :class="getChangeItemClass(change)">
                                                                <div class="card-body py-2">
                                                                    <div class="d-flex justify-content-between align-items-center">
                                                                        <div class="flex-grow-1">
                                                                            <div class="d-flex align-items-center mb-1">
                                                                                <span class="badge me-2" :class="getChangeTypeBadgeClass(change.type)" x-text="getChangeTypeText(change.type)"></span>
                                                                                <strong class="field-path" x-text="formatDisplayPath(change.path, moduleName)"></strong>
                                                                            </div>
                                                                            <div class="small text-muted">
                                                                                <div x-show="shouldShowOldValue(change)" class="mb-1">
                                                                                    <span class="text-danger">原值:</span>
                                                                                    <span class="change-value bg-light px-2 py-1 rounded" x-html="formatChangeValue(change.oldValue)"></span>
                                                                                </div>
                                                                                <div x-show="shouldShowNewValue(change)">
                                                                                    <span class="text-success">新值:</span>
                                                                                    <span class="change-value bg-light px-2 py-1 rounded" x-html="formatChangeValue(change.newValue)"></span>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                        <div class="change-actions">
                                                                            <button class="btn btn-sm"
                                                                                    :class="change.confirmed ? 'btn-success' : 'btn-outline-secondary'"
                                                                                    @click="toggleChangeConfirmation(change)"
                                                                                    :title="change.confirmed ? '取消确认' : '确认变更'">
                                                                                <i :class="change.confirmed ? 'fas fa-check' : 'far fa-square'"></i>
                                                                            </button>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </template>
                                                    </div>
                                                </div>
                                            </template>

                                            <!-- 无变更提示 -->
                                            <div x-show="stage1Changes.length === 0" class="text-center py-5">
                                                <i class="fas fa-info-circle text-muted fa-3x mb-3"></i>
                                                <p class="text-muted">第一阶段暂无变更数据</p>
                                                <p class="small text-muted">初次报告与随访报告内容一致</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 第二阶段对比：随访报告 vs 已录入数据 -->
                                <div class="tab-pane fade" id="stage2-tab" role="tabpanel">
                                    <div class="stage-header mb-3">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <h5 class="mb-0">
                                                <i class="fas fa-arrow-right text-warning me-2"></i>
                                                随访报告 → 已录入数据
                                            </h5>
                                            <div class="stage-actions">
                                                <span class="badge bg-info me-2" x-text="stage2Changes.length + ' 项变更'"></span>
                                                <button class="btn btn-success btn-sm"
                                                        @click="confirmStage2()"
                                                        :disabled="stage2Confirmed || stage2Changes.filter(c => c.confirmed).length === 0"
                                                        x-show="!stage2Confirmed">
                                                    <i class="fas fa-check me-1"></i>
                                                    确认第二阶段
                                                </button>
                                                <span class="badge bg-success" x-show="stage2Confirmed">
                                                    <i class="fas fa-check-circle me-1"></i>
                                                    第二阶段已确认
                                                </span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 阶段锁定提示 -->
                                    <div x-show="!canEnterStage2()" class="alert alert-warning mb-4">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-lock me-2"></i>
                                            <div>
                                                <strong>第二阶段已锁定</strong>
                                                <p class="mb-0 small">请先在第一阶段确认变更后才能进入第二阶段对比</p>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 数据对比视图 -->
                                    <div class="row mb-4" x-show="canEnterStage2()">
                                        <div class="col-md-6">
                                            <h6 class="text-warning">
                                                <i class="fas fa-file-medical-alt me-1"></i>
                                                随访报告
                                            </h6>
                                            <div class="data-stage-container" x-html="renderDataStage(followupReport, 'followup')"></div>
                                        </div>
                                        <div class="col-md-6">
                                            <h6 class="text-success">
                                                <i class="fas fa-database me-1"></i>
                                                已录入数据
                                            </h6>
                                            <div class="data-stage-container" x-html="renderDataStage(finalData, 'final')"></div>
                                        </div>
                                    </div>

                                    <!-- 第二阶段变更列表 -->
                                    <div class="stage-changes" x-show="canEnterStage2()">
                                        <h6 class="mb-3">
                                            <i class="fas fa-list-ul me-1"></i>
                                            第二阶段变更详情
                                        </h6>
                                        <div class="change-list-container">
                                            <template x-for="(moduleChanges, moduleName) in getChangesByModule(stage2Changes)" :key="'stage2-' + moduleName">
                                                <div class="module-changes-section mb-4">
                                                    <!-- 模块标题 -->
                                                    <div class="module-header card">
                                                        <div class="card-header bg-light">
                                                            <div class="d-flex justify-content-between align-items-center">
                                                                <h6 class="mb-0">
                                                                    <i class="fas fa-folder me-2"></i>
                                                                    <span x-text="moduleName"></span>
                                                                </h6>
                                                                <div class="d-flex align-items-center">
                                                                    <div class="module-stats me-3">
                                                                        <span class="badge bg-primary me-1" x-text="getModuleChangeStats(moduleChanges).total + ' 项'"></span>
                                                                        <span class="badge bg-success me-1" x-text="getModuleChangeStats(moduleChanges).byType.ADD + ' 新增'"></span>
                                                                        <span class="badge bg-warning text-dark me-1" x-text="getModuleChangeStats(moduleChanges).byType.MODIFY + ' 修改'"></span>
                                                                        <span class="badge bg-danger" x-text="getModuleChangeStats(moduleChanges).byType.DELETE + ' 删除'"></span>
                                                                    </div>
                                                                    <div class="module-actions">
                                                                        <button class="btn btn-sm btn-outline-success me-1"
                                                                                @click="confirmAllModuleChanges(moduleChanges)"
                                                                                title="确认所有变更">
                                                                            <i class="fas fa-check-double"></i>
                                                                        </button>
                                                                        <button class="btn btn-sm btn-outline-secondary me-1"
                                                                                @click="cancelAllModuleChanges(moduleChanges)"
                                                                                title="取消所有确认">
                                                                            <i class="fas fa-times"></i>
                                                                        </button>
                                                                        <button class="btn btn-sm btn-outline-primary"
                                                                                @click="toggleAllModuleChanges(moduleChanges)"
                                                                                title="切换所有状态">
                                                                            <i class="fas fa-exchange-alt"></i>
                                                                        </button>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="mt-2">
                                                                <div class="progress" style="height: 4px;">
                                                                    <div class="progress-bar bg-success"
                                                                         :style="'width: ' + (getModuleChangeStats(moduleChanges).confirmed / getModuleChangeStats(moduleChanges).total * 100) + '%'"
                                                                         x-text="getModuleChangeStats(moduleChanges).confirmed + '/' + getModuleChangeStats(moduleChanges).total + ' 已确认'"></div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <!-- 模块变更列表 -->
                                                    <div class="module-changes-list">
                                                        <template x-for="change in moduleChanges" :key="change.id">
                                                            <div class="change-item card mb-2" :class="getChangeItemClass(change)">
                                                                <div class="card-body py-2">
                                                                    <div class="d-flex justify-content-between align-items-center">
                                                                        <div class="flex-grow-1">
                                                                            <div class="d-flex align-items-center mb-1">
                                                                                <span class="badge me-2" :class="getChangeTypeBadgeClass(change.type)" x-text="getChangeTypeText(change.type)"></span>
                                                                                <strong class="field-path" x-text="formatDisplayPath(change.path, moduleName)"></strong>
                                                                            </div>
                                                                            <div class="small text-muted">
                                                                                <div x-show="shouldShowOldValue(change)" class="mb-1">
                                                                                    <span class="text-danger">原值:</span>
                                                                                    <span class="change-value bg-light px-2 py-1 rounded" x-html="formatChangeValue(change.oldValue)"></span>
                                                                                </div>
                                                                                <div x-show="shouldShowNewValue(change)">
                                                                                    <span class="text-success">新值:</span>
                                                                                    <span class="change-value bg-light px-2 py-1 rounded" x-html="formatChangeValue(change.newValue)"></span>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                        <div class="change-actions">
                                                                            <button class="btn btn-sm"
                                                                                    :class="change.confirmed ? 'btn-success' : 'btn-outline-secondary'"
                                                                                    @click="toggleChangeConfirmation(change)"
                                                                                    :title="change.confirmed ? '取消确认' : '确认变更'">
                                                                                <i :class="change.confirmed ? 'fas fa-check' : 'far fa-square'"></i>
                                                                            </button>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </template>
                                                    </div>
                                                </div>
                                            </template>

                                            <!-- 无变更提示 -->
                                            <div x-show="stage2Changes.length === 0 && canEnterStage2()" class="text-center py-5">
                                                <i class="fas fa-info-circle text-muted fa-3x mb-3"></i>
                                                <p class="text-muted">第二阶段暂无变更数据</p>
                                                <p class="small text-muted">随访报告与已录入数据内容一致</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 右侧：控制面板 -->
                <div class="col-lg-2">
                    <!-- 阶段状态 -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <i class="fas fa-tasks me-1"></i>
                            阶段状态
                        </div>
                        <div class="card-body">
                            <!-- 第一阶段状态 -->
                            <div class="stage-status mb-3">
                                <div class="d-flex align-items-center justify-content-between mb-2">
                                    <span class="fw-bold">第一阶段</span>
                                    <i class="fas fa-check-circle text-success" x-show="stage1Confirmed" title="已确认"></i>
                                    <i class="fas fa-clock text-warning" x-show="!stage1Confirmed && currentStage === 'stage1'" title="进行中"></i>
                                </div>
                                <div class="small text-muted mb-1">初次 → 随访</div>
                                <div class="d-flex justify-content-between small">
                                    <span>变更数量:</span>
                                    <span class="fw-bold" x-text="stage1Changes.length"></span>
                                </div>
                                <div class="d-flex justify-content-between small">
                                    <span>已确认:</span>
                                    <span class="fw-bold" x-text="stage1Changes.filter(c => c.confirmed).length"></span>
                                </div>
                            </div>

                            <!-- 第二阶段状态 -->
                            <div class="stage-status">
                                <div class="d-flex align-items-center justify-content-between mb-2">
                                    <span class="fw-bold">第二阶段</span>
                                    <i class="fas fa-check-circle text-success" x-show="stage2Confirmed" title="已确认"></i>
                                    <i class="fas fa-clock text-warning" x-show="!stage2Confirmed && currentStage === 'stage2'" title="进行中"></i>
                                    <i class="fas fa-lock text-muted" x-show="!canEnterStage2()" title="已锁定"></i>
                                </div>
                                <div class="small text-muted mb-1">随访 → 录入</div>
                                <div class="d-flex justify-content-between small">
                                    <span>变更数量:</span>
                                    <span class="fw-bold" x-text="stage2Changes.length"></span>
                                </div>
                                <div class="d-flex justify-content-between small">
                                    <span>已确认:</span>
                                    <span class="fw-bold" x-text="stage2Changes.filter(c => c.confirmed).length"></span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 统计信息 -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <i class="fas fa-chart-pie me-1"></i>
                            变更统计
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-4">
                                    <div class="stat-item">
                                        <div class="stat-number text-success" x-text="getChangeCount('ADD')"></div>
                                        <div class="stat-label">新增</div>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="stat-item">
                                        <div class="stat-number text-warning" x-text="getChangeCount('MODIFY')"></div>
                                        <div class="stat-label">修改</div>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="stat-item">
                                        <div class="stat-number text-danger" x-text="getChangeCount('DELETE')"></div>
                                        <div class="stat-label">删除</div>
                                    </div>
                                </div>
                            </div>
                            <hr>
                            <div class="d-flex justify-content-between">
                                <span>总变更:</span>
                                <span class="fw-bold" x-text="changeList.length"></span>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span>已确认:</span>
                                <span class="fw-bold" x-text="getConfirmedChangeCount()"></span>
                            </div>
                        </div>
                    </div>

                    <!-- 配置面板 -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <i class="fas fa-cog me-1"></i>
                            配置设置
                        </div>
                        <div class="card-body">
                            <button class="btn btn-outline-primary btn-sm w-100 mb-2 position-relative" @click="showConfigModal()">
                                <i class="fas fa-key me-1"></i>
                                主键配置
                                <span x-show="getConfigErrorCount() > 0"
                                      class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger"
                                      :title="'发现 ' + getConfigErrorCount() + ' 个配置错误'"
                                      x-text="getConfigErrorCount()">
                                </span>
                            </button>
                            <button class="btn btn-outline-secondary btn-sm w-100" @click="recompareData()">
                                <i class="fas fa-sync-alt me-1"></i>
                                重新对比
                            </button>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="card">
                        <div class="card-header">
                            <i class="fas fa-play me-1"></i>
                            操作
                        </div>
                        <div class="card-body">
                            <!-- 自动录入按钮 -->
                            <button class="btn btn-success w-100 mb-2"
                                    @click="autoSubmit()"
                                    :disabled="!canAutoSubmit()"
                                    :title="canAutoSubmit() ? '录入第二阶段已确认的变更' : '需要完成两个阶段的确认'">
                                <i class="fas fa-upload me-1"></i>
                                自动录入
                                <span x-show="stage2Confirmed" x-text="'(' + stage2Changes.filter(c => c.confirmed).length + '项)'"></span>
                            </button>

                            <!-- 阶段控制按钮 -->
                            <div class="btn-group w-100 mb-2" role="group">
                                <button class="btn btn-outline-primary btn-sm"
                                        @click="confirmStage1()"
                                        :disabled="stage1Confirmed || stage1Changes.filter(c => c.confirmed).length === 0"
                                        x-show="!stage1Confirmed"
                                        title="确认第一阶段变更">
                                    <i class="fas fa-check me-1"></i>
                                    确认阶段1
                                </button>
                                <button class="btn btn-outline-warning btn-sm"
                                        @click="confirmStage2()"
                                        :disabled="stage2Confirmed || stage2Changes.filter(c => c.confirmed).length === 0 || !canEnterStage2()"
                                        x-show="canEnterStage2() && !stage2Confirmed"
                                        title="确认第二阶段变更">
                                    <i class="fas fa-check me-1"></i>
                                    确认阶段2
                                </button>
                            </div>

                            <!-- 重置按钮 -->
                            <button class="btn btn-outline-secondary w-100 mb-2"
                                    @click="resetStages()"
                                    :disabled="!dataLoaded"
                                    title="重置所有阶段状态">
                                <i class="fas fa-redo me-1"></i>
                                重置阶段
                            </button>

                            <!-- 导出按钮 -->
                            <button class="btn btn-outline-info w-100" @click="exportChangeList()">
                                <i class="fas fa-download me-1"></i>
                                导出变更列表
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 配置模态框 -->
        <div class="modal fade" id="configModal" tabindex="-1" aria-labelledby="configModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="configModalLabel">主键配置</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <p class="text-muted">配置列表数据的主键字段，用于智能匹配数组项的变更。</p>

                        <!-- 错误摘要 -->
                        <div x-show="getConfigErrorCount() > 0" class="alert alert-warning mb-3">
                            <div class="d-flex align-items-center justify-content-between">
                                <div class="flex-grow-1">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                        <strong>发现 <span x-text="getConfigErrorCount()"></span> 个配置错误</strong>
                                    </div>
                                    <div class="small mt-2">
                                        <template x-for="error in getConfigErrors()" :key="error.module">
                                            <div class="text-danger d-flex align-items-center justify-content-between">
                                                <div>
                                                    <i class="fas fa-times-circle me-1"></i>
                                                    <strong x-text="error.module"></strong>: <span x-text="error.message"></span>
                                                </div>
                                                <button x-show="error.canAutoFix"
                                                        class="btn btn-sm btn-outline-primary ms-2"
                                                        @click="autoFixConfigError(error.module)"
                                                        title="自动修复此错误">
                                                    <i class="fas fa-magic"></i>
                                                </button>
                                            </div>
                                        </template>
                                    </div>
                                </div>
                                <div class="ms-3">
                                    <button class="btn btn-sm btn-primary"
                                            @click="autoFixAllErrors()"
                                            :disabled="getConfigErrors().filter(e => e.canAutoFix).length === 0"
                                            title="自动修复所有可修复的错误">
                                        <i class="fas fa-magic me-1"></i>
                                        一键修复
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 成功提示 -->
                        <div x-show="getConfigErrorCount() === 0 && dataLoaded" class="alert alert-success mb-3">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-check-circle me-2"></i>
                                <span>所有主键配置均有效</span>
                            </div>
                        </div>
                        <template x-for="(config, moduleName) in keyConfig" :key="moduleName">
                            <div class="mb-3">
                                <label class="form-label fw-bold" x-text="moduleName"></label>
                                <div class="input-group">
                                    <input type="text" class="form-control"
                                           :class="getConfigValidation(moduleName).valid ? '' : 'is-invalid'"
                                           :value="config.keyFields.join(', ')"
                                           @input="updateKeyConfig(moduleName, $event.target.value)"
                                           placeholder="输入主键字段，用逗号分隔">
                                    <button class="btn btn-outline-secondary" type="button" @click="resetKeyConfig(moduleName)" title="重置为默认配置">
                                        <i class="fas fa-undo"></i>
                                    </button>
                                    <button class="btn btn-outline-info" type="button"
                                            @click="analyzeModuleFields(moduleName)"
                                            title="分析字段建议"
                                            x-show="dataLoaded">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                                <div class="form-text">
                                    <span>当前配置: <code x-text="config.keyFields.join(', ') || '无'"></code></span>
                                    <span class="ms-3"
                                          :class="getConfigValidation(moduleName).valid ? 'text-success' : 'text-danger'"
                                          x-text="getConfigValidation(moduleName).message"></span>
                                </div>
                                <div class="form-text text-info" x-show="config.description" x-text="config.description"></div>

                                <!-- 字段建议 -->
                                <div x-show="moduleName === currentAnalyzingModule && fieldSuggestions.length > 0" class="mt-2">
                                    <small class="text-muted">字段建议:</small>
                                    <div class="d-flex flex-wrap gap-1 mt-1">
                                        <template x-for="field in fieldSuggestions" :key="field">
                                            <button type="button" class="btn btn-sm btn-outline-primary"
                                                    @click="addFieldToConfig(moduleName, field)"
                                                    x-text="field"></button>
                                        </template>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" @click="saveConfig()">保存配置</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript 库 -->
    <script src="../js/jquery.min.js"></script>
    <script src="../js/bootstrap.bundle.min.js"></script>
    <script src="./lib/<EMAIL>" defer></script>
    
    <!-- 应用脚本 -->
    <script src="js/data-compare.js"></script>
    <script src="js/key-generator.js"></script>
    <script src="js/followup-diff.js"></script>
</body>
</html>
