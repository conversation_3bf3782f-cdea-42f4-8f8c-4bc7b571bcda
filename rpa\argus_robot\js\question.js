/**
 * 页面加载完成后执行的初始化操作
 */
document.addEventListener("DOMContentLoaded", () => {
  console.log('质疑页面加载完毕');

  const questionTab = document.getElementById("question_tab");
  questionTab.addEventListener("click", (e) => {
    switchTab(0);
  });

  const closeBtn = document.getElementById("close-btn");
  closeBtn.addEventListener("click", () => closeFun("cancel"));

  const cancelBtn = document.getElementById("cancel-btn");
  cancelBtn.addEventListener("click", () => closeFun("cancel"));

  const okBtn = document.getElementById("ok-btn");
  okBtn.addEventListener("click", () => closeFun("ok"));

  window.addEventListener("message", (event) => {
    const message = event.data;
    if (message.action === "questionHistoryData") {
      const data = message.data;
      console.log(data, data.cn, data.en);
      
      const dataEn = data?.en;
      const dataCn = data?.cn;
      const question_tab_con = document.getElementById("question_tab_con");
      question_tab_con.innerHTML = `<div>
        <div><span>中文：</span><span>${dataCn || ""}</span></div>
        <div><span>英文：</span><span>${dataEn || ""}</span></div>
      </div>`;
    }
  });  
});

function closeFun(type) {
  window.parent.postMessage(
    {
      action: "questionHistory",
      type: type,
    },
    "*"
  ); 
}

function switchTab(index) {
  const tabItems = document.querySelectorAll(".tab-item");
  const tabPanes = document.querySelectorAll(".tab-pane");

  tabItems.forEach((item) => item.classList.remove("active"));
  tabPanes.forEach((pane) => pane.classList.remove("active"));

  tabItems[index].classList.add("active");
  tabPanes[index].classList.add("active");
}
