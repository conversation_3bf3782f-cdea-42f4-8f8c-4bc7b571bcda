# 病例随访数据对比工具

一个基于Alpine.js的病例随访数据对比工具，用于实现三阶段数据对比：初次报告 → 随访报告 → 已录入数据。

## 功能特性

### 核心功能
- ✅ **三阶段数据对比**：自动对比初次报告、随访报告和已录入数据
- ✅ **智能主键生成**：为列表数据自动生成主键配置，支持自定义
- ✅ **变更可视化**：清晰展示新增、修改、删除的数据项
- ✅ **人工干预**：支持手动确认/取消变更项
- ✅ **配置管理**：可自定义主键字段配置
- ✅ **数据导出**：支持导出变更列表为JSON格式

### 技术特性
- 🚀 **Alpine.js框架**：响应式数据绑定，轻量级前端框架
- 🎨 **Bootstrap样式**：现代化UI界面
- 📊 **智能算法**：深度对象比较和数组智能匹配
- 🔧 **可扩展性**：模块化设计，易于维护和扩展

## 文件结构

```
followup-diff/
├── index.html              # 主页面
├── css/
│   └── followup-diff.css   # 自定义样式
├── js/
│   ├── followup-diff.js    # 主应用逻辑
│   ├── data-compare.js     # 数据对比算法
│   └── key-generator.js    # 主键生成器
├── data/                   # 示例数据
│   ├── report-000.json     # 初次报告
│   ├── report-001.json     # 随访报告
│   └── report-002.json     # 已录入数据
├── server.js               # 开发服务器
└── README.md               # 说明文档
```

## 快速开始

### 1. 直接访问
在浏览器中打开 `index.html` 文件即可使用。

### 2. 使用开发服务器（推荐）
```bash
# 确保已安装Node.js和npm
cd followup-diff
npm install express cors
node server.js
```

然后访问：http://localhost:3001/followup-diff/index.html

## 使用说明

### 基本操作流程

1. **加载数据**
   - 点击"加载示例数据"按钮
   - 系统自动加载三个阶段的数据并执行对比

2. **查看对比结果**
   - **阶段对比**：并排显示三个阶段的数据
   - **变更详情**：列表显示所有变更项

3. **确认变更**
   - 在变更详情中勾选需要录入的变更项
   - 查看右侧统计信息

4. **配置主键**（可选）
   - 点击"主键配置"按钮
   - 自定义列表数据的主键字段

5. **自动录入**
   - 点击"自动录入"按钮
   - 系统将确认的变更提交到后端

### 主键配置说明

系统为常见的列表数据提供了默认主键配置：

- **SAE详细情况**：基于"不良事件名称"和"发生日期"
- **实验室检查**：基于"检查日期"和"检查项目名称"  
- **合并用药**：基于"药品名称"和"开始日期"
- **治疗用药**：基于"药品名称"和"开始日期"

您可以根据实际需求修改这些配置。

## 数据结构

### 输入数据格式
工具接受标准的JSON格式数据，包含多个模块：
```json
{
  "报告基础信息": { ... },
  "报告类型": { ... },
  "受试者信息": { ... },
  "SAE/ECI/AESI的详细情况": [ ... ],
  "合并用药信息": [ ... ]
}
```

### 变更列表格式
```json
{
  "id": "changelist_timestamp",
  "timestamp": "2025-01-07T10:00:00Z",
  "changes": [
    {
      "id": "change_1",
      "type": "MODIFY",
      "path": "报告类型.随访的次数",
      "oldValue": "",
      "newValue": "1",
      "stage": "initial_to_followup",
      "confirmed": true
    }
  ],
  "metadata": {
    "totalChanges": 15,
    "confirmedChanges": 12,
    "pendingChanges": 3
  }
}
```

## API接口

### 自动录入
```
POST /api/followup/auto-submit
Content-Type: application/json

{
  "changes": [...],
  "metadata": {...}
}
```

### 配置管理
```
GET /api/followup/config/load
POST /api/followup/config/save
```

## 自定义开发

### 添加新的对比规则
在 `data-compare.js` 中扩展 `DataComparer` 类：

```javascript
// 自定义对比逻辑
compareCustomField(val1, val2, path, stage) {
    // 实现自定义对比逻辑
}
```

### 修改UI界面
在 `followup-diff.js` 中修改Alpine.js组件：

```javascript
// 自定义渲染逻辑
renderCustomModule(data, stage) {
    // 实现自定义渲染
}
```

### 扩展主键生成规则
在 `key-generator.js` 中添加新的规则：

```javascript
// 添加新的优先级模式
const priorityPatterns = [
    /自定义模式$/,
    // ... 其他模式
];
```

## 浏览器兼容性

- Chrome 88+
- Firefox 85+
- Safari 14+
- Edge 88+

## 技术依赖

- **Alpine.js 3.x**：响应式框架
- **Bootstrap 5.x**：UI组件库
- **Font Awesome**：图标库
- **jQuery**：DOM操作（可选）

## 许可证

MIT License

## 更新日志

### v1.0.0 (2025-01-07)
- ✅ 初始版本发布
- ✅ 实现三阶段数据对比
- ✅ 智能主键生成
- ✅ 基础UI界面
- ✅ 配置管理功能

## 贡献指南

欢迎提交Issue和Pull Request来改进这个工具。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 创建GitHub Issue
- 发送邮件至开发团队
