<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Excel Data Processor</title>
    <link
      rel="stylesheet"
      href="https://cdnjs.loli.net/ajax/libs/twitter-bootstrap/5.3.1/css/bootstrap.min.css"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.loli.net/ajax/libs/tagify/4.17.9/tagify.css"
    />
    <style>
      .table-responsive {
        max-height: 400px;
      }
      #data-preview {
        position: relative;
        width: 10000px;
      }
      .tr {
        height: 60px;
      }
      .thead {
        position: sticky;
        top: 0; /* 固定表头 */
        z-index: 1; /* 保证表头在内容上方 */
        background-color: #eee;
      }
      .tbody{
        box-sizing: border-box;
      }
      .th,
      .td {
        width: 200px;
        height: 60px;
        display: inline-block;
        border: 1px solid #999999;
        padding: 3px 10px;
        box-sizing: border-box;
        max-width: 200px; /* Limit column width */
        word-break: break-all;
        text-overflow: ellipsis;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2; /* 这里是超出几行省略 */
        overflow: hidden;
      }
      .th:hover,
      .td:hover {
        background-color: #f8f9fa; /* Light background on hover */
        cursor: pointer;
      }
      /* Styling for dynamic filter inputs and buttons */
      .filter-container {
        margin-bottom: 10px;
      }
      .filter-input {
        margin-right: 10px;
        flex-grow: 1;
      }
      .filter-select {
        margin-right: 10px;
      }
      .filter-button {
        cursor: pointer;
      }
    </style>
  </head>
  <body>
    <div class="container mt-4">
      <h1 class="mb-4">Excel Data Processor</h1>
      <div class="mb-3">
        <label for="file-upload" class="form-label">Upload Excel File:</label>
        <input
          type="file"
          class="form-control"
          id="file-upload"
          accept=".xls, .xlsx"
        />
      </div>
      <div id="filters-container">
        <button class="btn btn-secondary mb-3" onclick="addFilter()">
          Add Filter
        </button>
      </div>
      <div class="mb-3">
        <input type="checkbox" id="remove-non-matching" checked />
        <label for="remove-non-matching"
          >Remove non-matching lines within cells</label
        >
      </div>
      <div class="mb-3">
        <button class="btn btn-primary" id="process-btn">Process Data</button>
        <button class="btn btn-success" id="download-btn" style="display: none">
          Download Filtered Data
        </button>
      </div>
      <div class="table-responsive">
        <div id="data-preview">
          <div class="thead"></div>
          <div class="tbody"></div>
        </div>
      </div>
    </div>

    <script src="https://cdnjs.loli.net/ajax/libs/twitter-bootstrap/5.3.1/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.loli.net/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script src="https://cdnjs.loli.net/ajax/libs/FileSaver.js/2.0.5/FileSaver.min.js"></script>
    <script src="https://cdnjs.loli.net/ajax/libs/tagify/4.17.9/tagify.min.js"></script>
    <script>
      document
        .getElementById("file-upload")
        .addEventListener("change", handleFileUpload);
      var workbook;
      let allData = [];
      let data = [];
      const rowHeight = 62;
      let totalRows = 0;
      const visibleRows = 10;
      let startIndex = 0;
      let endIndex = visibleRows;

      const tbody = document.querySelector("#data-preview .tbody");
      const container = document.querySelector(".table-responsive");
      
      container.addEventListener("scroll", () => { 
        const scrollTop = container.scrollTop;
        const newStartIndex = Math.floor(scrollTop / rowHeight);
        let newEndIndex = newStartIndex + visibleRows;  
        if(newStartIndex > totalRows) return;
		    if(newEndIndex > totalRows) newEndIndex = totalRows;
        if (newStartIndex !== startIndex || newEndIndex !== endIndex) {
          startIndex = newStartIndex;
          endIndex = newEndIndex;
          tbody.style.paddingTop = `${(startIndex) * rowHeight}px`;  
          generateItems(startIndex, endIndex);  
        }
      });

      function handleFileUpload(event) {
        const file = event.target.files[0];
        if (file) {
          const reader = new FileReader();
          reader.onload = function (e) {
            const file = e.target.result;
            workbook = XLSX.read(file, { type: "binary" });
            const sheetName = workbook.SheetNames[0];
            const sheet = workbook.Sheets[sheetName];
            allData = XLSX.utils.sheet_to_json(sheet, { header: 1 });
            
            data = [...allData];
            displayData(data);
          };
          reader.readAsBinaryString(file);
        }
      }

      function generateItems(start, end) {
        let html = "";
        for (let i = start; i < end; i++) {
          html += '<div class="tr">';
          const rowData = data[i + 1];
          if(!rowData) {
            html += '<div class="tr"></div>';
            continue;
          }
          for (let index = 0; index < rowData.length; index++) {
            const element = rowData[index];
            const title = element ? element : "&nbsp;";
            html += '<div class="td" title="' + title + '">' + title + "</div>";
          } 
          html += "</div>";
        }
        tbody.innerHTML = html;
      }

      function displayData(data) {
        totalRows = data.length;
        tbody.style.height = totalRows * rowHeight + "px";
        const thead = document.querySelector("#data-preview .thead");
        let tbody_innerHTML = "";
        let thead_innerHTML = "";

        if (data.length > 0) {
          thead_innerHTML += '<div class="tr">';
          data[0].forEach((header, index) => {
            thead_innerHTML +=
              '<div class="th" title="' + header + '">' + header + "</div>";
          });
          thead_innerHTML += "</div>";
          thead.innerHTML = thead_innerHTML;
        }
        startIndex = 0 
        endIndex = visibleRows
        generateItems(startIndex, endIndex);
      }

      function addFilter() {
        const filterDiv = document.createElement("div");
        filterDiv.className = "filter-container d-flex";

        const select = document.createElement("select");
        select.className = "form-control filter-select";
        const headers = data[0];
        headers.forEach((header, index) => {
          const option = document.createElement("option");
          option.value = index;
          option.textContent = header;
          select.appendChild(option);
        });

        const uniqueInputId =
          "tag-input-" + document.querySelectorAll(".filter-input").length;
        const input = document.createElement("input");
        input.id = uniqueInputId;
        input.className = "form-control filter-input";

        const removeBtn = document.createElement("button");
        removeBtn.textContent = "Remove";
        removeBtn.className = "btn btn-danger btn-sm filter-button";
        removeBtn.onclick = function () {
          filterDiv.remove();
        };

        filterDiv.appendChild(select);
        filterDiv.appendChild(input);
        filterDiv.appendChild(removeBtn);

        document.getElementById("filters-container").appendChild(filterDiv);

        // Initialize Tagify on the newly created input
        input.tagifyInstance = new Tagify(input);
        // Set up a delegated event listener on the document that checks if the paste is inside a Tagify control
        document.addEventListener("paste", function (e) {
          // Check if the event target is within a Tagify input container
          var tagifyInput = e.target.closest("tags.tagify");

          if (tagifyInput) {
            var inputElement = tagifyInput.previousElementSibling; // this should be the <input> element Tagify is attached to
            var tagifyInstance =
              inputElement.nextElementSibling.nextElementSibling.tagifyInstance; // Assuming the Tagify instance is stored here

            if (tagifyInstance) {
              e.preventDefault(); // Prevent the default paste action

              var clipboardData = e.clipboardData || window.clipboardData;
              var pastedData = clipboardData.getData("Text");

              // Clear existing input to avoid duplicating tags
              tagifyInstance.removeAllTags();

              // Split the pasted data by new lines and add each as a tag
              var tags = pastedData
                .split(/\r\n|\r|\n/)
                .filter((tag) => tag.trim() !== "");
              tags.forEach((tag) => {
                tagifyInstance.addTags([tag.trim()]); // Add each tag
                tagifyInstance.update(); // This ensures the internal state is updated
              });
            } else {
              console.error(
                "No Tagify instance found associated with the input element."
              );
            }
          }
        });
      }

      document
        .getElementById("process-btn")
        .addEventListener("click", processData, false);

      //document.getElementById('process-btn').addEventListener('click', processData);

      function processData() {
        const removeNonMatching = document.getElementById(
          "remove-non-matching"
        ).checked;
         
        const filters = Array.from(
          document.querySelectorAll(".filter-container")
        ).map((div) => {
          return {
            column: parseInt(div.querySelector("select").value, 10),
            filter: new Tagify(div.querySelector("input")).value.map(
              (item) => item.value
            ),
          };
        });

        let filteredRows = allData.slice(1).filter((row) =>
          filters.every((f) =>
            f.filter.some((filter) =>
              (row[f.column] ?? '').toString().includes(filter)
            )
          )
        );

        if (removeNonMatching) {
          filteredRows = filteredRows.map((row) => {
            const newRow = [...row];
            filters.forEach((filter) => {
              const cell = (newRow[filter.column] ?? '').toString();
              if (cell && cell.includes("\n")) {
                const lines = cell.split("\n");
                const filteredLines = lines.filter((line) =>
                  filter.filter.some((f) => line.includes(f))
                );
                cell = filteredLines.join("\n");
                newRow[filter.column] = cell;
              }
            });
            return newRow;
          });
        }

        data = [allData[0], ...filteredRows];
        startIndex = 0 
        totalRows = data.length;
        endIndex = totalRows
        displayData(data);

        document.getElementById("download-btn").style.display = "inline-block";
      }

      document
        .getElementById("download-btn")
        .addEventListener("click", function () {
          const filteredData = Array.from(
            document.querySelectorAll("#data-preview tbody tr")
          ).map((tr) => Array.from(tr.cells).map((td) => td.textContent));
          const headers = Array.from(
            document.querySelectorAll("#data-preview thead th")
          ).map((th) => th.textContent);

          const newSheet = XLSX.utils.aoa_to_sheet(data);
          const newWorkbook = XLSX.utils.book_new();
          XLSX.utils.book_append_sheet(newWorkbook, newSheet, "Filtered Data");
          XLSX.writeFile(newWorkbook, "filtered_data.xlsx");
        });
    </script>
  </body>
</html>
