<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="PV_TOOLS" content="true">
    <title>文件识别工具</title>
    <link href="../css/recognition.css" rel="stylesheet">
    <link href="../css/ckeditor5.css" rel="stylesheet">
    <link href="../css/bootstrap.min.css" rel="stylesheet">
    <link href="../css/dropzone.min.css" rel="stylesheet">
    <link href="../css/all.min.css" rel="stylesheet">
    <link href="../css/style.css" rel="stylesheet">
    <link href="../css/katex.min.css" rel="stylesheet">
    <link href="../css/jsoneditor.min.css" rel="stylesheet">
    <link href="../css/message.css" rel="stylesheet">
</head>
<body>
<div class="sidebar">
    <i class="fas fa-globe-europe" id="languageToggle" title="Switch Language"></i>
    <!-- 在现有侧边栏图标按钮列表中添加新按钮 -->
    <i class="fas fa-chart-pie" id="generateInsightView" title="Generate Insight View"></i>
    <i class="fas fa-edit" id="generateEventDescription" title="Generate Event Description"></i>
    <i class="fas fa-database" id="generateStructuredData" title="Generate Structured Data"></i>
    <i class="fas fa-sync-alt" id="secondaryTransform" title="Secondary Transformation"></i>
    <i class="fas fa-cogs" id="configButton" title="Configuration"></i>
    <i class="fas fa-redo" id="resetButton" title="Reset Data"></i>
    <!-- new export button -->
    <i class="fas fa-file-export" id="exportButton" title="Export Results"></i>
    <i class="fas fa-bug" id="reportBugButton" title="Report Bug"></i>
</div>
<div class="container">
    <div class="main-panel">
        <div class="info-section">
            <!-- 将信息卡片放入左侧的 info-section 中 -->
            <div class="info-card">
                <h3>环境信息</h3>
                <p><strong>租户环境:</strong> <span id="tenantEnv"></span></p>
                <p><strong>语言环境:</strong> <span id="languageEnv"></span></p>
            </div>
            <div class="info-card">
                <h3>租户信息</h3>
                <p><strong>租户名称:</strong> <span id="tenantName"></span></p>
                <p><strong>租户编号:</strong> <span id="tenantId"></span></p>
            </div>
            <div class="info-card">
                <h3>用户信息</h3>
                <p><strong>用户名:</strong> <span id="userName"></span></p>
                <p><strong>用户ID:</strong> <span id="userId"></span></p>
                <p><strong>用户角色:</strong> <span id="userRole"></span></p>
            </div>
            <div class="info-card">
                <h3>项目信息
                    <img id="gotoInsightView" style="cursor: pointer;transform: scale(5) translate(5px, 0px);display: none;" width="10" src="../img/360.gif" alt="Show InsightView" title="Show InsightView">
                </h3>
                <p><strong>方案编号:</strong> <span id="studyNum"></span></p>
                <p><strong>文件名称:</strong> <span id="fileName">未上传</span></p>
                <p><strong>文件MD5:</strong> 
                    <span id="fileMD5">未上传</span>
                </p>
            </div>
        </div>
        <div class="upload-section">
            <h2>文件上传</h2>
            <form action="/" class="dropzone" id="fileUpload"></form>
        </div>
    </div>
</div>
<div id="loading-spinner" class="loading-spinner">
    <div class="spinner-icon"></div>
    <div class="loading-info">
        <p id="currentNode">初始化中...</p>
        <p id="loadingMessage"></p> <!-- 添加的提示信息元素 -->
        <p id="startTime">开始时间: --:--:--</p>
        <p id="endTime">结束时间: --:--:--</p>
        <p id="totalTime">总耗时: --:--</p>
        <div id="progress-container">
            <progress id="loadingProgress" value="0" max="100"></progress>
            <p id="progressText">0%</p>
        </div>
    </div>
</div>

<!-- Modal for Event Description -->
<div class="modal fade" id="eventDescriptionModal" tabindex="-1" role="dialog" aria-labelledby="eventDescriptionModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="eventDescriptionModalLabel">事件描述</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="eventDescriptionContent" style="display: none;">处理中...</div>
                <textarea id="eventDescriptionTextarea" class="form-control hidden"></textarea>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" id="updateDescriptionBtn">更新</button>
                <button type="button" class="btn btn-primary" id="regenerateDescriptionBtn">重新生成</button>
            </div>
        </div>
    </div>
</div>
<div class="toast-container position-fixed bottom-0 end-0 p-3" style="z-index: 11">
    <div id="languageToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header">
            <strong class="me-auto">Notification</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
        <div class="toast-body">
            Language switched successfully to <span id="currentLanguage">English</span>!
        </div>
    </div>
</div>
<div class="modal fade" id="configModal" tabindex="-1" role="dialog" aria-labelledby="configModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <!-- 模态框头部 -->
            <div class="modal-header">
                <h5 class="modal-title" id="configModalLabel">配置</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <!-- 模态框主体 -->
            <div class="modal-body">
                <!-- 选项卡导航 -->
                <ul class="nav nav-tabs" id="configTab" role="tablist">
                    <li class="nav-item">
                        <a class="nav-link active" id="operationModeConfig-tab" data-bs-toggle="tab" href="#operationModeConfig" role="tab" aria-controls="operationModeConfig" aria-selected="true">Operation Mode Config</a>
                    </li>
                    <!-- 添加环境配置选项 -->
                    <li class="nav-item">
                        <a class="nav-link" id="environmentConfig-tab" data-bs-toggle="tab" href="#environmentConfig" role="tab" aria-controls="environmentConfig" aria-selected="false">Environment Config</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="promptsConfig-tab" data-bs-toggle="tab" href="#promptsConfig" role="tab" aria-controls="promptsConfig" aria-selected="false">提示词配置</a>
                    </li>
                    <!-- 在配置Modal的tab标题列表中新增一个tab -->
                    <li class="nav-item">
                        <a class="nav-link" id="drugDictionaryConfig-tab" data-bs-toggle="tab" href="#drugDictionaryConfig" role="tab" aria-controls="drugDictionaryConfig" aria-selected="false">知识库更新</a>
                    </li>
                </ul>
                <!-- 选项卡内容 -->
                <div class="tab-content" id="configTabContent">
                    <!-- 操作模式配置选项卡 -->
                    <div class="tab-pane fade show active" id="operationModeConfig" role="tabpanel" aria-labelledby="operationModeConfig-tab">
                        <div class="form-group">
                            <label for="operationModeSwitch">Operation Mode</label>
                            <select class="form-control" id="operationModeSwitch">
                                <option value="auto">普通用户</option>
                                <option value="manual">管理员</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="resultRenderFormat">Result Render Format</label>
                            <select class="form-control" id="resultRenderFormat">
                                <option value="markdown">Markdown</option>
                                <option value="mindmap">Mind Map</option>
                                <option value="tree">Tree</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="reportTypeSelect">Report Type</label>
                            <select class="form-control" id="reportTypeSelect">
                                <option value="initial">首次报告</option>
                                <option value="follow-up">随访报告</option>
                            </select>
                        </div>
                        <!-- 在此处添加“更新”按钮 -->
                        <button type="button" class="btn btn-primary mt-3" id="updateOperationModeBtn">操作模式更新</button>
                    </div>
                    <!-- 环境配置选项卡 -->
                    <div class="tab-pane fade" id="environmentConfig" role="tabpanel" aria-labelledby="environmentConfig-tab">
                        <div class="form-group">
                            <label for="environmentSelect">Select Environment</label>
                            <select class="form-control" id="environmentSelect">
                                <option value="dev">Development</option>
                                <option value="test">Test</option>
                                <option value="uat">UAT</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="devEnvInput">Development Environment URL</label>
                            <input type="text" class="form-control" id="devEnvInput" placeholder="Enter Development Environment URL">
                        </div>
                        <div class="form-group">
                            <label for="testEnvInput">Test Environment URL</label>
                            <input type="text" class="form-control" id="testEnvInput" placeholder="Enter Test Environment URL">
                        </div>
                        <div class="form-group">
                            <label for="uatEnvInput">UAT Environment URL</label>
                            <input type="text" class="form-control" id="uatEnvInput" placeholder="Enter UAT Environment URL">
                        </div>
                        <!-- 新增租户列表选择 -->
                        <div class="form-group">
                            <label for="tenantSelect">选择租户</label>
                            <select class="form-control" id="tenantSelect">
                                <!-- 选项将动态填充 -->
                            </select>
                        </div>
                        <!-- 在此处添加“更新”按钮 -->
                        <button type="button" class="btn btn-primary mt-3" id="updateEnvironmentBtn">环境更新</button>
                    </div>
                    <!-- 提示词配置选项卡 -->
                    <div class="tab-pane fade" id="promptsConfig" role="tabpanel" aria-labelledby="promptsConfig-tab">
                        <div class="prompts-wrapper">
                            <div class="prompts-navigation">
                                <!-- 左侧的提示词导航 -->
                            </div>
                            <div class="prompts-content">
                                <!-- 右侧的提示词具体内容 -->
                            </div>
                        </div>
                        <!-- 在此处添加“更新”按钮 -->
                        <button type="button" class="btn btn-primary mt-3" id="updatePromptsBtn">提示词更新</button>
                    </div>
                    <!-- 药品字典更新选项卡 -->
                    <!-- 药品字典更新选项卡 -->
                    <!-- 药品字典更新选项卡 -->
                    <div class="tab-pane fade" id="drugDictionaryConfig" role="tabpanel" aria-labelledby="drugDictionaryConfig-tab">
                        <div class="form-group">
                            <div class="row">
                                <div class="col-md-6">
                                    <label for="moduleSelect">知识库分类</label>
                                    <select class="form-control" id="moduleSelect">
                                        <!-- Options will be populated dynamically -->
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="placeholderSelect">报告模块</label>
                                    <select class="form-control" id="placeholderSelect">
                                        <!-- Options will be populated dynamically -->
                                    </select>
                                </div>
                            </div>
                        </div>
                        <!-- 删除了显示当前选择的模块和占位符的标题 -->
                        <!-- 删除了标签 <label>药品字典 Markdown 内容</label> -->
                        <div class="form-group">
                            <div id="drugDictionaryEditorContainer">
                                <p>正在加载数据...</p>
                            </div>
                        </div>
                        <button type="button" class="btn btn-primary mt-3" id="updateDrugDictionaryBtn">字典更新</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal for Export Results -->
<div class="modal fade" id="exportModal" tabindex="-1" role="dialog" aria-labelledby="exportModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exportModalLabel">导出结果</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <ul class="nav nav-tabs" id="exportTab" role="tablist">
                    <li class="nav-item">
                        <a class="nav-link active" id="imageResults-tab" data-bs-toggle="tab" href="#imageResults" role="tab" aria-controls="imageResults" aria-selected="true">
                            <i class="fas fa-recycle" title="清除缓存"></i>
                            图片识别结果</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="insightResults-tab" data-bs-toggle="tab" href="#insightResults" role="tab" aria-controls="insightResults" aria-selected="false">
                            <i class="fas fa-recycle" title="清除缓存"></i>
                            全息视图结果</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="narrativeResults-tab" data-bs-toggle="tab" href="#narrativeResults" role="tab" aria-controls="narrativeResults" aria-selected="false">
                            <i class="fas fa-recycle" title="清除缓存"></i>
                            事件描述结果</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="structuredDataResults-tab" data-bs-toggle="tab" href="#structuredDataResults" role="tab" aria-controls="structuredDataResults" aria-selected="false">
                            <i class="fas fa-recycle" title="清除缓存"></i>
                            结构化转换结果</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="transformedDataResults-tab" data-bs-toggle="tab" href="#transformedDataResults" role="tab" aria-controls="transformedDataResults" aria-selected="false">
                            <i class="fas fa-recycle" title="清除缓存"></i>
                            二次转换结果</a>
                    </li>
                </ul>
                <div class="tab-content" id="exportTabContent">
                    <div class="tab-pane fade show active" id="imageResults" role="tabpanel" aria-labelledby="imageResults-tab">
                        <textarea class="form-control" id="imageResultsText" rows="10" readonly></textarea>
                    </div>
                    <div class="tab-pane fade" id="insightResults" role="tabpanel" aria-labelledby="insightResults-tab">
                        <div class="form-control json-edit-wrap" id="insightResultsText"></div>
                    </div>
                    <div class="tab-pane fade" id="narrativeResults" role="tabpanel" aria-labelledby="narrativeResults-tab">
                        <textarea class="form-control" id="narrativeResultsText" rows="10" readonly></textarea>
                    </div>
                    <div class="tab-pane fade" id="structuredDataResults" role="tabpanel" aria-labelledby="structuredDataResults-tab">
                        <div class="form-control json-edit-wrap" id="structuredDataResultsText" ></div>
                    </div>
                    <div class="tab-pane fade" id="transformedDataResults" role="tabpanel" aria-labelledby="transformedDataResults-tab">
                        <div class="form-control json-edit-wrap" id="transformedDataResultsText"></div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<div class="toast-container position-fixed bottom-0 end-0 p-3" style="z-index: 11">
    <div id="errorModal" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header">
            <strong class="me-auto">Error</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
        <div class="toast-body" id="errorMessage">
            <!-- 错误信息会显示在这里 -->
        </div>
    </div>
</div>

<!-- 全局悬浮标签面板 -->
<!-- 全局悬浮标签面板 -->
<div id="global-label-panel" class="label-panel floating-panel">
    <!-- 选项卡导航 -->
    <ul class="tab-nav">
        <li class="tab-item active" data-tab="labeling-tab">数据标注</li>
        <li class="tab-item" data-tab="review-tab">数据复核</li>
    </ul>
    <!-- 选项卡内容 -->
    <div class="tab-content">
        <!-- 数据标注 Tab -->
        <div id="labeling-tab" class="tab-pane active">
            <div class="label-grid">
                <!-- 标签组 -->
                <div class="label-group">
                    <button class="label-button start-label">⬛ 相关邮件开始</button>
                    <button class="label-button end-label">🟡 相关邮件结束</button>
                </div>
                <div class="label-group">
                    <button class="label-button start-label">⬛ 事件描述开始</button>
                    <button class="label-button end-label">🟡 事件描述结束</button>
                </div>
                <div class="label-group">
                    <button class="label-button start-label">⬛ 相关检查开始</button>
                    <button class="label-button end-label">🟡 相关检查结束</button>
                </div>
                <div class="label-group">
                    <button class="label-button start-label">⬛ 相关病史开始</button>
                    <button class="label-button end-label">🟡 相关病史结束</button>
                </div>
                <div class="label-group">
                    <button class="label-button start-label">⬛ 相关用药开始</button>
                    <button class="label-button end-label">🟡 相关用药结束</button>
                </div>
            </div>
        </div>
        <!-- 数据复核 Tab -->
        <div id="review-tab" class="tab-pane">
            <!-- 键值对数据展示 -->
            <div id="key-value-data" class="key-value-data">
                <!-- JSON 数据将展示在这里 -->
            </div>
        </div>
    </div>
    <!-- 折叠按钮 -->
    <div id="label-panel-toggle" class="label-panel-toggle">
        <i class="fas fa-chevron-left"></i>
    </div>
</div>

<script src="../js/ckeditor5.umd.js"></script>
<script src="../js/Sortable.min.js"></script>
<script src="../js/dropzone.min.js"></script>
<script src="../js/bootstrap.bundle.min.js"></script>
<script src="../js/marked.min.js"></script>
<script src="../js/crypto-js.min.js"></script>
<script src="../js/markdown-it.min.js"></script>
<script src="../js/d3.min.js"></script>
<script src="../js/markmap-view-index.js"></script>
<script src="../js/markmap-toolbar-index.js"></script>
<script src="../js/jquery.min.js"></script>
<script src="../js/jsonpath.min.js"></script>
<script src="../js/jsoneditor.min.js"></script>
<script src="../js/thumbmark.umd.js"></script>
<script src="../js/message.js"></script>

<script> 
    document.write('<script src="../js/recognition.js?_v='+Date.now()+'"><\/script>');
</script>   
</body>
</html>
