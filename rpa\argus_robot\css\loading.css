.loading-spinner {
    display: none;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 300px; /* 增加宽度 */
    height: 370px; /* 高度根据内容自动调整 */
    max-height: 70vh; /* 设置最大高度，避免内容过多时撑破页面 */
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 9999;
    text-align: center;
    padding: 20px;
    border-radius: 10px;
    background-color: #716DCC; /* 初始背景颜色 */
    color: white;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
}

.spinner-icon {
    width: 60px;
    height: 60px;
    border: 8px solid #f3f3f3;
    border-top: 8px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

#loadingProgress {
    background-color: #716DCC;
}

.loading-info {
    margin-top: 20px;
    font-size: 14px; /* 调整字体大小 */
    line-height: 1.5; /* 调整行高以提高可读性 */
}

#currentNode {
    font-weight: bold; /* 使当前节点的名称更加显眼 */
    margin-bottom: 10px; /* 增加与其他文本的间距 */
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes flash {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}
