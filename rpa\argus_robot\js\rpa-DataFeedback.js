const {
  rpaClient,
} = await import(`./rpa-client.js?v=${window.__RPA_VERSION}`);  
 
const html2canvas = await import(`./html2canvas.esm.min.js?v=${window.__RPA_VERSION}`)  
await import(`./jszip.min.js?v=${window.__RPA_VERSION}`)    
const JSZip = window.JSZip;

function useXhr(cb){
  const originalXHR = window.XMLHttpRequest;
  window.XMLHttpRequest = function() {
    const xhr = new originalXHR();
    const originalSend = xhr.send;
    xhr.send = function(body) {       
      // 拦截响应
      xhr.addEventListener('readystatechange', function() {
        if (xhr.readyState === 4) { 
          cb({
            body,
            image: '',
            status: xhr.status,
            statusText: xhr.statusText,
            // responseType: xhr.responseType,
            // response: xhr.response,
            responseText: xhr.responseType === '' || xhr.responseType === 'text' ? xhr.responseText : '[无法直接访问]',
            // responseURL: xhr.responseURL,
            // getAllResponseHeaders: xhr.getAllResponseHeaders()
          })
        }
      });
      
      // 调用原始的 send 方法
      return originalSend.apply(this, arguments);
    };
    
    return xhr;
  };
  return () => {
    window.XMLHttpRequest = originalXHR;
  }
}
const wait = (n) => {  
  return new Promise((r) => setTimeout(r, n));
}

const waitXhr = (revs, count) => {
  return new Promise((r) => {
    const t = setInterval(() => {
      if (revs.length >= count) {
        clearInterval(t);
        r(revs);
      }
    }, 200);
  })
}


async function handleExport(e){ 
  e.stopImmediatePropagation();
  e.preventDefault(); 
  
  document.querySelector('#export').removeEventListener('click', handleExport, false);
  
  const revs = []
  const releaseXhr = useXhr((res) => {
    revs.push(res);
  });
   
  document.querySelector('.ant-btn.ant-btn-primary').click();
  await waitXhr(revs, 1); 
  await wait(200);
 
  revs[0].image = await domToImg('.ant-table.ant-table-default'); 

  const data = JSON.parse(revs[0].responseText); 

  const pages = Math.ceil(data.total / data.data.length);
  for(let i = 1; i < pages; i++){
    const page = i + 1;
    const pageBtn = document.querySelector(`.ant-pagination-item.ant-pagination-item-${page}`);
    if(!pageBtn){
      break;
    }
    pageBtn.click();
    await waitXhr(revs, (i + 1));    
    await wait(200);     
    revs[i].image = await domToImg('.ant-table.ant-table-default');
  }
  
  console.warn('revs :>> ', revs);
  releaseXhr();
  exportData(revs); 
}

function waitFormSubmit(){
  return new Promise((r) => {
    const originalSubmit = HTMLFormElement.prototype.submit;
    // 重写submit方法
    HTMLFormElement.prototype.submit = function() { 
      console.warn('表单submit方法被调用', this.id || this.name);
      
      // 获取表单数据
      const formData = new FormData(this);
       
      r({
        form: this,
        action: this.action,
        data: formData
      }) 
      HTMLFormElement.prototype.submit = originalSubmit;
    };

  })
}

function downloadBlob(blob, fileName) {
  // 创建一个指向Blob的URL
  const url = URL.createObjectURL(blob);
  
  // 创建一个a标签
  const a = document.createElement('a');
  a.href = url;
  a.download = fileName; // 设置下载的文件名
  
  // 将a标签添加到DOM中（不需要显示）
  document.body.appendChild(a);
  
  // 模拟点击a标签触发下载
  a.click();
  
  // 清理：移除a标签并释放URL对象
  setTimeout(() => {
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, 0);
}

async function exportData(revs){
  const data = [];
  const images = [];
  revs.forEach(rev => {
    const item = JSON.parse(rev.responseText);
    images.push(rev.image);
    data.push(...item.data);
  });

  // 合并图片
  const mergedImageUrl = await mergeImages(images);

  document.querySelector('#export button').click();
  
  const postData = await waitFormSubmit();
  console.warn('postData :>> ', postData); 

  // 创建zip文件
  const zip = new JSZip();

  // 添加合并后的图片到zip
  const imgResponse = await fetch(mergedImageUrl);
  const imgBlob = await imgResponse.blob();
  zip.file('screenshots.png', imgBlob); 

   
  // 添加导出的excel文件到zip
  const fileResponse = await fetch(postData.action,{
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
    },
    body: new URLSearchParams(Object.fromEntries(postData.data.entries())) 
  });

  const fileBlob = await fileResponse.blob();
  zip.file( postData.data.get('downName') , fileBlob);

  // 添加JSON数据到zip
  zip.file('data.json', JSON.stringify(data, null, 2));

  // 生成并下载zip文件
  const content = await zip.generateAsync({type: 'blob'});
  const now = new Date().toLocaleDateString().replace(/\//g, '-');
  downloadBlob(content, `export-${now}.zip`);

  
}

async function domToImg(selector) { 
  const canvas = await html2canvas.default(document.querySelector(selector))
  const url = canvas.toDataURL(`image/png`);  
  return url;
}

async function mergeImages(dataUrls) {  
  const df = document.createDocumentFragment();
  df.innerHTML = `<span style="margin-left:60px;font-size: 20px;color: #000;">截图日期：  ${(new Date()).toLocaleDateString()}</span>`;
  document.querySelector('.ant-pagination.ant-table-pagination').innerHTML = df.innerHTML
  
  const sign = await domToImg('.ant-table-pagination') 
  // 加载所有图片并获取尺寸
  const loadedImages = await Promise.all([...dataUrls,sign].map(url => {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => resolve(img);
      img.onerror = reject;
      img.src = url;
    });
  }));

  // 计算合并后画布的尺寸
  const maxWidth = Math.max(...loadedImages.map(img => img.width));
  const totalHeight = loadedImages.reduce((sum, img) => sum + img.height, 0);

  // 创建最终画布
  const canvas = document.createElement('canvas');
  canvas.width = maxWidth;
  canvas.height = totalHeight;
  const ctx = canvas.getContext('2d');

  // 在画布上垂直绘制图片
  let currentY = 0;
  loadedImages.forEach(img => {
    ctx.drawImage(img, 0, currentY);
    currentY += img.height;
  });

  // 返回合并后的dataURL
  return canvas.toDataURL('image/png');
}


window.addEventListener('hashchange', function() {
  init();
});

async function init() {
  // 获取当前 hash（不含 # 符号）
  const currentHash = window.location.hash.slice(1);
  if(currentHash.indexOf('EnterpriseDataFeedback') === -1){    
    exportBtn.removeEventListener('click', handleExport, false);
    return;
  }

  await wait(1000);
  const exportBtn = document.querySelector('#export');  
  console.warn('exportBtn :>> ', exportBtn);

  if(!exportBtn){
    return;
  }
  
  exportBtn.addEventListener('click', handleExport, false);
}
  
init();
 
  // releaseXhr(); 
// https://daers.adrs.org.cn/feedBackCenter/getExce1A11.do