/**
 * Message组件样式
 * 现代化的消息提示组件样式
 */

/* 消息容器 */
.message-container {
    position: fixed;
    pointer-events: none;
    z-index: 9999;
    max-width: 100%;
    box-sizing: border-box;
}

.message-container * {
    box-sizing: border-box;
}

/* 消息项 */
.message-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    padding: 12px 16px;
    min-width: 300px;
    max-width: 500px;
    background: #fff;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(0, 0, 0, 0.06);
    font-size: 14px;
    line-height: 1.4;
    color: #333;
    pointer-events: auto;
    transform: translateY(-100%);
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 显示状态 */
.message-item.message-show {
    transform: translateY(0);
    opacity: 1;
}

/* 隐藏状态 */
.message-item.message-hide {
    transform: translateY(-100%);
    opacity: 0;
    margin-bottom: 0;
    padding-top: 0;
    padding-bottom: 0;
}

/* 消息内容 */
.message-content {
    display: flex;
    align-items: center;
    width: 100%;
}

/* 消息图标 */
.message-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;
    margin-right: 8px;
    font-size: 14px;
    font-weight: bold;
    flex-shrink: 0;
}

/* 加载图标动画 */
.message-loading-icon {
    animation: message-loading-rotate 1s linear infinite;
}

@keyframes message-loading-rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* 消息文本 */
.message-text {
    flex: 1;
    word-wrap: break-word;
    word-break: break-word;
}

/* 关闭按钮 */
.message-close {
    margin-left: 12px;
    width: 16px;
    height: 16px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 18px;
    line-height: 1;
    color: #999;
    transition: color 0.2s ease;
    flex-shrink: 0;
    user-select: none;
}

.message-close:hover {
    color: #666;
}

/* 成功消息 */
.message-success {
    background: #f6ffed;
    border-color: #b7eb8f;
}

.message-success .message-icon {
    color: #52c41a;
}

.message-success .message-text {
    color: #135200;
}

/* 错误消息 */
.message-error {
    background: #fff2f0;
    border-color: #ffccc7;
}

.message-error .message-icon {
    color: #ff4d4f;
}

.message-error .message-text {
    color: #a8071a;
}

/* 警告消息 */
.message-warning {
    background: #fffbe6;
    border-color: #ffe58f;
}

.message-warning .message-icon {
    color: #faad14;
}

.message-warning .message-text {
    color: #ad6800;
}

/* 信息消息 */
.message-info {
    background: #e6f7ff;
    border-color: #91d5ff;
}

.message-info .message-icon {
    color: #1890ff;
}

.message-info .message-text {
    color: #003a8c;
}

/* 加载消息 */
.message-loading {
    background: #f0f0f0;
    border-color: #d9d9d9;
}

.message-loading .message-icon {
    color: #1890ff;
}

.message-loading .message-text {
    color: #333;
}

/* 暗色主题 */
@media (prefers-color-scheme: dark) {
    .message-item {
        background: #2f2f2f;
        color: #fff;
        border-color: rgba(255, 255, 255, 0.15);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
    }

    .message-close {
        color: #ccc;
    }

    .message-close:hover {
        color: #fff;
    }

    /* 暗色主题下的消息类型 */
    .message-success {
        background: #2d5a2d;
        border-color: #52c41a;
    }

    .message-success .message-text {
        color: #95de64;
    }

    .message-error {
        background: #5a2d2d;
        border-color: #ff4d4f;
    }

    .message-error .message-text {
        color: #ff7875;
    }

    .message-warning {
        background: #5a4d2d;
        border-color: #faad14;
    }

    .message-warning .message-text {
        color: #ffc53d;
    }

    .message-info {
        background: #2d4a5a;
        border-color: #1890ff;
    }

    .message-info .message-text {
        color: #69c0ff;
    }

    .message-loading {
        background: #4a4a4a;
        border-color: #666;
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .message-item {
        min-width: 280px;
        max-width: calc(100vw - 32px);
        margin-left: 16px;
        margin-right: 16px;
    }

    .message-container {
        left: 0 !important;
        right: 0 !important;
        transform: none !important;
    }

    .message-container.message-top {
        top: 16px;
    }

    .message-container.message-center {
        top: 50%;
        transform: translateY(-50%) !important;
    }

    .message-container.message-bottom {
        bottom: 16px;
    }

    .message-container.message-top-left,
    .message-container.message-top-right {
        top: 16px;
    }

    .message-container.message-bottom-left,
    .message-container.message-bottom-right {
        bottom: 16px;
    }
}

/* 小屏幕优化 */
@media (max-width: 480px) {
    .message-item {
        min-width: 260px;
        max-width: calc(100vw - 24px);
        margin-left: 12px;
        margin-right: 12px;
        padding: 10px 12px;
        font-size: 13px;
    }

    .message-icon {
        width: 14px;
        height: 14px;
        font-size: 12px;
        margin-right: 6px;
    }

    .message-close {
        width: 14px;
        height: 14px;
        font-size: 16px;
        margin-left: 8px;
    }

    .message-container {
        margin-left: 12px;
        margin-right: 12px;
    }

    .message-container.message-top,
    .message-container.message-top-left,
    .message-container.message-top-right {
        top: 12px;
    }

    .message-container.message-bottom,
    .message-container.message-bottom-left,
    .message-container.message-bottom-right {
        bottom: 12px;
    }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    .message-item {
        border-width: 2px;
    }

    .message-success {
        border-color: #52c41a;
    }

    .message-error {
        border-color: #ff4d4f;
    }

    .message-warning {
        border-color: #faad14;
    }

    .message-info {
        border-color: #1890ff;
    }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
    .message-item {
        transition: opacity 0.2s ease;
    }

    .message-loading-icon {
        animation: none;
    }
}

/* 打印样式 */
@media print {
    .message-container {
        display: none;
    }
}
