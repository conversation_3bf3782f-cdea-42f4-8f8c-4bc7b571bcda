<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ant Design 抽屉模拟 - 底部弹出</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
            margin: 0;
            padding: 0;
        }
        .drawer {
            height: 378px;
            background-color: #fff;
            box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.15);
        }
        .drawer-header {
            display: flex;
            align-items: center;
            padding: 16px 24px;
            color: rgba(0, 0, 0, 0.85);
            background: #fff;
            border-bottom: 1px solid #f0f0f0;
        }
        .drawer-close {
            font-size: 16px;
            line-height: 1;
            color: rgba(0, 0, 0, 0.45);
            cursor: pointer;
            margin-right: 12px;
        }
        .drawer-title {
            flex: 1;
            margin: 0;
            color: rgba(0, 0, 0, 0.85);
            font-weight: 500;
            font-size: 16px;
            line-height: 22px;
        }
        .drawer-content {
            padding: 24px;
        }
        .drawer-header button {
            margin-left: 8px;
            padding: 4px 15px;
            font-size: 14px;
            border-radius: 2px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
        }
        .btn-default {
            color: rgba(0, 0, 0, 0.65);
            background-color: #fff;
            border: 1px solid #d9d9d9;
        }
        .btn-default:hover {
            color: #211D70;
            border-color: #211D70;
        }
        .btn-primary {
            color: #fff;
            background-color: #211D70;
            border: 1px solid #211D70;
        }
        .btn-primary:hover {
            background-color: #211D70;
            border-color: #211D70;
        }
        .tabs-container {
            display: flex;
            border: 1px solid #f0f0f0;
            border-radius: 2px;
            height: 266px;
            overflow: auto;
        }
        .tabs-nav {
            width: 200px;
            background-color: #fafafa;
            border-right: 1px solid #f0f0f0;
        }
        .tab-item {
            padding: 12px 16px;
            cursor: pointer;
            transition: all 0.3s;
        }
        .tab-item:hover {
            color: #211D70;
        }
        .tab-item.active {
            color: #211D70;
            background-color: #fff;
            border-right: 2px solid #211D70;
        }
        .tab-content {
            flex-grow: 1;
            padding: 24px;
            height: 218px;
            overflow: auto;
        }
        .tab-pane {
            display: none;
        }
        .tab-pane.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="drawer">
        <div class="drawer-header">
            <span class="drawer-close" id="close-btn">X</span>
            <h3 class="drawer-title">报告编号: <span id="scheme-number"></span></h3>
            <button class="btn-default" id="cancel-btn">取消</button>
            <button class="btn-primary" id="ok-btn">确定</button>
        </div>
        <div class="drawer-content">
            <div class="tabs-container">
                <div class="tabs-nav">
                    <div class="tab-item active" id="question_tab">质疑</div>
                    <div class="tab-item" id="other_tab">其他</div>
                </div>
                <div class="tab-content">
                    <div class="tab-pane active" id="question_tab_con">
                    </div>
                    <div class="tab-pane" id="other_tab_con" >
                        <h3>标签 2 内容</h3>
                        <p>这是标签 2 的内容区域。</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script> 
        document.write('<script src="../js/question.js?_v='+Date.now()+'"><\/script>');
    </script>   
</body>
</html>