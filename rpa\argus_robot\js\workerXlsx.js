self.importScripts('../js/xlsx.full.min.js');
          
self.onmessage = function(event) {
    const data = event.data;
    const workbook = XLSX.read(data, {type: 'binary'});
            const sheetName = workbook.SheetNames[0];
            const sheet = workbook.Sheets[sheetName];
            const json = XLSX.utils.sheet_to_json(sheet, {header: 1});
            self.postMessage({status: 'completed', data: json});
};