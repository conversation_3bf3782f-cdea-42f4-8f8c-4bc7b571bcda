 

async function justifyCloseDialog() {
  if (isDialog()) {
    if (
      getDialog()
        ?.getElementById("txtMessage")
        ?.innerHTML?.includes(
          "The following lab data(s) shall be removed as no results exist for them."
        )
    ) {
      triggerEvent(getDialog().getElementById("btnOkYes"), "click");
      await waitForPageReload(2000);
    } else {
      await cancelDialog();
    }
  }
}
function isDialog() {
  const mainDoc = document.getElementById(mainAppId)?.contentDocument;
  if (!mainDoc) return false;
  const dialogNode = mainDoc.querySelector(iframeDialog);
  const result = !!dialogNode;
  return result;
}

function getDialog() {
  const mainDoc = document.getElementById(mainAppId)?.contentDocument;
  if (!mainDoc) {
    return null;
  }
  const currentNodeList = mainDoc.querySelectorAll(iframeDialog);
  if (!currentNodeList || currentNodeList.length === 0) {
    return null;
  }
  for (const node of currentNodeList) {
    if (!node) {
      continue;
    }
    return node.contentDocument;
  }
  return null;
}

async function clickFun(path, title) {
  while (isDialog()) {
    let dom = await getElement(getDialog(title), path);
    if (dom) {
      triggerEvent(dom, "click");
      await waitForPageReload(200);
      dom = null;
      return;
    } else {
      break;
    }
  }
}
async function cancelDialog(reportLocale) {
  // 处理弹出窗口没有关闭
  await clickFun(
    "//button[@value='" + (reportLocale === "cn" ? "取消" : "Cancel") + "']"
  );

  // 处理弹出窗口没有关闭，科伦的中文whodrugcoding使用的是英文的控件，造成中英文的适配失效
  await clickFun("//button[@value='Cancel']");

  // 处理弹出窗口没有关闭
  await clickFun("//*[@id='btnNo']");

  // 处理弹出窗口没有关闭
  await clickFun("//*[@id='bt_cancel']");

  await clickFun("//*[@id='btnOkYes']");

  await clickFun("//*[@id='btn_cancel']");
  await clickFun("//*[@id='btnCancel']");
}
/**
 * 等待指定的 DOM 元素出现
 * @param {String} key - 元素的 ID 或 XPath 表达式
 * @param {Number} [timeout=3000] - 超时时间，默认为 3000 毫秒
 * @returns {Promise} - 返回一个 Promise，当元素出现时解析，当超时或出错时拒绝
 */
function waitForElement(key, timeout = 3000) {
  return new Promise((resolve, reject) => {
    const startTime = Date.now(); // 记录开始时间
    let element = null; // 用于存储找到的元素
    function checkElement() {
      // 获取元素
      element =
        key === mainFrameId ? getDocument() : getElement(getDocument(), key);
      if (element) {
        clearInterval(intervalId);
        resolve(true);
        element = null;
      } else if (Date.now() - startTime > timeout) {
        clearInterval(intervalId);
        reject(new Error(`Element of ${key} not found within timeout`));
      } else {
        // 如果未找到元素且未超时，则继续下一帧检查
        requestAnimationFrame(checkElement);
      }
    }
    // 使用 requestAnimationFrame 进行定时检查
    const intervalId = requestAnimationFrame(checkElement);
  });
}
function monitorNetworkRequest(timeout = 10) {
  return new Promise((resolve) => {
    let pendingRequests = 0; // 用于跟踪未完成的请求数量
    let timeoutId; // 用于存储超时定时器的 ID

    // 劫持 XMLHttpRequest 的 open 方法
    const originalXHROpen = XMLHttpRequest.prototype.open;
    XMLHttpRequest.prototype.open = function () {
      // 在请求状态改变时触发的事件监听器
      this.addEventListener(
        "readystatechange",
        function () {
          if (this.readyState === 4) {
            // 请求完成
            pendingRequests--; // 减少未完成请求计数
            checkAllRequestsComplete(); // 检查所有请求是否已完成
          }
        },
        false
      );
      pendingRequests++; // 增加未完成请求计数
      clearTimeout(timeoutId); // 清除超时定时器
      originalXHROpen.apply(this, arguments); // 调用原始的 open 方法
    };

    // 劫持 fetch 方法
    const originalFetch = window.fetch;
    window.fetch = function () {
      pendingRequests++;
      clearTimeout(timeoutId);
      return originalFetch
        .apply(this, arguments)
        .then((response) => {
          pendingRequests--;
          checkAllRequestsComplete();
          return response;
        })
        .catch((error) => {
          pendingRequests--;
          checkAllRequestsComplete();
          throw error;
        });
    };

    // 劫持 $.ajax 方法
    // const originalAjax = $.ajax;
    // $.ajax = function(options) {
    //     pendingRequests++;
    //     clearTimeout(timeoutId);

    //     return originalAjax(options)
    //         .then(response => {
    //             pendingRequests--;
    //             checkAllRequestsComplete();
    //             return response;
    //         })
    //         .catch(error => {
    //             pendingRequests--;
    //             checkAllRequestsComplete();
    //             throw error;
    //         });
    // };

    // 检查是否所有请求都已完成
    function checkAllRequestsComplete() {
      if (pendingRequests === 0) {
        clearTimeout(timeoutId);
        resolve(true);
      }
    }

    // 设置超时
    timeoutId = setTimeout(() => {
      if (pendingRequests === 0) {
        resolve(true);
      } else {
        reject(new Error("请求超时"));
      }
    }, timeout);
  });
}
async function waitForPageReload(waitTime, key = mainFrameId) {
  let loadingDom;
  let GloadingDom;
  try {
    const requestFinish = await monitorNetworkRequest(waitTime);
    // await wait(1000)
    loadingDom = getDocument()?.getElementById?.(Loading);
    GloadingDom = getDocument()?.getElementById?.(Gloading);
    if (loadingDom || GloadingDom) {
      const loadingStyle = window.getComputedStyle(loadingDom);
      const gloadingStyle = window.getComputedStyle(GloadingDom);

      if (
        loadingStyle?.display !== "none" ||
        gloadingStyle?.display !== "none"
      ) {
        await waitForPageReload(waitTime, Loading);
        return;
      }
      if (isDialog()) {
        let loadingContainer;
        try {
          loadingContainer = getDialog()?.getElementById?.("loadingContainer");
          if (loadingContainer) {
            const loadingMaskStyle = window.getComputedStyle(loadingContainer);
            if (loadingMaskStyle?.display === "block") {
              await waitForPageReload(waitTime);
              return;
            }
          }
        } finally {
          loadingContainer = null;
        }
      }
      await waitForElement(key, waitTime);
      if (requestFinish && performance.timing.responseEnd) {
        return;
      } else {
        await waitForPageReload(waitTime, Loading);
      }
    } else {
      await waitForPageReload(waitTime, key);
    }
  } catch (e) {
    console.log("error", e.message);
  } finally {
    loadingDom = null;
    GloadingDom = null;
  }
}

async function supplementaryInput(moduleKeys) {
  async function rightClickToDelete(element, deleteId) {
    const event = new MouseEvent("contextmenu", {
      bubbles: true,
      cancelable: true,
      view: window,
      button: 2, // 右键是 button 为 2
    });

    // 触发右键
    element.dispatchEvent(event);

    await waitForPageReload(1000);
    const deleteIds = [deleteId, "PopupMenu_prddelete", "PopupMenu_rptdelete"];
    let rightClickDeleteButton = deleteIds.reduce((btn, id) => btn || getElement(getDocument(), id), null);
    if (rightClickDeleteButton) {
      triggerEvent(rightClickDeleteButton, "click");
      await waitForPageReload(1000);
      rightClickDeleteButton = null;
      return true;
    }

    await waitForPageReload(1000);
    // 触发
    return false;
  }

  async function clearBtnAddFol() {
    function getList() {
      const Fol_Table = getDocument().getElementById("Fol_Table");
      if (!Fol_Table) {
        console.error("无法找到ID为 'Fol_Table' 的元素。");
        return;
      }
      // 获取所有数据行，排除表头
      return (
        Fol_Table.querySelectorAll(
          "tr[id^='Fol_Table_']:not([style*='display: none'])"
        ) || []
      );
    }
    const Fol_Table_Rows = getList();
    if (!Fol_Table_Rows?.length) return;

    // 遍历每一行
    for (let rowIndex = 0; rowIndex < Fol_Table_Rows?.length; rowIndex++) {
      const dom = getElement(
        getDocument(),
        `//*[@id='Fol_Table_${rowIndex}']`
      );
      triggerEvent(dom, "click");
      if (dom?.style?.display === "none") {
        triggerEvent(
          getElement(
            getDocument(),
            `//*[@id='${Fol_Table_Rows[rowIndex].id}']`
          ),
          "click"
        );
        getDocument().getElementById("Fol_Table")
          ?.setAttribute(
            "currentrownum",
            getElement(
              getDocument(),
              `//*[@id='${Fol_Table_Rows[rowIndex].id}']`
            )?.getAttribute("rowseqnum")
          );
      }
      triggerEvent(
        getElement(getDocument(), `//*[@id='btnDeleteFollowup']`),
        "click"
      );
      await waitForPageReload(1000);
      if (isDialog()) {
        triggerEvent(getElement(getDialog(), "//*[@id='btnOkYes']"), "click");
      }
      await waitForPageReload(1000);
    }
    if (getList()?.length) await clearBtnAddFol();
  }

  async function clearBtnAddClass() {
    function getList() {
      const Class_Table = getDocument().getElementById("Class_Table");
      if (!Class_Table) {
        console.error("无法找到ID为 'Class_Table' 的元素。");
        return;
      }
      // 获取所有数据行，排除表头
      return (
        Class_Table.querySelectorAll(
          "tr[id^='Class_Table_']:not([style*='display: none'])"
        ) || []
      );
    }
    const Class_Table_Rows = getList();

    if (!Class_Table_Rows?.length) return;

    // 遍历每一行
    for (let rowIndex = 0; rowIndex < Class_Table_Rows?.length; rowIndex++) {
      const dom = getElement(
        getDocument(),
        `//*[@id='Class_Table_${rowIndex}']`
      );
      triggerEvent(dom, "click");
      if (dom?.style?.display === "none") {
        triggerEvent(
          getElement(
            getDocument(),
            `//*[@id='${Class_Table_Rows[rowIndex].id}']`
          ),
          "click"
        );
        getDocument().getElementById("Class_Table")
          ?.setAttribute(
            "currentrownum",
            getElement(
              getDocument(),
              `//*[@id='${Class_Table_Rows[rowIndex].id}']`
            )?.getAttribute("rowseqnum")
          );
      }
      triggerEvent(
        getElement(getDocument(), `//*[@id='btnDeleteClass']`),
        "click"
      );
      await waitForPageReload(1000);
      if (isDialog()) {
        triggerEvent(getElement(getDialog(), "//*[@id='btnOkYes']"), "click");
      }
      await waitForPageReload(1000);
    }

    if (getList()?.length) await clearBtnAddClass();
  }

  async function clearReporter() {
    function getList() {
      const reportReporterTabs = getDocument().getElementById("tr_CF_REP");
      if (!reportReporterTabs) {
        console.error("无法找到ID为 'tr_CF_REP' 的元素。");
        return;
      }
      // 获取所有报告者名称列表
      const repNameLists = [];

      for (let element of reportReporterTabs.children) {
        if (
          !(
            element.getAttribute("title")?.includes("(New)") ||
            element.getAttribute("title")?.includes("(新建)")
          )
        ) {
          repNameLists.push(element);
        }
      }
      return repNameLists || [];
    }
    const repNameLists = getList();

    for (let item of repNameLists) {
      triggerEvent(getElement(getDocument(), `//*[@id='${item.id}']`), "click");
      await rightClickToDelete(
        getDocument().getElementById("Reporter_Data_Table"),
        "PopupMenu_rptdelete"
      );
    }
    // if (getList()?.length > 1) await clearReporter();
  }

  function clearField(element) {
    element.value = "";
    triggerEvent(element, "input");
    triggerEvent(element, "change");
    triggerEvent(element, "blur");
  }

  function clearPage(containerID) {
    if (!containerID) {
      return;
    }
    const allElements = getDocument()
      .getElementById(containerID)
      ?.getElementsByTagName("*");
    if (!allElements?.length) return;
    // 创建一个数组所有表单元素
    const formElements = [];

    // 遍历所有元素
    for (let i = 0; i < allElements?.length; i++) {
      const childElement = allElements[i];
      // 筛选存在tabindex属性，并且不是button或img的元素
      if (
        childElement.getAttribute("tabindex") &&
        !["BUTTON", "IMG"].includes(childElement.nodeName)
      ) {
        formElements.push(childElement);
      }
    }

    for (let j = 0; j < formElements?.length; j++) {
      const form = formElements[j];
      if (
        ["product_name", "TXT_IngredientTable_0_ingredient_id"].includes(
          form.id
        )
      )
        continue;
      if (form.type === "checkbox") {
        form.checked = "";
      } else if (form.type === "radio" || form.id.endsWith("_nfdiv")) {
        // radio或者nullflavor字段的后缀
        form.click();
      } else {
        form.value = "";
        triggerEvent(form, "input");
        triggerEvent(form, "change");
        triggerEvent(form, "blur");
      }
    }
  }

  async function clearRace() {
    function getList() {
      // 获取表格元素
      const Pat_Race_Table = getDocument().querySelector("#Pat_Race_Table");
      if (!Pat_Race_Table) {
        console.error("无法找到ID为 'Pat_Race_Table' 的表格元素。");
        return;
      }

      // 获取所有数据行，排除表头
      return (
        Pat_Race_Table.querySelectorAll(
          "tr[id^='Pat_Race_Table_']:not([style*='display: none'])"
        ) || []
      );
    }
    const Pat_Race_TableRows = getList();
    if (!Pat_Race_TableRows?.length) return;

    // 遍历每一行
    for (let rowIndex = 0; rowIndex < Pat_Race_TableRows.length; rowIndex++) {
      const dom = getElement(
        getDocument(),
        `//*[@id='Pat_Race_Table_${rowIndex}']`
      );
      triggerEvent(dom, "click");
      if (dom?.style?.display === "none") {
        triggerEvent(
          getElement(
            getDocument(),
            `//*[@id='${Pat_Race_TableRows[rowIndex].id}']`
          ),
          "click"
        );
        getDocument().getElementById("Pat_Race_Table")
          ?.setAttribute(
            "currentrownum",
            getElement(
              getDocument(),
              `//*[@id='${Pat_Race_TableRows[rowIndex].id}']`
            )?.getAttribute("rowseqnum")
          );
      }
      triggerEvent(
        getElement(getDocument(), `//*[@id='pat_race_del']`),
        "click"
      );
      await waitForPageReload(1000);
      if (isDialog()) {
        triggerEvent(getElement(getDialog(), "//*[@id='btnOkYes']"), "click");
      }
      await waitForPageReload(1000);
    }
    if (!getList()?.length) await clearRace();
  }

  async function clearDeath() {
    function getList() {
      // 获取表格元素
      const CSDDTable = getDocument().querySelector("#CSDD");
      if (!CSDDTable) {
        console.error("无法找到ID为 'CSDD' 的表格元素。");
        return;
      }

      // 获取所有数据行，排除表头
      return (
        CSDDTable.querySelectorAll(
          "tr[id^='CSDD_']:not([style*='display: none'])"
        ) || []
      );
    }
    const CSDDRows = getList();
    if (!CSDDRows?.length) return;
    // 遍历每一行
    for (let rowIndex = 0; rowIndex < CSDDRows?.length; rowIndex++) {
      const dom = getElement(
        getDocument(),
        `//*[@id='CSDD_${rowIndex}']`
      );
      triggerEvent(dom, "click");
      if (dom?.style?.display === "none") {
        triggerEvent(
          getElement(
            getDocument(),
            `//*[@id='${CSDDRows[rowIndex].id}']`
          ),
          "click"
        );
        getDocument().getElementById("CSDD")
          ?.setAttribute(
            "currentrownum",
            getElement(
              getDocument(),
              `//*[@id='${CSDDRows[rowIndex].id}']`
            )?.getAttribute("rowseqnum")
          );
      }
      triggerEvent(getElement(getDocument(), `//*[@id='btnDelete']`), "click");
      await waitForPageReload(1000);
      if (isDialog()) {
        triggerEvent(getElement(getDialog(), "//*[@id='btnOkYes']"), "click");
      }
      await waitForPageReload(1000);
    }
    if (!getList()?.length) await clearDeath();
  }

  async function clearRelHist() {
    function getList() {
      // 获取表格元素
      const Rel_Hist_Table = getDocument().querySelector("#Rel_Hist_Table");
      if (!Rel_Hist_Table) {
        console.error("无法找到ID为 'Rel_Hist_Table' 的表格元素。");
        return;
      }

      // 获取所有数据行，排除表头
      return (
        Rel_Hist_Table.querySelectorAll(
          "tr[id^='Rel_Hist_Table_']:not([style*='display: none'])"
        ) || []
      );
    }
    const DeathRows = getList();
    if (!DeathRows?.length) return;
    // 遍历每一行
    for (let rowIndex = 0; rowIndex < DeathRows?.length; rowIndex++) {
      const dom = getElement(
        getDocument(),
        `//*[@id='Rel_Hist_Table_${rowIndex}']`
      );
      triggerEvent(dom, "click");
      if (dom?.style?.display === "none") {
        triggerEvent(
          getElement(
            getDocument(),
            `//*[@id='${DeathRows[rowIndex].id}']`
          ),
          "click"
        );
        getDocument().getElementById("Rel_Hist_Table")
          ?.setAttribute(
            "currentrownum",
            getElement(
              getDocument(),
              `//*[@id='${DeathRows[rowIndex].id}']`
            )?.getAttribute("rowseqnum")
          );
      }
      triggerEvent(
        getElement(getDocument(), `//*[@id='rel_hist_del']`),
        "click"
      );
      await waitForPageReload(1000);
      if (isDialog()) {
        triggerEvent(getElement(getDialog(), "//*[@id='btnOkYes']"), "click");
      }
      await waitForPageReload(1000);
    }
    if (!getList()?.length) await clearRelHist();
  }

  async function clearLabData() {
    function getList() {
      // 获取实验室数据模块的容器
      const labDataDiv = getDocument().getElementById(
        "Pat_LabData_Content_Div"
      );
      if (!labDataDiv) {
        console.error("无法找到ID为 'Pat_LabData_Content_Div' 的元素。");
        return;
      }

      // 获取实验室测试表格
      const labTestsTable = labDataDiv.querySelector("#labtests");
      if (!labTestsTable) {
        console.error("无法找到ID为 'labtests' 的表格元素。");
        return;
      }

      // 获取日期头部（examineDate）从crosstab表格
      const crosstabTable = labDataDiv.querySelector("#crosstab");
      if (!crosstabTable) {
        console.error("无法找到ID为 'crosstab' 的表格元素。");
        return;
      }

      // 获取所有日期列（examineDate）
      const dateHeaders = crosstabTable.querySelectorAll(
        "#labcontentheader input[name^='labdate_']:not([style*='display: none'])"
      );

      // 获取所有实验室测试行，排除表头
      return {
        labTestRows: labTestsTable.querySelectorAll(
          "tr[id^='labtest_']:not([style*='display: none'])"
        ),
        examineDates: Array.from(dateHeaders).map((input) =>
          input.value.trim()
        ),
      };
    }
    const { labTestRows, examineDates } = getList();

    if (!labTestRows?.length) return;

    for (let rowIndex = 0; rowIndex < labTestRows.length; rowIndex++) {
      const row = labTestRows[rowIndex];
      // 获取labtestreptd
      const labtestreptdElement = row.querySelector(
        "input[name='labtestreptd']:not([style*='display: none'])"
      );
      clearField(labtestreptdElement);

      // 获取labtest
      const labtestElement = row.querySelector(
        "input[name='labtest']:not([style*='display: none'])"
      );
      clearField(labtestElement);

      // 获取labtestlow和labtesthigh
      const labtestlowElement = row.querySelector(
        "input[name='labtestlow']:not([style*='display: none'])"
      );
      clearField(labtestlowElement);

      const labtesthighElement = row.querySelector(
        "input[name='labtesthigh']:not([style*='display: none'])"
      );
      clearField(labtesthighElement);

      // 获取TXT_labunit_X（只读字段）
      const txtLabunitElement = row.querySelector(
        `input[name='TXT_labunit_${rowIndex}']:not([style*='display: none'])`
      );
      clearField(txtLabunitElement);

      for (let dateIndex = 0; dateIndex < examineDates?.length; dateIndex++) {
        // 构建单元格ID，如 cell_0_0
        const cellId = `cell_${rowIndex}_${dateIndex}`;
        const cell = getDocument().getElementById(cellId);
        if (!cell) {
          console.warn(`无法找到ID为 '${cellId}' 的单元格。`);
          return;
        }

        await waitForPageReload(500);
        rightClickToDelete(cell, "PopupMenu_DivMenuDelete");
        await waitForPageReload(500);
      }
    }
    await waitForPageReload(2000);
  }

  async function clearInd() {
    function getList() {
      // 获取表格元素
      const Ind_Table = getDocument().querySelector("#Ind_Table");
      if (!Ind_Table) {
        console.error("无法找到ID为 'Ind_Table' 的表格元素。");
        return;
      }

      // 获取所有数据行，排除表头
      return Ind_Table.querySelectorAll(
        "tr[id^='Ind_Table_']:not([style*='display: none'])"
      );
    }
    const Ind_TableRows = getList();
    if (!Ind_TableRows?.length) return;
    // 遍历每一行
    for (let rowIndex = 0; rowIndex < Ind_TableRows.length; rowIndex++) {
      const dom = getElement(
        getDocument(),
        `//*[@id='Ind_Table_${rowIndex}']`
      );
      triggerEvent(dom, "click");
      if (dom?.style?.display === "none") {
        triggerEvent(
          getElement(
            getDocument(),
            `//*[@id='${Ind_TableRows[rowIndex].id}']`
          ),
          "click"
        );
        getDocument().getElementById("Ind_Table")
          ?.setAttribute(
            "currentrownum",
            getElement(
              getDocument(),
              `//*[@id='${Ind_TableRows[rowIndex].id}']`
            )?.getAttribute("rowseqnum")
          );
      }
      triggerEvent(
        getElement(getDocument(), `//*[@id='btnDeleteInd']`),
        "click"
      );
      await waitForPageReload(1000);
      if (isDialog()) {
        triggerEvent(getElement(getDialog(), "//*[@id='btnOkYes']"), "click");
      }
      await waitForPageReload(1000);
    }
    if (!getList()?.length) await clearInd();
  }

  async function clearRegimen() {
    function getList() {
      const RegimenTabs = getDocument().getElementById("tr_CF_DOSEREG");
      if (!RegimenTabs) {
        console.error("无法找到ID为 'tr_CF_DOSEREG' 的元素。");
        return;
      }
      // 获取所有报告者名称列表
      const RegimenLists = [];
      for (let element of RegimenTabs.children) {
        if (
          !(
            element.getAttribute("title")?.includes("(New)") ||
            element.getAttribute("title")?.includes("(新建)")
          )
        ) {
          RegimenLists.push(element);
        }
      }
      return RegimenLists;
    }
    const RegimenLists = getList();

    if (!RegimenLists?.length) return;
    for (let item of RegimenLists) {
      triggerEvent(getElement(getDocument(), `//*[@id='${item.id}']`), "click");
      await rightClickToDelete(
        getElement(getDocument(), "Regimen_Data_Table"),
        "PopupMenu_regdelete"
      );
      await waitForPageReload(1000);
    }
    if (!getList()?.length) await clearRegimen();
  }

  function getDeleteIdByProduct() {
     // 获取id为Event_Assess_Table的元素
     const eventAssessTable = getDocument().getElementById('PopupMenu');
 
     // 获取所有子元素
     const childElements = eventAssessTable?.getElementsByTagName('*');
 
     // 遍历所有子元素
     for (let i = 0; i < childElements.length; i++) {
         const childElement = childElements[i];
         // 如果元素的id包含Product_Row_，添加到数组中
         if (childElement.getAttribute('sectionname') === 'PRODUCT' && childElement.id?.includes('delete')) {
          return childElement.id
        }
     }
    
  }

  async function clearMedica(type) {
    const reportMedicationTabs = getDocument().getElementById("tr_CF_PRD");
    if (!reportMedicationTabs) {
      console.error("无法找到ID为 'tr_CF_PRD' 的元素。");
      return;
    }
    // 获取所有报告者名称列表
    const medicaLists = [];

    for (let element of reportMedicationTabs.children) {
      if (
        !(
          element.getAttribute("title")?.includes("(New)") ||
          element.getAttribute("title")?.includes("(新建)")
        )
      ) {
        medicaLists.push(element);
      }
    }

    if (!medicaLists.length) return;
    let index = 0;
    let actualIndex = 0;
    while (index < medicaLists.length) {
      actualIndex++;
      if (actualIndex > medicaLists.length) break;
      const item = medicaLists[index];
      if (item.title.includes("New") || item.title.includes("新建")) break;
      triggerEvent(getElement(getDocument(), item.id), "click");
      await waitForPageReload(3000);

      if (type === "试验用药") {
        if (getDocument().getElementById("drug_type_0").checked) {
          if (index === 0) {
            // 保留第一个，只清除内容
            clearPage("Product_Info_Content_Div");
            clearPage("Sub_DataDiv");
            await clearInd();
            clearPage("Product_Details_Data_Table");
            await clearRegimen();
            index++; // 移动到下一个
          } else {
            const deleteId = getDeleteIdByProduct()
            // 删除非第一个的试验用药
            await rightClickToDelete(
              getElement(getDocument(), "ProdInfo_Table"),
              deleteId
            );
            await waitForPageReload(1000);
            if (isDialog()) {
              triggerEvent(
                getElement(getDialog(), "//*[@id='btnOkYes']"),
                "click"
              );
            }
            await waitForPageReload(2000);
            continue; // 继续检查下一个，不增加index
          }
        } else {
          index++; // 如果不是试验用药，移动到下一个
        }
      } else if (type === "合并用药") {
        if (getDocument().getElementById("drug_type_1").checked) {
          const deleteId = getDeleteIdByProduct()
          await rightClickToDelete(
            getElement(getDocument(), "ProdInfo_Table"),
            deleteId
          );
          await waitForPageReload(1000);
          if (isDialog()) {
            triggerEvent(
              getElement(getDialog(), "//*[@id='btnOkYes']"),
              "click"
            );
          }
          await waitForPageReload(2000);
          continue; // 继续检查下一个，不增加index
        } else {
          index++; // 如果不是合并用药，移动到下一个
        }
      } else {
        index++;
      }
    }
  }

  async function clearEvent() {
    function getList() {
      const reportEventTabs = getDocument().getElementById("tr_CF_EVT");
      if (!reportEventTabs) {
        console.error("无法找到ID为 'tr_CF_PRD' 的元素。");
        return;
      }
      // 获取所有报告者名称列表
      const eventLists = [];

      for (let element of reportEventTabs.children) {
        if (
          !(
            element.getAttribute("title")?.includes("(New)") ||
            element.getAttribute("title")?.includes("(新建)")
          )
        ) {
          eventLists.push(element);
        }
      }
      return eventLists;
    }
    const eventLists = getList();
    if(eventLists.length < 1) return;
    let index = 0;
    let actualIndex = 0;
    while (index < eventLists.length) {
      actualIndex++;
      if (actualIndex > eventLists.length) break;
      item = eventLists[index]
      let count = 0;
      while(!getElement(getDocument(), `//*[@id='${item.id}']`)){
        await waitForPageReload(1000);
        count++;
        if(count > 15) break;
      }
      triggerEvent(getElement(getDocument(), `//*[@id='${item.id}']`), "click");      
      await waitForPageReload(1000);
      await rightClickToDelete(
        getElement(getDocument(), "Event_Info_Content_Div"),
        "PopupMenu_evtdelete"
      );
      await waitForPageReload(1000);
      if (isDialog()) {
        count = 0;
        while(document.getElementById(mainAppId).contentDocument?.getElementById("dialogTitle")?.textContent){
          count++;
          triggerEvent(getElement(getDialog(), "//*[@id='btnOkYes']"), "click");
          await waitForPageReload(1000);
          if(count >= 15) break;
        }
      }
    }

    clearPage("Event_Info_Content_Div");
    await waitForPageReload(1000);
    if (isDialog()) {
      triggerEvent(getDialog().getElementById("bt_cancel"), "click");
    }
    // if (getList()?.length > 1) await clearEvent();
  }

  async function clearTheContactLog() {
    function getList() {
      // 获取表格元素
      const TheContactLog = getDocument().querySelector("#TheContactLog");
      if (!TheContactLog) {
        console.error("无法找到ID为 'TheContactLog' 的表格元素。");
        return;
      }

      // 获取所有数据行，排除表头
      return (
        TheContactLog.querySelectorAll(
          "tr[id^='TheContactLog_']:not([style*='display: none'])"
        ) || []
      );
    }
    const TheContactLogRows = getList();
    if (!TheContactLogRows?.length) return;

    // 遍历每一行
    for (let rowIndex = 0; rowIndex < TheContactLogRows.length; rowIndex++) {
      const dom = getElement(
        getDocument(),
        `//*[@id='TheContactLog_${rowIndex}']`
      );
      triggerEvent(dom, "click");
      if (dom?.style?.display === "none") {
        triggerEvent(
          getElement(
            getDocument(),
            `//*[@id='${TheContactLogRows[rowIndex].id}']`
          ),
          "click"
        );
        getDocument().getElementById("TheContactLog")
          ?.setAttribute(
            "currentrownum",
            getElement(
              getDocument(),
              `//*[@id='${TheContactLogRows[rowIndex].id}']`
            )?.getAttribute("rowseqnum")
          );
      }
      triggerEvent(
        getElement(getDocument(), `//*[@id='CL_Delete']`),
        "click"
      );
      await waitForPageReload(1000);
      if (isDialog()) {
        triggerEvent(getElement(getDialog(), "//*[@id='btnOkYes']"), "click");
      }
      await waitForPageReload(1000);
    }
    if (!getList()?.length) await clearTheContactLog();
  }

  async function clearAdditionalInfo() {
    function getList() {
      const TableNotesAttach = getDocument().getElementById("TableNotesAttach");
      if (!TableNotesAttach) {
        console.error("无法找到ID为 'TableNotesAttach' 的元素。");
        return;
      }
      // 获取所有数据行，排除表头
      return TableNotesAttach.querySelectorAll(
        "tr[id^='TableNotesAttach_']:not([style*='display: none'])"
      );
    }
    const TableNotesAttachRows = getList();
    if (!TableNotesAttachRows.length) return;
    // 遍历每一行
    for (let rowIndex = 0; rowIndex < TableNotesAttachRows.length; rowIndex++) {
      const dom = getElement(
        getDocument(),
        `//*[@id='TableNotesAttach_${rowIndex}']`
      );
      triggerEvent(dom, "click");
      if (dom?.style?.display === "none") {
        triggerEvent(
          getElement(
            getDocument(),
            `//*[@id='${TableNotesAttachRows[rowIndex].id}']`
          ),
          "click"
        );
        getDocument().getElementById("TableNotesAttach")
          ?.setAttribute(
            "currentrownum",
            getElement(
              getDocument(),
              `//*[@id='${TableNotesAttachRows[rowIndex].id}']`
            )?.getAttribute("rowseqnum")
          );
      }
      triggerEvent(getElement(getDocument(), `//*[@id='NA_Delete']`), "click");
      await waitForPageReload(1000);
      if (isDialog()) {
        triggerEvent(getElement(getDialog(), "//*[@id='btnOkYes']"), "click");
      }
      await waitForPageReload(1000);
    }
    if (getList()?.length) await clearAdditionalInfo();
  }

  async function clearTableReference() {
    function getList() {
      const TableReference = getDocument().getElementById("TableReference");
      if (!TableReference) {
        console.error("无法找到ID为 'TableReference' 的元素。");
        return;
      }
      // 获取所有数据行，排除表头
      return TableReference.querySelectorAll(
        "tr[id^='TableReference_']:not([style*='display: none'])"
      );
    }

    if (isDialog()) {
      triggerEvent(getElement(getDialog(), "//*[@id='btnOkYes']"), "click");
    }
    await waitForPageReload(1000);

    if (!getElement(getDocument(), `//*[@id='REF_Delete']`)) {
      clearPage("TableReference");
    } else {
      const TableReferenceRows = getList();
      if (!TableReferenceRows.length) return;
      // 遍历每一行
      for (let rowIndex = 0; rowIndex < TableReferenceRows.length; rowIndex++) {
        const dom = getElement(
          getDocument(),
          `//*[@id='TableReference_${rowIndex}']`
        );
        triggerEvent(dom, "click");
        if (dom?.style?.display === "none") {
          triggerEvent(
            getElement(
              getDocument(),
              `//*[@id='${TableReferenceRows[rowIndex].id}']`
            ),
            "click"
          );
          getDocument().getElementById("TableReference")
            ?.setAttribute(
              "currentrownum",
              getElement(
                getDocument(),
                `//*[@id='${TableReferenceRows[rowIndex].id}']`
              )?.getAttribute("rowseqnum")
            );
        }
        triggerEvent(
          getElement(getDocument(), `//*[@id='REF_Delete']`),
          "click"
        );
        await waitForPageReload(1000);
        if (isDialog()) {
          triggerEvent(getElement(getDialog(), "//*[@id='btnOkYes']"), "click");
        }
        await waitForPageReload(1000);
      }
      if (getList()?.length) await clearTableReference();
    }
  }

  const execDataMap = new Map([
    [
      "btnAddClass",
      {
        path: [`//*[@id='td_CF_Tab_1']`],
        checkId: "Gen_Info_Content_Div",
        event: clearBtnAddClass,
      },
    ],
    [
      "btnAddFol",
      {
        path: [`//*[@id='td_CF_Tab_1']`],
        checkId: "Gen_Info_Content_Div",
        event: clearBtnAddFol,
      },
    ],
    [
      "reportReporterInfo",
      {
        path: [`//*[@id='td_CF_Tab_1']`],
        checkId: "Gen_Info_Content_Div",
        event: clearReporter,
      },
    ],
    [
      "btnAdd",
      {
        path: [`//*[@id='td_CF_Tab_2']`],
        checkId: "Pat_Info_Content_Div",
        event: clearDeath,
      },
    ],
    [
      "pat_race_add",
      {
        path: [`//*[@id='td_CF_Tab_2']`],
        checkId: "Pat_Info_Content_Div",
        event: clearRace,
      },
    ],
    [
      "rel_hist_add",
      {
        path: [`//*[@id='td_CF_Tab_2']`],
        checkId: "Pat_Info_Content_Div",
        event: clearRelHist,
      },
    ],
    [
      "refExamineTable",
      {
        path: [`//*[@id='td_CF_Tab_2']`],
        checkId: "Pat_Info_Content_Div",
        event: clearLabData,
      },
    ],
    [
      "试验用药",
      {
        path: [`//*[@id='td_CF_Tab_3']`],
        checkId: "div_tabs_CF_PRD",
        event: () => clearMedica("试验用药"),
      },
    ],
    [
      "合并用药",
      {
        path: [`//*[@id='td_CF_Tab_3']`],
        checkId: "div_tabs_CF_PRD",
        event: () => clearMedica("合并用药"),
      },
    ],
    [
      "reportSaeDetailInfo",
      {
        path: [`//*[@id='td_CF_Tab_4']`, `//*[@id='td_CF_EVENT_1']`],
        event: clearEvent,
        checkId: "div_tabs_CF_EVT",
        isAsync: true,
      },
    ],
    [
      "workLog",
      {
        path: [`//*[@id='td_CF_Tab_6']`],
        event: clearTheContactLog,
        checkId: "TheContactLog",
      },
    ],
    [
      "reportAttachment",
      {
        path: [`//*[@id='td_CF_Tab_7']`],
        event: clearAdditionalInfo,
        checkId: "TableNotesAttachDiv",
      },
    ],
    [
      "referenceInfo",
      {
        path: [`//*[@id='td_CF_Tab_7']`],
        event: clearTableReference,
        checkId: "TableNotesAttachDiv",
      },
    ],
  ]);
  // 从主流程传递过来需要删除的多记录标识
  // const moduleKeys = [
  //   "btnAddClass",
  //   "btnAddFol",
  //   "reportReporterInfo",
  //   "btnAdd",
  //   "pat_race_add",
  //   "rel_hist_add",
  //   "refExamineTable",
  //   "试验用药",
  //   "合并用药",
  //   "reportSaeDetailInfo",
  //   "reportAttachment",
  //   "referenceInfo",
  // ];

  console.log("moduleKeys", moduleKeys);

  // 从起始索引开始执行模块
  for (let i = 0; i < moduleKeys.length; i++) {
    const key = moduleKeys[i];
    const module = execDataMap.get(key);
    if (module) {
      let count = 0;
      while (!getElement(getDocument(), module.checkId)) {
        count++;
        for (let path of module.path) {
          triggerEvent(getElement(getDocument(), path), "click");
          await waitForPageReload(3000);
          await justifyCloseDialog();
        }
        if (count > 3) break;
      }
      while (isDialog()) await cancelDialog();
      await module.event();
    } else {
      console.log(`No found for module key "${key}".`);
    }
  }
}

window.addEventListener("message", async function (event) {
  if (event.data.type === "supplementaryInputModuleData") {
    console.log("补录执行");

    await supplementaryInput(event.data.data);
    triggerEvent(getElement(getDocument(), `//*[@id='td_CF_Tab_1']`), "click");
    await waitForPageReload(2000);
    await justifyCloseDialog();

    window.postMessage(
      { type: "supplementaryInputData" },
      window.location.origin
    );
  }
});
