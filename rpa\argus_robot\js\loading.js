// 全局状态管理对象，包含所有需要在不同函数间共享的变量
const AppState = {
  startTime: null,
  endTime: null,
  hasFinishedLoading: false,
  isLoading: false,
  hasError: false,
};

/**
 * 点击隐藏加载框的处理函数
 */
function hideSpinnerOnClick() {
  const spinner = document.getElementById("loading-spinner");
  if (AppState.hasFinishedLoading) {
    spinner.style.display = "none";
  }
}

/**
 * 更新当前操作节点的显示
 * @param {string} nodeName - 当前节点名称
 */
function updateCurrentNode(nodeName) {
  document.getElementById("currentNode").textContent = nodeName;
}

/**
 * 显示加载动画
 */
function showLoadingSpinner(loadingMessage) {  
  // 重置 AppState 中的相关状态信息
  resetAppState();

  AppState.hasError = false; // 重置错误标记

  // 设置加载开始时间
  AppState.startTime = new Date();

  // 重置并显示加载框的 UI
  resetLoadingUI();
  // 添加更新当前节点显示
  updateCurrentNode('正在' + loadingMessage + "中");

  updateLoadingUI(true, loadingMessage);

  // 设置提示信息
  document.getElementById("loadingMessage").textContent =
    loadingMessage + "中，请勿点击 loading 框";

  // 确保在加载过程中不能点击隐藏
  const spinner = document.getElementById("loading-spinner");
  spinner.removeEventListener("click", hideSpinnerOnClick); // 移除之前可能绑定的事件
  spinner.style.pointerEvents = "none"; // 禁用点击事件
}

/**
 * 隐藏加载动画
 */
function hideLoadingSpinner(loadingMessage, error) {
  // 设置加载结束时间
  AppState.endTime = new Date();

  // 添加更新当前节点显示
  updateCurrentNode(loadingMessage + (error ? error : '已结束'));

  updateLoadingUI(false, loadingMessage);

  // 确保旋转动画已停止
  const spinnerIcon = document.querySelector("#loading-spinner .spinner-icon");
  spinnerIcon.classList.remove("rotate");

  // 重置提示信息（移除或注释掉这行代码）
  // document.getElementById('loadingMessage').textContent = '';

  // 加载完成后，允许通过点击隐藏加载框
  const spinner = document.getElementById("loading-spinner");
  spinner.style.pointerEvents = "auto"; // 允许点击
  spinner.addEventListener("click", () => {
    const currentNode = document.getElementById('currentNode')?.textContent;
    if(currentNode && !/^正在.*中$/.test(currentNode)){
      window.parent.postMessage({action: 'loadingClose' }, '*'); 
    }
  });
}

/**
 * 格式化时间为HH:MM:SS
 * @param {Date} date - 时间对象
 * @returns {string} - 格式化后的时间字符串
 */
function formatTime(date) {
  return date.toTimeString().split(" ")[0];
}

/**
 * 重置加载框的UI状态，清除上次的显示内容
 */
function resetLoadingUI() {
  const spinner = document.getElementById("loading-spinner");

  // 重置时间和进度条显示
  document.getElementById("startTime").textContent = "开始时间: --:--:--";
  document.getElementById("endTime").textContent = "结束时间: --:--:--";
  document.getElementById("totalTime").textContent = "总耗时: --";

  // 重置加载框的背景和动画状态
  spinner.style.backgroundColor = "#716DCC";
  spinner.style.display = "block";
  spinner.style.animation = "none";
}
/**
 * 更新加载动画的UI状态
 * @param {boolean} isLoading - 当前是否正在加载
 */
function updateLoadingUI(isLoading, loadingMessage) {
  const spinner = document.getElementById("loading-spinner");
  const spinnerIcon = document.querySelector("#loading-spinner .spinner-icon");
  const startTime = AppState.startTime
    ? formatTime(AppState.startTime)
    : "--:--:--";
  const endTime = AppState.endTime ? formatTime(AppState.endTime) : "--:--:--";
  const totalTime = AppState.endTime
    ? ((AppState.endTime - AppState.startTime) / 1000).toFixed(2)
    : "--";

  document.getElementById("startTime").textContent = "开始时间:" + startTime;
  document.getElementById("endTime").textContent = "结束时间:" + endTime;
  document.getElementById("totalTime").textContent =
    "总耗时:" + totalTime + "秒";

  if (isLoading) {
    // spinner.style.animation = "none";
    // spinnerIcon.style.animation = "spin 1s linear infinite";
    document.getElementById("loadingMessage").textContent =
      loadingMessage + "中，请勿点击 loading 框";
    spinner.style.pointerEvents = "none"; // 禁用点击事件
  } else {
    // spinnerIcon.style.animation = "none";
    // spinner.style.animation = "flash 1s linear infinite";
    document.getElementById("loadingMessage").textContent =
      loadingMessage + "完成，请确认结果后，点击 loading 框来结束当前流程";
    spinner.style.pointerEvents = "auto"; // 允许点击
  }

  spinner.style.display = "block";

  AppState.isLoading = isLoading;
  AppState.hasFinishedLoading = !isLoading;
}

/**
 * 重置 AppState 中的加载相关状态
 */
function resetAppState() {
  AppState.startTime = null;
  AppState.endTime = null;
  AppState.isLoading = false;
  AppState.hasFinishedLoading = false;
}

const TypeMap = new Map([
  ['create', '创建'],
  ['entry', '录入'],
  ['qc', 'QC'],
  ['suppAdmis', '补录']
])

// 接收报告切换后，方案编号更换
window.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'loadingTransform') {
    const data = event.data.data
    const spinner = document.getElementById("loading-spinner");
    if (data.data === "show") {
      showLoadingSpinner(TypeMap.get(data.type))
    }
    if (data.data === "hide") {
      hideLoadingSpinner(TypeMap.get(data.type))
    }
    if (data.data === "error") {      
      hideLoadingSpinner(TypeMap.get(data.type), data.error)
    }
    spinner.style.backgroundColor = data.background // 设置背景颜色
  }
}); 
 