/**
 * 简单的开发服务器
 * 用于测试病例随访数据对比工具
 */

const express = require('express');
const path = require('path');
const cors = require('cors');

const app = express();
const PORT = 3012;

// 中间件
app.use(cors());
app.use(express.json());
app.use(express.static('.'));

// 静态文件服务
app.use('/followup-diff', express.static(path.join(__dirname)));

// API路由
app.post('/api/followup/auto-submit', (req, res) => {
    console.log('收到自动录入请求:', req.body);
    
    // 模拟处理时间
    setTimeout(() => {
        res.json({
            success: true,
            message: '变更已成功录入',
            processedCount: req.body.changes?.length || 0,
            timestamp: new Date().toISOString()
        });
    }, 1000);
});

app.get('/api/followup/config/load', (req, res) => {
    // 返回默认配置
    res.json({
        success: true,
        config: {
            "SAE/ECI/AESI的详细情况": {
                keyFields: ["不良事件名称", "发生日期"],
                description: "基于不良事件名称和发生日期生成主键"
            },
            "与事件相关的实验室检查": {
                keyFields: ["检查日期", "检查项目名称"],
                description: "基于检查日期和检查项目名称生成主键"
            }
        }
    });
});

app.post('/api/followup/config/save', (req, res) => {
    console.log('保存配置:', req.body);
    res.json({
        success: true,
        message: '配置已保存'
    });
});

// 启动服务器
app.listen(PORT, () => {
    console.log(`开发服务器已启动: http://localhost:${PORT}`);
    console.log(`访问应用: http://localhost:${PORT}/followup-diff/index.html`);
});

module.exports = app;
