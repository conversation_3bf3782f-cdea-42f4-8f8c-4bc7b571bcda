const drug_select_code = `window.fn_DrugSelect = async (lClick) => {
    var ParameterArray = new Array();
    var strURL;
    var cnt;
    var bOverWrite;
    bOverWrite = false;

/*     if (glDisplayLang == cfCMN_LANG_JP) {
        if (document.all.drug_code_type_j[0].checked)
            ParameterArray[0] = document.form.drug_code_j.value;
        else
            ParameterArray[0] = "";
    }
    else { */
        ParameterArray[0] = document.form.who_drug_code.value;
    // }
    ParameterArray[1] = document.form.product_name.value;
    ParameterArray[2] = "false";

    // strURL = (glDisplayLang == cfCMN_LANG_JP ? "/Dictionary/AddJDrug.asp" : "/Dictionary/AddWHODrug.asp");
	strURL = "/Dictionary/AddWHODrug.asp";
    strURL += "?click=" + lClick;
    strURL += "&search_drug_code=" + fn_URLEncode(ParameterArray[0]);
    strURL += "&search_drug_description=" + fn_URLEncode(ParameterArray[1]);
    strURL += "&search_drug_ingredient=" + ParameterArray[2];
    strURL += "&DSPLYLNG=" + glDisplayLang;
    strURL += "&case_id=" + document.form.case_id.value;
    var sDialogStyle = { dialogHeight: "650", dialogWidth: "1000", resizable: true, scrollable: false };
    var result = await fn_OpenModalDialog(strURL, ParameterArray, sDialogStyle);
    if (result) {
        if ((document.form.co_drug_code.value == "Study Drug") || (document.form.product_id.value * 1 > 0)) {
            if (!await fn_prod_nullification(sTranCHANGEDTO + " " + result[1]))
                return;
        }
        if (((document.form.product_name.value == document.form.product_name_alt.value)
            && (document.form.generic_name.value == document.form.generic_name_alt.value))
            || (document.form.views_available.value != 7)
        ) {
            bOverWrite = true;
        }
        // when manually entering the product name and tabbing out, we need to auto populated japanese fields with the english data ONLY
        // when it's not encoded already with JDRUG dictionary
        if (((document.form.product_name.value != result.product_name) ||
            (document.form.generic_name.value != result.generic_name)) &&
            (document.form.drug_code_j.value == ""))
            bOverWrite = true;

        if (result.product_name_cn != "" && result.product_name_cn != null)
            await ModifyChineseLangData("product_name", 250, result.product_name_cn, 28);
        if (result.generic_name_cn != "" && result.generic_name_cn != null)
            await ModifyChineseLangData("generic_name", 2000, result.generic_name_cn, 28);

        if (glDisplayLang == cfCMN_LANG_JP) {
/*             document.form.product_id.value = -1;
            document.form.prod_lic_id.value = -1;
            document.form.product_name.value = result.product_name;
            document.form.generic_name.value = result.generic_name;
            if ((document.form.product_name_alt.value == "") || (document.form.product_name_alt.value == null) || (document.form.product_name_alt.value == sTranslationReqd)) {
                document.form.product_name_alt.value = result.english_name;
                document.form.prod_coded_alt.value = result.english_name;
            }
            document.form.drug_code_j.value = result.drug_code;
            document.form.drug_code_type_j[0].checked = true;
            document.form.drug_code_type_j[1].checked = false;
            document.form.drug_code_type_j[2].checked = false; */

			document.form.country_id.value = "";
            document.form.obtain_drug_country_id.value = document.form.country_id.value;
            document.form.drug_authorization_country_id.value = document.form.country_id.value;
            document.form.product_id.value = -1;
            document.form.prod_lic_id.value = -1;
            document.form.product_name.value = result.product_name_cn;
            document.form.generic_name.value = result.generic_name_cn;
            if ((((document.form.product_name_alt.value == '') || (document.form.product_name_alt.value == null) || (document.form.product_name_alt.value == sTranslationReqd)) &&
                ((document.form.generic_name_alt.value == '') || (document.form.generic_name_alt.value == null) || (document.form.generic_name_alt.value == sTranslationReqd))) ||
                (bOverWrite)) {
                document.form.product_name_alt.value = result.product_name;
                document.form.generic_name_alt.value = result.generic_name;
                document.form.prod_coded_alt.value = result.product_name;
                document.form.drug_code_j.value = "";
            }
            document.form.co_drug_code.value = "";
            document.form.who_drug_code.value = result.drug_code;
            document.form.who_med_prod_id.value = result.med_prod_id;
            document.form.manufacturer_id.value = result.manufacturer;
            fn_SetFormulation("");
            document.form.concentration.value = "";
            document.form.conc_units_id.value = "";
            document.form.not_coded.value = "0";
            await fn_set_ingredients(result.ingredient_id, "", "", "");
            document.form.auth_type.value = "";
            document.form.auth_number.value = "";
            document.form.mkt_auth_holder_id.value = "";
            document.form.views_available.value = 7;
            if (result.drug_code.length > 0) {
                if (CENTRAL_CODING_INTERFACE > "0")
                    document.form.prod_code_status.value = "2";
                else
                    document.form.prod_code_status.value = "1";
            }
            else
                document.form.prod_code_status.value = "0";
            document.form.prod_coded.value = result.product_name;

        }
        else {
            document.form.country_id.value = "";
            document.form.obtain_drug_country_id.value = document.form.country_id.value;
            document.form.drug_authorization_country_id.value = document.form.country_id.value;
            document.form.product_id.value = -1;
            document.form.prod_lic_id.value = -1;
            document.form.product_name.value = result.product_name;
            document.form.generic_name.value = result.generic_name;
            if ((((document.form.product_name_alt.value == '') || (document.form.product_name_alt.value == null) || (document.form.product_name_alt.value == sTranslationReqd)) &&
                ((document.form.generic_name_alt.value == '') || (document.form.generic_name_alt.value == null) || (document.form.generic_name_alt.value == sTranslationReqd))) ||
                (bOverWrite)) {
/*                 document.form.product_name_alt.value = result.product_name;
                document.form.generic_name_alt.value = result.generic_name;
                document.form.prod_coded_alt.value = result.product_name; */
				document.form.product_name_alt.value = result.product_name_cn;
                document.form.generic_name_alt.value = result.generic_name_cn;
                document.form.prod_coded_alt.value = result.product_name_cn;
                document.form.drug_code_j.value = "";
            }
            document.form.co_drug_code.value = "";
            document.form.who_drug_code.value = result.drug_code;
            document.form.who_med_prod_id.value = result.med_prod_id;
            document.form.manufacturer_id.value = result.manufacturer;
            fn_SetFormulation("");
            document.form.concentration.value = "";
            document.form.conc_units_id.value = "";
            document.form.not_coded.value = "0";
            await fn_set_ingredients(result.ingredient_id, "", "", "");
            document.form.auth_type.value = "";
            document.form.auth_number.value = "";
            document.form.mkt_auth_holder_id.value = "";
            document.form.views_available.value = 7;
            if (result.drug_code.length > 0) {
                if (CENTRAL_CODING_INTERFACE > "0")
                    document.form.prod_code_status.value = "2";
                else
                    document.form.prod_code_status.value = "1";
            }
            else
                document.form.prod_code_status.value = "0";
            document.form.prod_coded.value = result.product_name;
        }

        await fn_NameProdChange();
        await fn_DrugView('unknown');
        return;
    }
}`;

const langIcon = `<span title="translate" data-pv="argus-plus-icon" style="font-size: 12px;cursor: pointer;position: absolute;right: 0;top: 0;z-index:1;">📝
</span>`;

const checkIframeLoaded = (iframe) => {
  const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
  return iframeDoc && iframeDoc.readyState === "complete";
};

async function waitArgusPage(urls = []) {
  const fm_MainFrame = await new Promise((resolve) => {
    const checkInterval = setInterval(() => {
      const fm_MainApp = document.querySelector("#fm_MainApp");
      if (!fm_MainApp) return;
      if (!checkIframeLoaded(fm_MainApp)) {
        return;
      }
      const fm_MainFrame =
        fm_MainApp.contentWindow.document.querySelector("#fm_MainFrame");
      if (!fm_MainFrame) return;
      clearInterval(checkInterval);
      resolve(fm_MainFrame);
    }, 1000);
  });

  return new Promise((resolve) => {
    fm_MainFrame.addEventListener("load", () => {
      const iframeWin = fm_MainFrame.contentWindow;
      if (iframeWin) {
        const currentUrl = iframeWin.location.href;
        if (urls.some((url) => currentUrl.includes(url))) {
          resolve(iframeWin);
        }
      }
    });
  });
}

 

async function drug_select() {
  const checkInterval = setInterval(() => {
    if (!window.fm_MainApp?.fm_MainFrame?.fn_DrugSelect) {
      return;
    }
    if (window.fm_MainApp?.fm_MainFrame?.fn_DrugSelect?.__overwrite === true) {
      return;
    }
    try {
      if (window.fm_MainApp?.fm_MainFrame?.fn_DrugSelect) {
        window.fm_MainApp.fm_MainFrame.eval(drug_select_code);
        window.fm_MainApp.fm_MainFrame.fn_DrugSelect.__overwrite = true;
      }
    } catch (e) {
      // 
    }
  },300);
  return () => {
    clearInterval(checkInterval);
  };
}

function isVisible(element) {
  return !!(element.offsetWidth || element.offsetHeight || element.getClientRects().length);
}

function getElementAbove(element, distance = 30) { 
  const rect = element.getBoundingClientRect();
  const x = rect.left + rect.width / 2;  // 取元素中心点的 x 坐标
  const y = rect.top - distance;  // 向上 30px 的 y 坐标
  
  return document.elementFromPoint(x, y);
}

async function langs_input() {
  const checkInterval = setInterval(() => {
    if (!window.fm_MainApp?.fm_MainFrame?.document) {
      return;
    }
    
    const win = window.fm_MainApp?.fm_MainFrame;
    win.document.querySelectorAll("textarea.textarea.jpn-font,input[type='text']:not([ondblclick])").forEach((input) => {
      const id = input.getAttribute("id");
      if(!id) return;
      if(!isVisible(input)) return;
      if (input.getAttribute('data-pv') == 'argus-plus') {
        return;
      }

      const input_alt = input.parentNode.querySelector(`#${id}_alt`) || win.document.querySelector(`#${id}_alt`);
      if (!input_alt || input_alt.getAttribute("type") !== 'hidden' ) {
        return;  
      }

      input.setAttribute('data-pv', 'argus-plus');   
      const cf = document.createRange().createContextualFragment(langIcon);
      const icon = cf.childNodes[0];
      
      input.parentNode.style.position = 'relative';
      input.parentNode.insertBefore(icon, input);

      const readonly = input.readOnly;

      icon.addEventListener('click', async (e) => {
        e.stopPropagation();
        e.preventDefault();
        
 
        const modalContainer = document.createElement('div');
        modalContainer.style.cssText = 'position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); display: flex; justify-content: center; align-items: center; z-index: 9999;';

        const modalContent = document.createElement('div');
        modalContent.style.cssText = 'background: white; padding: 20px; border-radius: 5px; width: 80vw; max-width: 90%;';
 
        const currentValue = input.value || '';
        const altValue = input_alt.value || ''; 

        const header = document.createElement('div');
        header.innerHTML = `<h3 style="margin-top: 0;">Edit Text</h3>`;
        modalContent.appendChild(header); 

        const form = document.createElement('form');
        form.innerHTML = `
          <div style="margin-bottom: 15px;display: flex; flex-direction: row;">
          <div style="flex:1;">
            <label style="display: block; margin-bottom: 5px;">Primary Language:</label>
            <textarea ${readonly? 'readonly': ''} style="width: 100%; padding: 8px; box-sizing: border-box;" rows="${currentValue.length > 100 ? 40 : 20}">${currentValue}</textarea>
          </div>
          <div style="flex:1;margin-left: 20px;">
            <label style="display: block; margin-bottom: 5px;">Alternate Language:</label>
            <textarea ${readonly? 'readonly': ''} style="width: 100%; padding: 8px; box-sizing: border-box;" rows="${currentValue.length > 100 ? 40 : 20}">${altValue}</textarea>
          </div>
          </div>
        `;
        modalContent.appendChild(form);
 
        const buttonContainer = document.createElement('div');
        buttonContainer.style.cssText = 'display: flex; justify-content: flex-end; gap: 10px;';

        const cancelButton = document.createElement('button');
        cancelButton.textContent = 'Cancel';
        cancelButton.style.cssText = 'padding: 8px 16px;';
        cancelButton.onclick = () => document.body.removeChild(modalContainer);

        const saveButton = document.createElement('button');
        saveButton.textContent = 'Save';
        saveButton.style.cssText = 'padding: 8px 16px; background: #4CAF50; color: white; border: none;';

        buttonContainer.appendChild(cancelButton);
        if(!readonly){
          buttonContainer.appendChild(saveButton);
        }
        modalContent.appendChild(buttonContainer);

        modalContainer.appendChild(modalContent);
        document.body.appendChild(modalContainer);
 
        const textareas = form.querySelectorAll('textarea');
        saveButton.onclick = () => {
          const primaryValue = textareas[0].value;
          const altValue = textareas[1].value;
           
          input.value = primaryValue;
          input_alt.value = altValue;
          input.dispatchEvent(new Event('change', { bubbles: true }));
          input_alt.dispatchEvent(new Event('change', { bubbles: true }));
            
          document.body.removeChild(modalContainer);
        };

      });  

    }); 
  },1000);
  return () => {
    // clearInterval(checkInterval);
  }; 
}

drug_select();
langs_input();
