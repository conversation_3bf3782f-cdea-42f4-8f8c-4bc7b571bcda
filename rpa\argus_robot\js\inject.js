function messageHandler(event) {

    console.info(event.data);

    if (event.data.type === 'INVOKE_PAGE_FUNCTION') {
        const args = event.data.args;
        fn_labtest_select(args[0], args[1], args[2]);
    }

    // 临时清除，依然数组中依然包含全部数量的元素，只是元素都是空的
    if (event.data.type === 'clearCache') {
        const contentWindow = window.parent;
        console.info(contentWindow.$dialog, '临时清除缓存开始')
        contentWindow.$dialog = null;
        console.info(contentWindow.$dialog, '临时清除缓存完成')

        console.info(contentWindow.$dialogs, '临时清除缓存开始')
        contentWindow.$dialogs = [];
        contentWindow.popCounter = 0;
        console.info(contentWindow.$dialogs, '临时清除缓存完成', contentWindow.popCounter)
    }

    // 永久清除，不保留$dialogs的任何数据
    if (event.data.type === 'forceClearCacheDialog') {
        const contentWindow = window.parent;
        console.info(contentWindow.$dialog, '永久清除缓存开始')
        contentWindow.$dialog = null;
        console.info(contentWindow.$dialog, '永久清除缓存完成')

        console.info(contentWindow.$dialogs, '永久清除缓存开始')
        contentWindow.$dialogs = [];
        contentWindow.popCounter = 0;
        console.info(contentWindow.$dialogs, '永久清除缓存完成', contentWindow.popCounter)

        // 清除性能相关的数据
        contentWindow.performance.clearMarks();
        contentWindow.performance.clearMeasures();
        contentWindow.performance.clearResourceTimings();
        console.log('刷新内存');

        // 建议进行垃圾回收
        if (contentWindow.gc) {
            console.info('强制执行垃圾回收完成')
            contentWindow.gc();
        }

        // 重置性能缓冲区
        contentWindow.performance.setResourceTimingBufferSize(250);
    }

    if (event.data.type === 'refreshCache') {
        
        console.log('收到刷新消息:');  // 添加调试日志
        try {
            // 直接在当前window环境中执行脚本
            const script = document.createElement('script');
            script.textContent = `
                // 设置全局变量
                window.lReloadArgus = 1;
                if(window.parent) {
                    window.parent.lReloadArgus = 1;
                }
                // 触发一个自定义事件，通知变量已更新
                window.dispatchEvent(new CustomEvent('lReloadArgusChanged'));
            `;
            (document.head || document.documentElement).appendChild(script);
            script.remove();
            
            console.log('已设置 lReloadArgus:', window.lReloadArgus);
        } catch (error) {
            console.error('设置 lReloadArgus 失败:', error);
        }
    }
}

// 向页面注入通信桥
window.addEventListener('message', function(event) {
    messageHandler(event);
    // if(event.data.type === 'INVOKE_PAGE_FUNCTION') messageHandler(event);
    // if(event.data.type === 'clearCacheDialog') {
    //     window.$dialog = null;
    //     window.$dialogs = [];
    // }
});
