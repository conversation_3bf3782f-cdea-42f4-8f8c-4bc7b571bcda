// 示例数据
let processedSource = [];
let processedTarget = [];

const target = [{
    "Rel_Hist_Table_0_pat_hist_rptd_lltname": "10014733(子宫内膜癌)",
    "Rel_Hist_Table_0_pat_hist_cont": "1",
    "Rel_Hist_Table_0_pat_hist_rptd": "子宫内膜癌",
    "Rel_Hist_Table_0_pat_hist_start": "YYYY/??/??",
    "Rel_Hist_Table_0_pat_hist_stop": "YYYY/??/??",
    "Rel_Hist_Table_0_pat_hist_notes": "",
    "TXT_Rel_Hist_Table_0_pat_hist_type": "当前疾病"
}, {
    "Rel_Hist_Table_1_pat_hist_rptd_lltname": "10029331(周围神经病)",
    "Rel_Hist_Table_1_pat_hist_cont": "1",
    "Rel_Hist_Table_1_pat_hist_rptd": "周围神经病",
    "Rel_Hist_Table_1_pat_hist_start": "2023/12/01",
    "Rel_Hist_Table_1_pat_hist_stop": "YYYY/??/??",
    "Rel_Hist_Table_1_pat_hist_notes": "",
    "TXT_Rel_Hist_Table_1_pat_hist_type": "当前疾病"
}, {
    "Rel_Hist_Table_2_pat_hist_rptd_lltname": "10089855(癌症痛)",
    "Rel_Hist_Table_2_pat_hist_cont": "1",
    "Rel_Hist_Table_2_pat_hist_rptd": "癌症痛",
    "Rel_Hist_Table_2_pat_hist_start": "2024/02/01",
    "Rel_Hist_Table_2_pat_hist_stop": "YYYY/??/??",
    "Rel_Hist_Table_2_pat_hist_notes": "",
    "TXT_Rel_Hist_Table_2_pat_hist_type": "当前疾病"
}, {
    "Rel_Hist_Table_3_pat_hist_rptd_lltname": "10020942(低白蛋白血症)",
    "Rel_Hist_Table_3_pat_hist_cont": "1",
    "Rel_Hist_Table_3_pat_hist_rptd": "低白蛋白血症",
    "Rel_Hist_Table_3_pat_hist_start": "2024/04/19",
    "Rel_Hist_Table_3_pat_hist_stop": "YYYY/??/??",
    "Rel_Hist_Table_3_pat_hist_notes": "",
    "TXT_Rel_Hist_Table_3_pat_hist_type": "当前疾病"
}, {
    "Rel_Hist_Table_4_pat_hist_rptd_lltname": "10001675(碱性磷酸酶增加)",
    "Rel_Hist_Table_4_pat_hist_cont": "1",
    "Rel_Hist_Table_4_pat_hist_rptd": "碱性磷酸酶增加",
    "Rel_Hist_Table_4_pat_hist_start": "2024/04/19",
    "Rel_Hist_Table_4_pat_hist_stop": "YYYY/??/??",
    "Rel_Hist_Table_4_pat_hist_notes": "",
    "TXT_Rel_Hist_Table_4_pat_hist_type": "当前疾病"
}, {
    "Rel_Hist_Table_5_pat_hist_rptd_lltname": "10002034(贫血)",
    "Rel_Hist_Table_5_pat_hist_cont": "1",
    "Rel_Hist_Table_5_pat_hist_rptd": "贫血111",
    "Rel_Hist_Table_5_pat_hist_start": "2024/04/19",
    "Rel_Hist_Table_5_pat_hist_stop": "YYYY/??/??",
    "Rel_Hist_Table_5_pat_hist_notes": "111",
    "TXT_Rel_Hist_Table_5_pat_hist_type": "当前疾病"
}];

// 指定用于唯一性判断的字段（去除索引后的字段名）
const specifiedFields = ['pat_hist_rptd', 'pat_hist_rptd_lltname', 'pat_hist_start', 'pat_hist_type'];

// 指定表格字段的顺序和显示名称
const fieldOrder = [
    {field: 'pat_hist_rptd_lltname', displayName: '编码(名称)'},
    {field: 'pat_hist_rptd', displayName: '报告疾病'},
    {field: 'pat_hist_start', displayName: '开始日期'},
    {field: 'pat_hist_stop', displayName: '结束日期'},
    {field: 'pat_hist_notes', displayName: '备注'},
    {field: 'pat_hist_type', displayName: '类型'},
    {field: 'pat_hist_cont', displayName: '是否持续'}
];

// 处理数据，将字段名中的索引去除，并保留映射关系
function processData(dataArray) {
    return dataArray?.map(item => {
        const newItem = {};
        const fieldMapping = {};
        for (let key in item) {
            let newKey = key.replace(/Rel_Hist_Table_\d+_/, '').replace(/TXT_/, '');
            newItem[newKey] = item[key];
            fieldMapping[newKey] = key; // 保留原始字段名
        }
        newItem._fieldMapping = fieldMapping; // 将映射关系保存在对象中
        return newItem;
    });
}

const dataTable = document.getElementById('dataTable');
const tableHeader = document.getElementById('tableHeader');
const selectAllCheckbox = document.getElementById('selectAll');
const batchStatusSelect = document.getElementById('batchStatus');
const finishButton = document.getElementById('finishButton');

let data = [];
let selectedRecords = new Set();
let lastCheckedIndex = null;
let dragSrcIndices = [];

// 获取合并按钮并添加事件监听器
const mergeButton = document.getElementById('mergeButton');
mergeButton.addEventListener('click', handleMerge);

// 辅助函数：清除辅助属性
function cleanItem(item) {
    const newItem = {...item};
    delete newItem._fieldMapping;
    delete newItem.status;
    delete newItem.changes;
    return newItem;
}

// 比较数据并标记状态
function compareData(processedSource, processedTarget) {
    const sourceMap = new Map();
    const targetMap = new Map();
    console.log('compareData', processedSource);
    
    processedSource.forEach(item => {
        const key = specifiedFields.map(field => item[field]).join('_');
        if (!sourceMap.has(key)) {
            sourceMap.set(key, []);
        }
        sourceMap.get(key).push(item);
    });

    processedTarget.forEach(item => {
        const key = specifiedFields.map(field => item[field]).join('_');
        if (!targetMap.has(key)) {
            targetMap.set(key, []);
        }
        targetMap.get(key).push(item);
    });

    const allKeys = new Set([...sourceMap.keys(), ...targetMap.keys()]);

    allKeys.forEach(key => {
        const sourceItems = sourceMap.get(key) || [];
        const targetItems = targetMap.get(key) || [];
        const maxLength = Math.max(sourceItems.length, targetItems.length);

        for (let i = 0; i < maxLength; i++) {
            const sourceItem = sourceItems[i];
            const targetItem = targetItems[i];

            if (sourceItem && targetItem) {
                const isSame = JSON.stringify(cleanItem(sourceItem)) === JSON.stringify(cleanItem(targetItem));
                if (isSame) {
                    data.push({...targetItem, status: 'matched'});
                } else {
                    data.push({...targetItem, status: 'modified', changes: getChanges(sourceItem, targetItem)});
                }
            } else if (targetItem && !sourceItem) {
                data.push({...targetItem, status: 'added'});
            } else if (sourceItem && !targetItem) {
                data.push({...sourceItem, status: 'deleted'});
            }
        }
    });
}

// 获取字段变化
function getChanges(oldItem, newItem) {
    const changes = {};
    const oldCleanItem = cleanItem(oldItem);
    const newCleanItem = cleanItem(newItem);

    for (let key in newCleanItem) {
        if (oldCleanItem[key] !== newCleanItem[key]) {
            changes[key] = true;
        }
    }
    return changes;
}

// 动态生成表头
function renderTableHeader() {
    tableHeader.innerHTML = '';

    // 添加选择框列
    const selectTh = document.createElement('th');
    selectTh.style.width = '30px'; // 缩小列宽
    tableHeader.appendChild(selectTh);

    // 动态添加字段列
    fieldOrder.forEach(fieldInfo => {
        const th = document.createElement('th');
        th.textContent = fieldInfo.displayName;
        tableHeader.appendChild(th);
    });

    // 添加链接操作列
    const linkTh = document.createElement('th');
    linkTh.textContent = '操作';
    tableHeader.appendChild(linkTh);
}

// 渲染表格（移除链接按钮列）
function renderTable() {
    const tbody = dataTable.getElementsByTagName('tbody')[0];
    tbody.innerHTML = '';
    data.forEach((item, index) => {
        const row = tbody.insertRow();
        row.setAttribute('data-index', `${index}`);
        row.setAttribute('draggable', 'true');

        // 设置行的类名
        if (selectedRecords.has(index)) {
            row.classList.add('selected');
        }
        row.classList.add(item.status);

        // 拖拽事件
        row.addEventListener('dragstart', dragStart);
        row.addEventListener('dragover', dragOver);
        row.addEventListener('drop', drop);
        row.addEventListener('dragend', dragEnd);

        // 使用自定义复选框显示状态
        const checkboxCell = row.insertCell();
        const checkboxContainer = document.createElement('label');
        checkboxContainer.className = `custom-checkbox ${item.status}`;

        const checkbox = document.createElement('input');
        checkbox.type = 'checkbox';
        checkbox.checked = selectedRecords.has(index);
        checkbox.addEventListener('click', (e) => {
            if (e.shiftKey && lastCheckedIndex !== null) {
                let start = Math.min(lastCheckedIndex, index);
                let end = Math.max(lastCheckedIndex, index);

                for (let i = start; i <= end; i++) {
                    if (e.target.checked) {
                        selectedRecords.add(i);
                    } else {
                        selectedRecords.delete(i);
                    }
                }
                renderTable();
            } else {
                if (e.target.checked) {
                    selectedRecords.add(index);
                } else {
                    selectedRecords.delete(index);
                }
                lastCheckedIndex = index;
                renderTable();
            }
        });

        const checkmark = document.createElement('span');
        checkboxContainer.appendChild(checkbox);
        checkboxContainer.appendChild(checkmark);
        checkboxCell.appendChild(checkboxContainer);

        // 动态生成单元格
        fieldOrder.forEach(fieldInfo => {
            const field = fieldInfo.field;
            const cell = row.insertCell();
            cell.textContent = item[field] !== undefined ? item[field] : '';

            // 高亮显示变化的字段
            if (item.status === 'modified' && item.changes && item.changes[field]) {
                cell.classList.add('highlight');
            }
        });
    });
}

// 处理合并操作
function handleMerge() {
    const selectedIndexes = Array.from(selectedRecords);
    if (selectedIndexes.length !== 2) {
        alert('请选中两条记录进行合并。');
        return;
    }

    const item1 = data[selectedIndexes[0]];
    const item2 = data[selectedIndexes[1]];

    showMergeDialog(item1, item2, selectedIndexes[0], selectedIndexes[1]);
}

// 显示合并对话框
function showMergeDialog(item1, item2, index1, index2) {
    // 创建对话框元素
    const dialogOverlay = document.createElement('div');
    dialogOverlay.style.position = 'fixed';
    dialogOverlay.style.top = '0';
    dialogOverlay.style.left = '0';
    dialogOverlay.style.width = '100%';
    dialogOverlay.style.height = '100%';
    dialogOverlay.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
    dialogOverlay.style.display = 'flex';
    dialogOverlay.style.alignItems = 'center';
    dialogOverlay.style.justifyContent = 'center';
    dialogOverlay.style.zIndex = '9999';

    const dialogBox = document.createElement('div');
    dialogBox.style.backgroundColor = '#fff';
    dialogBox.style.padding = '20px';
    dialogBox.style.borderRadius = '8px';
    dialogBox.style.width = '400px';
    dialogBox.style.maxHeight = '80%';
    dialogBox.style.overflowY = 'auto';

    const dialogContent = document.createElement('div');
    fieldOrder.forEach(fieldInfo => {
        const field = fieldInfo.field;

        const label = document.createElement('label');
        label.style.display = 'block';
        label.style.marginBottom = '10px';

        const checkbox = document.createElement('input');
        checkbox.type = 'checkbox';
        checkbox.style.marginRight = '10px';
        checkbox.checked = false;

        label.appendChild(checkbox);
        label.appendChild(document.createTextNode(fieldInfo.displayName + ': '));

        const item1Value = document.createElement('span');
        item1Value.textContent = `[记录1]: ${item1[field]}`;
        label.appendChild(item1Value);

        const item2Value = document.createElement('span');
        item2Value.style.marginLeft = '10px';
        item2Value.textContent = `[记录2]: ${item2[field]}`;
        label.appendChild(item2Value);

        dialogContent.appendChild(label);
    });

    dialogBox.appendChild(dialogContent);

    const buttonContainer = document.createElement('div');
    buttonContainer.style.textAlign = 'right';
    buttonContainer.style.marginTop = '20px';

    const mergeButton = document.createElement('button');
    mergeButton.textContent = '合并';
    mergeButton.style.marginRight = '10px';
    mergeButton.addEventListener('click', () => {
        mergeData(item1, item2, index1, index2, dialogContent);
        document.body.removeChild(dialogOverlay);
    });

    const cancelButton = document.createElement('button');
    cancelButton.textContent = '取消';
    cancelButton.addEventListener('click', () => {
        document.body.removeChild(dialogOverlay);
    });

    buttonContainer.appendChild(mergeButton);
    buttonContainer.appendChild(cancelButton);
    dialogBox.appendChild(buttonContainer);

    dialogOverlay.appendChild(dialogBox);
    document.body.appendChild(dialogOverlay);
}

// 合并数据
function mergeData(item1, item2, index1, index2, dialogContent) {
    const mergedItem = {...item1};
    const checkboxes = dialogContent.querySelectorAll('input[type="checkbox"]');

    fieldOrder.forEach((fieldInfo, i) => {
        if (checkboxes[i].checked) {
            mergedItem[fieldInfo.field] = item2[fieldInfo.field];
        }
    });

    mergedItem.status = 'modified';
    mergedItem.changes = getChanges(item1, mergedItem);

    // 更新数据
    data[index1] = mergedItem;
    data.splice(index2, 1);

    // 清空选择记录
    selectedRecords.clear();

    renderTable();
}

function dragStart(e) {
    const index = parseInt(e.target.getAttribute('data-index'));
    if (!selectedRecords.has(index)) {
        // 如果当前行未被选中，则只拖拽当前行
        selectedRecords.clear();
        selectedRecords.add(index);
        renderTable();
    }

    dragSrcIndices = Array.from(selectedRecords);
    e.dataTransfer.effectAllowed = 'move';

    // 添加拖拽样式
    dragSrcIndices.forEach(i => {
        const row = dataTable.querySelector(`tr[data-index='${i}']`);
        if (row) {
            row.classList.add('dragging');
        }
    });
}

function dragOver(e) {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
}

function drop(e) {
    e.preventDefault();
    const targetRow = e.target.closest('tr');
    if (!targetRow) return;
    const targetIndex = parseInt(targetRow.getAttribute('data-index'));
    if (dragSrcIndices.length && targetIndex !== null && !dragSrcIndices.includes(targetIndex)) {
        const draggedItems = dragSrcIndices.map(i => data[i]);
        data = data.filter((item, index) => !dragSrcIndices.includes(index));
        let insertIndex = targetIndex;
        // 如果拖拽到后面的行，需要调整插入索引
        if (targetIndex > Math.min(...dragSrcIndices)) {
            insertIndex -= dragSrcIndices.filter(i => i < targetIndex).length;
        }
        data.splice(insertIndex, 0, ...draggedItems);
        // 更新selectedRecords
        selectedRecords.clear();
        draggedItems.forEach((item, i) => {
            selectedRecords.add(insertIndex + i);
        });
        renderTable();
    }
}

function dragEnd() {
    dragSrcIndices.forEach(i => {
        const row = dataTable.querySelector(`tr[data-index='${i}']`);
        if (row) {
            row.classList.remove('dragging');
        }
    });
    dragSrcIndices = [];
}

// 全选/取消全选
selectAllCheckbox.addEventListener('change', (e) => {
    if (e.target.checked) {
        data.forEach((item, index) => {
            selectedRecords.add(index);
        });
    } else {
        selectedRecords.clear();
    }
    renderTable();
});

// 批量修改状态
batchStatusSelect.addEventListener('change', (e) => {
    const newStatus = e.target.value;
    if (newStatus) {
        data.forEach((item, index) => {
            if (selectedRecords.has(index)) {
                item.status = newStatus;
                if (newStatus !== 'modified') {
                    delete item.changes;
                }
            }
        });
        renderTable();
        batchStatusSelect.value = '';
    }
});

// 完成按钮
finishButton.addEventListener('click', function(){
    finishEditing(processedSource)
});

function finishEditing(processedSource) {
    // 1. 过滤掉已删除的记录，生成合并后的最终数据
    const filteredData = data.filter(item => item.status !== 'deleted');

    const finalData = filteredData.map((item, index) => {
        const newItem = {};
        const originalFieldMapping = item._fieldMapping || {};

        for (let field in item) {
            if (field !== 'status' && field !== 'changes' && field !== '_fieldMapping') {
                const originalFieldName = originalFieldMapping[field];
                if (originalFieldName) {
                    // 更新索引
                    const newFieldName = originalFieldName.replace(/Rel_Hist_Table_\d+_/, `Rel_Hist_Table_${index}_`);
                    newItem[newFieldName] = item[field];
                } else {
                    // 处理TXT_前缀的字段
                    const newFieldName = `TXT_Rel_Hist_Table_${index}_${field}`;
                    newItem[newFieldName] = item[field];
                }
            }
        }
        return newItem;
    });

    // 2. 记录变更操作
    const changeLog = [];
    const sourceIndexes = new Set();

    processedSource.forEach((item, originalIndex) => {
        // 在最终数据中找出相同的项
        const foundIndex = finalData.findIndex(finalItem => {
            return JSON.stringify(cleanItem(finalItem)) === JSON.stringify(cleanItem(item));
        });

        if (foundIndex === -1) {
            // 如果在最终数据中找不到，说明它被删除了
            changeLog.push({ action: 'delete', originalIndex });
        } else {
            // 如果找到了，记录原始索引
            sourceIndexes.add(foundIndex);
        }
    });

    data.forEach((item, index) => {
        if (item.status === 'added') {
            // 记录新增
            changeLog.push({ action: 'add', index });
        } else if (item.status === 'modified') {
            // 记录修改
            const originalIndex = processedSource.findIndex(sourceItem => JSON.stringify(cleanItem(sourceItem)) === JSON.stringify(cleanItem(item)));
            if (originalIndex !== -1) {
                changeLog.push({ action: 'modify', originalIndex, newIndex: index });
            }
        } else if (item.status === 'matched') {
            // 检查是否移动了行
            const originalIndex = processedSource.findIndex(sourceItem => JSON.stringify(cleanItem(sourceItem)) === JSON.stringify(cleanItem(item)));
            if (originalIndex !== index) {
                changeLog.push({ action: 'move', originalIndex, newIndex: index });
            }
        }
    });

    // 检查未被使用到的行
    finalData.forEach((item, index) => {
        if (!sourceIndexes.has(index)) {
            changeLog.push({ action: 'add', index });
        }
    });

    // 3. 输出两份数据
    const outputData = {
        finalData,
        changeLog
    };

    // const jsonData = JSON.stringify(outputData, null, 2);
    // 在新窗口中显示JSON数据
    // const newWindow = window.open();
    // newWindow.document.write('<pre>' + jsonData + '</pre>');

    // 完成后发送信息给background script
    window.parent.postMessage({
        action: 'MergeCompleted',
        data: finalData
    }, '*') 
}

window.addEventListener('message', function(event) {
    // 处理接收到的数据
    const receivedData = event.data;
    if(receivedData.type === 'MedicalHistoryList'){
        console.log('病史数据列表', receivedData.data);
        
        processedSource = processData(receivedData?.data?.oldData) || [];
        processedTarget = processData(receivedData?.data?.newData || target) || [];
        // 初始化
        compareData(processedSource, processedTarget);
        renderTableHeader();
        renderTable();

    }
});
