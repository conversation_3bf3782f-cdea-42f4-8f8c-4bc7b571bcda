/**
 * Message组件 - 替代alert的现代化消息提示组件
 * 
 * 使用方法:
 * Message.show('消息内容');
 * Message.success('成功消息');
 * Message.error('错误消息');
 * Message.warning('警告消息');
 * Message.info('信息消息');
 * Message.loading('加载中...');
 */

(function(global) {
    'use strict';

    // 默认配置
    const DEFAULT_CONFIG = {
        duration: 3000,        // 自动关闭时间，0表示不自动关闭
        position: 'top',       // 显示位置
        maxCount: 5,           // 最大显示数量
        offset: 20,            // 偏移量
        zIndex: 9999,          // 层级
        theme: 'default'       // 主题
    };

    // 消息类型配置
    const MESSAGE_TYPES = {
        success: { icon: '✓', className: 'message-success' },
        error: { icon: '✗', className: 'message-error' },
        warning: { icon: '⚠', className: 'message-warning' },
        info: { icon: 'ℹ', className: 'message-info' },
        loading: { icon: '◐', className: 'message-loading' }
    };

    /**
     * 消息项类
     */
    class MessageItem {
        constructor(options) {
            this.options = Object.assign({}, DEFAULT_CONFIG, options);
            this.id = Date.now() + Math.random();
            this.element = null;
            this.timer = null;
            this.visible = false;
        }

        /**
         * 创建消息元素
         */
        createElement() {
            const element = document.createElement('div');
            element.className = this.getClassName();
            element.setAttribute('data-message-id', this.id);
            
            const typeConfig = MESSAGE_TYPES[this.options.type] || {};
            
            element.innerHTML = `
                <div class="message-content">
                    ${typeConfig.icon ? `<span class="message-icon ${this.options.type === 'loading' ? 'message-loading-icon' : ''}">${typeConfig.icon}</span>` : ''}
                    <span class="message-text">${this.options.html ? this.options.content : this.escapeHtml(this.options.content)}</span>
                    ${this.options.closable ? '<span class="message-close">×</span>' : ''}
                </div>
            `;

            // 绑定事件
            this.bindEvents(element);
            
            return element;
        }

        /**
         * 获取CSS类名
         */
        getClassName() {
            const classes = ['message-item'];
            
            if (this.options.type) {
                const typeConfig = MESSAGE_TYPES[this.options.type];
                if (typeConfig) {
                    classes.push(typeConfig.className);
                }
            }
            
            return classes.join(' ');
        }

        /**
         * 转义HTML
         */
        escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        /**
         * 绑定事件
         */
        bindEvents(element) {
            // 关闭按钮事件
            const closeBtn = element.querySelector('.message-close');
            if (closeBtn) {
                closeBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    this.close();
                });
            }

            // 点击事件
            if (this.options.onClick) {
                element.addEventListener('click', this.options.onClick);
            }
        }

        /**
         * 显示消息
         */
        show() {
            if (this.visible) return this;

            this.element = this.createElement();            
            const container = messageManager.getContainer();
            container.appendChild(this.element);

            // 触发重绘，确保动画效果
            this.element.offsetHeight;
            
            // 添加显示类
            this.element.classList.add('message-show');
            this.visible = true;

            // 设置自动关闭
            if (this.options.duration > 0) {
                this.timer = setTimeout(() => {
                    this.close();
                }, this.options.duration);
            }

            return this;
        }

        /**
         * 关闭消息
         */
        close() {
            if (!this.visible || !this.element) return this;

            // 清除定时器
            if (this.timer) {
                clearTimeout(this.timer);
                this.timer = null;
            }

            // 添加关闭动画
            this.element.classList.add('message-hide');
            
            // 动画结束后移除元素
            setTimeout(() => {
                this.destroy();
            }, 300);

            // 调用关闭回调
            if (this.options.onClose) {
                this.options.onClose();
            }

            return this;
        }

        /**
         * 销毁消息
         */
        destroy() {
            if (this.element && this.element.parentNode) {
                this.element.parentNode.removeChild(this.element);
            }
            this.element = null;
            this.visible = false;
            
            // 从管理器中移除
            messageManager.remove(this);
        }
    }

    /**
     * 消息管理器
     */
    class MessageManager {
        constructor() {
            this.config = Object.assign({}, DEFAULT_CONFIG);
            this.messages = [];
            this.container = null;
        }

        /**
         * 获取或创建容器
         */
        getContainer() {
            if (!this.container || !document.body.contains(this.container)) {
                this.container = this.createContainer();
            }
            return this.container;
        }

        /**
         * 创建容器
         */
        createContainer() {
            const container = document.createElement('div');
            container.className = `message-container message-${this.config.position}`;
            container.style.cssText = `
                position: fixed;
                z-index: ${this.config.zIndex};
                pointer-events: none;
            `;
            
            this.updateContainerPosition(container);
            document.body.appendChild(container);
            return container;
        }

        /**
         * 更新容器位置
         */
        updateContainerPosition(container) {
            const offset = this.config.offset + 'px';
            
            // 重置所有位置样式
            container.style.top = 'auto';
            container.style.bottom = 'auto';
            container.style.left = 'auto';
            container.style.right = 'auto';
            container.style.transform = 'none';

            switch (this.config.position) {
                case 'top':
                    container.style.top = offset;
                    container.style.left = '50%';
                    container.style.transform = 'translateX(-50%)';
                    break;
                case 'top-left':
                    container.style.top = offset;
                    container.style.left = offset;
                    break;
                case 'top-right':
                    container.style.top = offset;
                    container.style.right = offset;
                    break;
                case 'center':
                    container.style.top = '50%';
                    container.style.left = '50%';
                    container.style.transform = 'translate(-50%, -50%)';
                    break;
                case 'bottom':
                    container.style.bottom = offset;
                    container.style.left = '50%';
                    container.style.transform = 'translateX(-50%)';
                    break;
                case 'bottom-left':
                    container.style.bottom = offset;
                    container.style.left = offset;
                    break;
                case 'bottom-right':
                    container.style.bottom = offset;
                    container.style.right = offset;
                    break;
            }
        }

        /**
         * 显示消息
         */
        show(options) {
            // 处理字符串参数
            if (typeof options === 'string') {
                options = { content: options };
            }

            // 合并配置
            options = Object.assign({}, this.config, options);

            // 检查最大数量限制
            if (this.messages.length >= this.config.maxCount) {
                // 移除最老的消息
                const oldestMessage = this.messages[0];
                if (oldestMessage) {
                    oldestMessage.close();
                }
            }

            // 创建并显示消息
            const message = new MessageItem(options);
            this.messages.push(message);
            message.show();

            return message;
        }

        /**
         * 显示成功消息
         */
        success(content, options = {}) {
            return this.show(Object.assign({ content, type: 'success' }, options));
        }

        /**
         * 显示错误消息
         */
        error(content, options = {}) {
            return this.show(Object.assign({ content, type: 'error' }, options));
        }

        /**
         * 显示警告消息
         */
        warning(content, options = {}) {
            return this.show(Object.assign({ content, type: 'warning' }, options));
        }

        /**
         * 显示信息消息
         */
        info(content, options = {}) {
            return this.show(Object.assign({ content, type: 'info' }, options));
        }

        /**
         * 显示加载消息
         */
        loading(content, options = {}) {
            return this.show(Object.assign({ 
                content, 
                type: 'loading', 
                duration: 0,  // 加载消息默认不自动关闭
                closable: true 
            }, options));
        }

        /**
         * 清除所有消息
         */
        clear() {
            this.messages.slice().forEach(message => {
                message.close();
            });
        }

        /**
         * 移除消息
         */
        remove(message) {
            const index = this.messages.indexOf(message);
            if (index > -1) {
                this.messages.splice(index, 1);
            }
        }

        /**
         * 更新配置
         */
        setConfig(newConfig) {
            Object.assign(this.config, newConfig);
            
            // 更新容器位置
            if (this.container) {
                this.container.className = `message-container message-${this.config.position}`;
                this.updateContainerPosition(this.container);
                this.container.style.zIndex = this.config.zIndex;
            }
        }
    }

    // 创建全局实例
    const messageManager = new MessageManager(); 

    // 导出API
    const Message = {
        show: (options) => messageManager.show(options),
        success: (content, options) => messageManager.success(content, options),
        error: (content, options) => messageManager.error(content, options),
        warning: (content, options) => messageManager.warning(content, options),
        info: (content, options) => messageManager.info(content, options),
        loading: (content, options) => messageManager.loading(content, options),
        clear: () => messageManager.clear(),
        config: (config) => messageManager.setConfig(config)
    };

    // 兼容不同的模块系统
    if (typeof module !== 'undefined' && module.exports) {
        module.exports = Message;
    // eslint-disable-next-line no-undef
    } else if (typeof define === 'function' && define.amd) {
        // eslint-disable-next-line no-undef
        define([], function() { return Message; });
    } else {
        global.Message = Message;
    }

})(typeof window !== 'undefined' ? window : this);